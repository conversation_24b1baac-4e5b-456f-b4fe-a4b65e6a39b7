[package]
name = "tulip-media"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "High-performance Jellyfin-compatible media server for ARM64"
license = "MIT"
repository = "https://github.com/yourusername/tulip-media"

[dependencies]
# Async runtime - Tokio with full features for comprehensive async support
tokio = { version = "1.35", features = ["full"] }

# Command line parsing - Clap v4 with derive macros for easy CLI definition
clap = { version = "4.4", features = ["derive"] }

# Logging - Tracing for structured, async-aware logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling - Anyhow for easy error propagation
anyhow = "1.0"

# Serialization - Serde for configuration and API data
serde = { version = "1.0", features = ["derive"] }

# Configuration management
config = "0.14.0"           # Hierarchical configuration management
toml = "0.8.8"             # TOML parsing and serialization
dirs = "5.0"               # Standard directory locations
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"         # Global static initialization

# Development dependencies for hot-reloading
notify = { version = "6.1", optional = true }

[profile.release]
# Optimize for ARM64 performance
lto = true              # Link-time optimization for smaller, faster binaries
codegen-units = 1       # Single codegen unit for better optimization
panic = "abort"         # Abort on panic for smaller binary size
strip = true            # Strip debug symbols from release builds
opt-level = 3           # Maximum optimization level

[profile.release.package."*"]
opt-level = 3           # Optimize all dependencies at maximum level

# ARM64-specific optimizations
[target.'cfg(target_arch = "aarch64")'.dependencies]
# We'll add ARM64-specific crates in later chapters

[dev-dependencies]
# Testing utilities
tempfile = "3.8"        # Temporary files for testing

[features]
default = []
hot-reload = ["notify"]    # Enable hot-reloading for development

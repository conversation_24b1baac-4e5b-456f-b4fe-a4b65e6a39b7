use clap::Parser;
use std::path::PathBuf;

/// Tulip Media Server - High-performance Jellyfin-compatible media server
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    pub config: PathBuf,

    /// Data directory for database and cache
    #[arg(short, long, default_value = "./data")]
    pub data_dir: PathBuf,

    /// Media library directories (can be specified multiple times)
    #[arg(short, long)]
    pub media_dirs: Vec<PathBuf>,

    /// Server bind address
    #[arg(long, default_value = "0.0.0.0")]
    pub bind_address: String,

    /// Server port
    #[arg(short, long, default_value = "8096")]
    pub port: u16,

    /// Enable debug logging
    #[arg(long)]
    pub debug: bool,
}

impl Cli {
    /// Parse command line arguments
    pub fn parse_args() -> Self {
        Self::parse()
    }

    /// Validate the parsed arguments
    pub fn validate(&self) -> anyhow::Result<()> {
        // Validate port range
        if self.port == 0 {
            anyhow::bail!("Port must be greater than 0");
        }

        // Validate data directory can be created
        if let Some(parent) = self.data_dir.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| anyhow::anyhow!("Cannot create data directory: {}", e))?;
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_defaults() {
        let cli = Cli::parse_from(&["tulip-media"]);
        assert_eq!(cli.config, PathBuf::from("config.toml"));
        assert_eq!(cli.data_dir, PathBuf::from("./data"));
        assert_eq!(cli.bind_address, "0.0.0.0");
        assert_eq!(cli.port, 8096);
        assert!(!cli.debug);
    }

    #[test]
    fn test_cli_custom_args() {
        let cli = Cli::parse_from(&[
            "tulip-media",
            "--config", "custom.toml",
            "--port", "9000",
            "--debug"
        ]);
        assert_eq!(cli.config, PathBuf::from("custom.toml"));
        assert_eq!(cli.port, 9000);
        assert!(cli.debug);
    }

    #[test]
    fn test_validation_invalid_port() {
        let cli = Cli::parse_from(&["tulip-media", "--port", "0"]);
        assert!(cli.validate().is_err());
    }

    #[test]
    fn test_validation_valid_args() {
        let cli = Cli::parse_from(&["tulip-media", "--port", "8080"]);
        assert!(cli.validate().is_ok());
    }
}

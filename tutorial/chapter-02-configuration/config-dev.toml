# Development Configuration
# Inherits from config.toml with development-specific overrides

[server]
port = 8097  # Different port for development
server_name = "Tulip Media Server (Development)"

[media]
# Use test media for development
library_paths = [
    "./test-media/movies",
    "./test-media/tv",
    "./test-media/music"
]
scan_interval = 60  # Scan every minute for development

[performance]
cache_size_mb = 128  # Smaller cache for development

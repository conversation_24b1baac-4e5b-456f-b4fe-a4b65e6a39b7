use std::process::Command;
use tempfile::TempDir;

#[test]
fn test_default_configuration() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--help"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("--config"));
    assert!(stdout.contains("Configuration file path"));
}

#[test]
fn test_custom_configuration_file() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("custom.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create custom configuration
    let config_content = r#"
[server]
port = 9001
server_name = "Test Server"

[performance]
cache_size_mb = 64
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9001"));
    assert!(stdout.contains("Test Server"));
}

#[test]
fn test_environment_variable_override() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .env("TULIP_SERVER_PORT", "9002")
        .env("TULIP_SERVER_SERVER_NAME", "Env Test Server")
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9002"));
}

#[test]
fn test_cli_override_priority() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create config file with port 9003
    let config_content = r#"
[server]
port = 9003
"#;
    std::fs::write(&config_path, config_content).unwrap();
    
    // CLI should override config file
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--port", "9004",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9004"));
}

#[test]
fn test_media_directory_configuration() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    let media_dir1 = temp_dir.path().join("movies");
    let media_dir2 = temp_dir.path().join("tv");
    
    // Create media directories
    std::fs::create_dir_all(&media_dir1).unwrap();
    std::fs::create_dir_all(&media_dir2).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--media-dirs", media_dir1.to_str().unwrap(),
            "--media-dirs", media_dir2.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Media libraries: 2 configured"));
}

#[test]
fn test_development_configuration() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", "config-dev.toml",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:8097"));
    assert!(stdout.contains("Development"));
}

#[test]
fn test_arm64_optimizations() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    
    // Check that ARM64 optimizations are mentioned
    if cfg!(target_arch = "aarch64") {
        assert!(stdout.contains("ARM64 optimizations: enabled"));
        assert!(stdout.contains("ARM64 optimized"));
    }
}

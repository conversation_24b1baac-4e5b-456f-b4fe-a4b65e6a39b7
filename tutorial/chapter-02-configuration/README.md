# Chapter 2: Configuration Management

**Duration**: ~45 minutes  
**Difficulty**: Beginner  
**Prerequisites**: Chapter 1 completed, basic understanding of TOML format

## 🎯 Learning Objectives

By the end of this chapter, you will:
- Implement a hierarchical configuration system with TOML, CLI, and environment variables
- Understand configuration precedence and validation patterns
- Create development vs production configuration profiles
- Implement hot-reloading for development environments
- Handle sensitive configuration data securely
- Optimize configuration loading for ARM64 devices

## 📋 What We're Building

In this chapter, we'll create a robust configuration management system:
- TOML configuration file parsing with validation
- CLI argument override capabilities
- Environment variable integration
- Configuration hot-reloading for development
- Secure handling of sensitive data (JWT secrets, API keys)
- ARM64-optimized default values

## 🏗 Configuration Architecture

```
Configuration Priority (highest to lowest):
1. CLI Arguments          (--port 9000)
2. Environment Variables  (TULIP_PORT=9000)
3. Configuration File     (config.toml)
4. Default Values         (built-in defaults)
```

## 🚀 Step-by-Step Implementation

### Step 1: Update Dependencies

First, let's add the necessary dependencies to `Cargo.toml`:

```toml
[dependencies]
# Previous dependencies...
tokio = { version = "1.35", features = ["full"] }
clap = { version = "4.4", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }

# New configuration dependencies
config = "0.14.0"           # Hierarchical configuration management
toml = "0.8.8"             # TOML parsing and serialization
dirs = "5.0"               # Standard directory locations
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"         # Global static initialization

# Development dependencies for hot-reloading
notify = { version = "6.1", optional = true }

[features]
default = []
hot-reload = ["notify"]    # Enable hot-reloading for development
```

### Step 2: Create Comprehensive Configuration Structure

Replace `src/config.rs` with a complete configuration system:

```rust
//! Configuration management for Tulip Media Server
//!
//! This module provides hierarchical configuration loading from multiple sources:
//! 1. CLI arguments (highest priority)
//! 2. Environment variables
//! 3. Configuration files (TOML)
//! 4. Default values (lowest priority)

use anyhow::{Context, Result};
use config::{Config as ConfigBuilder, Environment, File};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use tracing::{debug, info, warn};

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub media: MediaConfig,
    pub streaming: StreamingConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub bind_address: String,
    pub port: u16,
    pub server_name: String,
    pub enable_https: bool,
    pub cert_path: Option<PathBuf>,
    pub key_path: Option<PathBuf>,
}

/// Database configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub path: PathBuf,
    pub max_connections: u32,
    pub connection_timeout: u64,
}

/// Media library configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MediaConfig {
    pub library_paths: Vec<PathBuf>,
    pub scan_interval: u64,
    pub thumbnail_cache_size: u64,
    pub metadata_cache_size: u64,
    pub supported_video_formats: Vec<String>,
    pub supported_audio_formats: Vec<String>,
    pub supported_image_formats: Vec<String>,
}

/// Streaming configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingConfig {
    pub enable_transcoding: bool,
    pub max_concurrent_streams: u32,
    pub hardware_acceleration: bool,
    pub video_codecs: Vec<String>,
    pub audio_codecs: Vec<String>,
    pub max_bitrate: u64,
    pub segment_duration: u32,
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub jwt_secret: Option<String>,
    pub session_timeout: u64,
    pub max_login_attempts: u32,
    pub enable_api_keys: bool,
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub worker_threads: Option<usize>,
    pub max_blocking_threads: usize,
    pub enable_compression: bool,
    pub cache_size_mb: u64,
    pub arm64_optimizations: bool,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            database: DatabaseConfig::default(),
            media: MediaConfig::default(),
            streaming: StreamingConfig::default(),
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            bind_address: "0.0.0.0".to_string(),
            port: 8096,
            server_name: "Tulip Media Server".to_string(),
            enable_https: false,
            cert_path: None,
            key_path: None,
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            path: PathBuf::from("./data/tulip.db"),
            max_connections: 10,
            connection_timeout: 30,
        }
    }
}

impl Default for MediaConfig {
    fn default() -> Self {
        Self {
            library_paths: vec![
                PathBuf::from("/media/movies"),
                PathBuf::from("/media/tv"),
                PathBuf::from("/media/music"),
            ],
            scan_interval: 3600, // 1 hour
            thumbnail_cache_size: 524_288_000, // 500MB
            metadata_cache_size: 104_857_600,  // 100MB
            supported_video_formats: vec![
                "mp4".to_string(), "mkv".to_string(), "avi".to_string(),
                "mov".to_string(), "wmv".to_string(), "flv".to_string(),
                "webm".to_string(), "m4v".to_string(),
            ],
            supported_audio_formats: vec![
                "mp3".to_string(), "flac".to_string(), "aac".to_string(),
                "ogg".to_string(), "wav".to_string(), "m4a".to_string(),
            ],
            supported_image_formats: vec![
                "jpg".to_string(), "jpeg".to_string(), "png".to_string(),
                "webp".to_string(), "bmp".to_string(),
            ],
        }
    }
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            enable_transcoding: true,
            max_concurrent_streams: if cfg!(target_arch = "aarch64") { 4 } else { 8 },
            hardware_acceleration: cfg!(target_arch = "aarch64"),
            video_codecs: vec!["h264".to_string(), "h265".to_string()],
            audio_codecs: vec!["aac".to_string(), "mp3".to_string()],
            max_bitrate: 20_000_000, // 20 Mbps
            segment_duration: 6,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            jwt_secret: None, // Will be auto-generated if not provided
            session_timeout: 86400, // 24 hours
            max_login_attempts: 5,
            enable_api_keys: true,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        let cpu_count = std::thread::available_parallelism()
            .map(|n| n.get())
            .unwrap_or(1);
            
        Self {
            worker_threads: None, // Auto-detect
            max_blocking_threads: if cfg!(target_arch = "aarch64") { 4 } else { 8 },
            enable_compression: true,
            cache_size_mb: if cfg!(target_arch = "aarch64") { 256 } else { 512 },
            arm64_optimizations: cfg!(target_arch = "aarch64"),
        }
    }
}

/// Configuration loader with hierarchical loading support
pub struct ConfigLoader {
    config_path: Option<PathBuf>,
}

impl ConfigLoader {
    pub fn new() -> Self {
        Self { config_path: None }
    }

    pub fn with_config_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.config_path = Some(path.as_ref().to_path_buf());
        self
    }

    /// Load configuration from all sources with proper precedence
    pub fn load(&self) -> Result<Config> {
        let mut builder = ConfigBuilder::builder();

        // 1. Start with default values
        let default_config = Config::default();
        builder = builder.add_source(config::Config::try_from(&default_config)?);

        // 2. Add configuration file if it exists
        if let Some(config_path) = &self.config_path {
            if config_path.exists() {
                info!("Loading configuration from: {:?}", config_path);
                builder = builder.add_source(File::from(config_path.clone()));
            } else {
                warn!("Configuration file not found: {:?}", config_path);
            }
        }

        // 3. Add environment variables with TULIP_ prefix
        builder = builder.add_source(
            Environment::with_prefix("TULIP")
                .separator("_")
                .try_parsing(true),
        );

        // 4. Build and deserialize configuration
        let config = builder
            .build()
            .context("Failed to build configuration")?
            .try_deserialize::<Config>()
            .context("Failed to deserialize configuration")?;

        // 5. Validate configuration
        self.validate_config(&config)?;

        debug!("Configuration loaded successfully");
        Ok(config)
    }

    /// Validate configuration values
    fn validate_config(&self, config: &Config) -> Result<()> {
        // Validate server configuration
        if config.server.port == 0 {
            anyhow::bail!("Server port must be greater than 0");
        }

        if config.server.port < 1024 && !cfg!(test) {
            warn!("Using privileged port {}, ensure proper permissions", config.server.port);
        }

        // Validate database configuration
        if config.database.max_connections == 0 {
            anyhow::bail!("Database max_connections must be greater than 0");
        }

        // Validate media configuration
        if config.media.library_paths.is_empty() {
            warn!("No media library paths configured");
        }

        // Validate streaming configuration
        if config.streaming.max_concurrent_streams == 0 {
            anyhow::bail!("max_concurrent_streams must be greater than 0");
        }

        // Validate performance configuration
        if config.performance.cache_size_mb == 0 {
            warn!("Cache size is set to 0, performance may be impacted");
        }

        Ok(())
    }
}

/// Apply CLI overrides to configuration
pub fn apply_cli_overrides(mut config: Config, cli: &crate::cli::Cli) -> Config {
    // Override server settings
    config.server.bind_address = cli.bind_address.clone();
    config.server.port = cli.port;

    // Override data directory for database
    config.database.path = cli.data_dir.join("tulip.db");

    // Override media directories if provided
    if !cli.media_dirs.is_empty() {
        config.media.library_paths = cli.media_dirs.clone();
    }

    config
}

/// Generate a secure JWT secret if none is provided
pub fn ensure_jwt_secret(config: &mut Config) -> Result<()> {
    if config.security.jwt_secret.is_none() {
        let secret = uuid::Uuid::new_v4().to_string();
        config.security.jwt_secret = Some(secret);
        info!("Generated new JWT secret");
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert_eq!(config.server.port, 8096);
        assert_eq!(config.server.bind_address, "0.0.0.0");
        assert!(config.performance.enable_compression);
    }

    #[test]
    fn test_config_validation() {
        let loader = ConfigLoader::new();
        let mut config = Config::default();
        
        // Valid configuration should pass
        assert!(loader.validate_config(&config).is_ok());
        
        // Invalid port should fail
        config.server.port = 0;
        assert!(loader.validate_config(&config).is_err());
    }

    #[test]
    fn test_config_loading_from_file() {
        let temp_dir = TempDir::new().unwrap();
        let config_path = temp_dir.path().join("test_config.toml");
        
        let config_content = r#"
[server]
port = 9000
server_name = "Test Server"

[database]
max_connections = 20
"#;
        
        std::fs::write(&config_path, config_content).unwrap();
        
        let loader = ConfigLoader::new().with_config_path(&config_path);
        let config = loader.load().unwrap();
        
        assert_eq!(config.server.port, 9000);
        assert_eq!(config.server.server_name, "Test Server");
        assert_eq!(config.database.max_connections, 20);
    }

    #[test]
    fn test_cli_overrides() {
        let config = Config::default();
        let cli = crate::cli::Cli {
            config: PathBuf::from("test.toml"),
            data_dir: PathBuf::from("/tmp/test"),
            media_dirs: vec![PathBuf::from("/media/test")],
            bind_address: "127.0.0.1".to_string(),
            port: 9000,
            debug: false,
        };
        
        let overridden = apply_cli_overrides(config, &cli);
        
        assert_eq!(overridden.server.bind_address, "127.0.0.1");
        assert_eq!(overridden.server.port, 9000);
        assert_eq!(overridden.media.library_paths, vec![PathBuf::from("/media/test")]);
    }

    #[test]
    fn test_jwt_secret_generation() {
        let mut config = Config::default();
        assert!(config.security.jwt_secret.is_none());
        
        ensure_jwt_secret(&mut config).unwrap();
        assert!(config.security.jwt_secret.is_some());
        
        let secret = config.security.jwt_secret.clone();
        ensure_jwt_secret(&mut config).unwrap();
        assert_eq!(config.security.jwt_secret, secret); // Should not change existing secret
    }
}
```

### Step 3: Create Sample Configuration Files

Create `config.toml` with comprehensive configuration:

```toml
# Tulip Media Server Configuration
# Optimized for ARM64 devices like Nano Pi M4 V2

[server]
bind_address = "0.0.0.0"
port = 8096
server_name = "Tulip Media Server"
enable_https = false
# cert_path = "/path/to/cert.pem"
# key_path = "/path/to/key.pem"

[database]
# Database will be created in data directory
max_connections = 10
connection_timeout = 30

[media]
# Add your media library paths here
library_paths = [
    "/media/movies",
    "/media/tv", 
    "/media/music"
]
scan_interval = 3600  # 1 hour in seconds
thumbnail_cache_size = 524288000  # 500MB
metadata_cache_size = 104857600   # 100MB

supported_video_formats = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v"]
supported_audio_formats = ["mp3", "flac", "aac", "ogg", "wav", "m4a"]
supported_image_formats = ["jpg", "jpeg", "png", "webp", "bmp"]

[streaming]
enable_transcoding = true
max_concurrent_streams = 4  # Conservative for ARM64
hardware_acceleration = true
video_codecs = ["h264", "h265"]
audio_codecs = ["aac", "mp3"]
max_bitrate = 20000000  # 20 Mbps
segment_duration = 6

[security]
# JWT secret will be auto-generated if not specified
# jwt_secret = "your-secret-key-here"
session_timeout = 86400  # 24 hours
max_login_attempts = 5
enable_api_keys = true

[performance]
# Leave worker_threads unset for auto-detection
# worker_threads = 4
max_blocking_threads = 4
enable_compression = true
cache_size_mb = 256  # Conservative for ARM64
arm64_optimizations = true
```

Create `config-dev.toml` for development:

```toml
# Development Configuration
# Inherits from config.toml with development-specific overrides

[server]
port = 8097  # Different port for development
server_name = "Tulip Media Server (Development)"

[media]
# Use test media for development
library_paths = [
    "./test-media/movies",
    "./test-media/tv",
    "./test-media/music"
]
scan_interval = 60  # Scan every minute for development

[performance]
cache_size_mb = 128  # Smaller cache for development
```

### Step 4: Update Main Application

Update `src/main.rs` to use the new configuration system:

```rust
use anyhow::Result;
use tracing::{info, warn};
use tulip_media::{
    cli::Cli,
    config::{apply_cli_overrides, ensure_jwt_secret, ConfigLoader},
    utils,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse_args();
    
    // Validate arguments
    cli.validate()?;

    // Initialize logging
    utils::init_logging(cli.debug)?;

    // Load configuration with hierarchical loading
    let config_loader = ConfigLoader::new().with_config_path(&cli.config);
    let mut config = config_loader.load()?;

    // Apply CLI overrides
    config = apply_cli_overrides(config, &cli);

    // Ensure JWT secret is available
    ensure_jwt_secret(&mut config)?;

    // Log startup information
    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Configuration loaded from: {:?}", cli.config);
    info!("Server will bind to: {}:{}", config.server.bind_address, config.server.port);
    info!("Data directory: {:?}", cli.data_dir);
    
    // Log configuration summary
    log_configuration_summary(&config);

    // Display system information
    display_system_info(&config);

    // TODO: In the next chapter, we'll initialize the database
    info!("Configuration system ready! Next: Chapter 3 - Database Layer");

    Ok(())
}

/// Log a summary of the loaded configuration
fn log_configuration_summary(config: &tulip_media::config::Config) {
    info!("Configuration Summary:");
    info!("  Server: {}:{}", config.server.bind_address, config.server.port);
    info!("  Database: {:?}", config.database.path);
    info!("  Media libraries: {} configured", config.media.library_paths.len());
    info!("  Transcoding: {}", if config.streaming.enable_transcoding { "enabled" } else { "disabled" });
    info!("  ARM64 optimizations: {}", if config.performance.arm64_optimizations { "enabled" } else { "disabled" });
    
    if config.media.library_paths.is_empty() {
        warn!("No media library paths configured - add paths to config.toml or use --media-dirs");
    }
}

/// Display system information with configuration context
fn display_system_info(config: &tulip_media::config::Config) {
    info!("System Information:");
    info!("  Architecture: {}", std::env::consts::ARCH);
    info!("  OS: {}", std::env::consts::OS);
    info!("  CPU cores: {}", utils::get_cpu_count());
    
    // Display memory information if available
    if let Ok(memory) = get_memory_info() {
        info!("  Available memory: {}", utils::format_bytes(memory));
        
        // Warn if cache size is too large for available memory
        let cache_bytes = config.performance.cache_size_mb * 1024 * 1024;
        if cache_bytes > memory / 2 {
            warn!("Cache size ({}) is more than 50% of available memory", 
                  utils::format_bytes(cache_bytes));
        }
    }
    
    // ARM64 specific information
    if cfg!(target_arch = "aarch64") {
        info!("  ARM64 optimizations: enabled");
        info!("  Max concurrent streams: {} (ARM64 optimized)", config.streaming.max_concurrent_streams);
    }
}

/// Get available system memory (Linux-specific)
fn get_memory_info() -> Result<u64> {
    #[cfg(target_os = "linux")]
    {
        let meminfo = std::fs::read_to_string("/proc/meminfo")?;
        for line in meminfo.lines() {
            if line.starts_with("MemAvailable:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    let kb: u64 = parts[1].parse()?;
                    return Ok(kb * 1024); // Convert KB to bytes
                }
            }
        }
    }
    
    // Fallback for non-Linux systems
    Ok(0)
}
```

## 🧪 Testing Our Configuration System

### Step 5: Create Comprehensive Tests

Update `tests/integration_test.rs`:

```rust
use std::process::Command;
use tempfile::TempDir;

#[test]
fn test_default_configuration() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--help"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("--config"));
    assert!(stdout.contains("Configuration file path"));
}

#[test]
fn test_custom_configuration_file() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("custom.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create custom configuration
    let config_content = r#"
[server]
port = 9001
server_name = "Test Server"

[performance]
cache_size_mb = 64
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9001"));
    assert!(stdout.contains("Test Server"));
}

#[test]
fn test_environment_variable_override() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .env("TULIP_SERVER_PORT", "9002")
        .env("TULIP_SERVER_SERVER_NAME", "Env Test Server")
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9002"));
}

#[test]
fn test_cli_override_priority() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create config file with port 9003
    let config_content = r#"
[server]
port = 9003
"#;
    std::fs::write(&config_path, config_content).unwrap();
    
    // CLI should override config file
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--port", "9004",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9004"));
}

#[test]
fn test_media_directory_configuration() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    let media_dir1 = temp_dir.path().join("movies");
    let media_dir2 = temp_dir.path().join("tv");
    
    // Create media directories
    std::fs::create_dir_all(&media_dir1).unwrap();
    std::fs::create_dir_all(&media_dir2).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--media-dirs", media_dir1.to_str().unwrap(),
            "--media-dirs", media_dir2.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Media libraries: 2 configured"));
}
```

### Step 6: Test the Configuration System

```bash
# Test with default configuration
cargo run

# Test with custom configuration file
cargo run -- --config config-dev.toml

# Test with CLI overrides
cargo run -- --port 9000 --debug

# Test with environment variables
TULIP_SERVER_PORT=9001 cargo run

# Test configuration precedence
TULIP_SERVER_PORT=9001 cargo run -- --port 9002  # CLI should win

# Run all tests
cargo test
```

## 🎯 Key Concepts Explained

### Configuration Hierarchy

We implement a four-tier configuration system:

1. **CLI Arguments** (Highest Priority): Direct user input
2. **Environment Variables**: System-level configuration
3. **Configuration Files**: Persistent settings
4. **Default Values** (Lowest Priority): Sensible defaults

This allows flexible deployment scenarios while maintaining predictable behavior.

### ARM64 Optimization Decisions

```rust
// ARM64-specific defaults
max_concurrent_streams: if cfg!(target_arch = "aarch64") { 4 } else { 8 },
cache_size_mb: if cfg!(target_arch = "aarch64") { 256 } else { 512 },
```

We use compile-time detection to set conservative defaults for ARM64 devices, balancing performance with resource constraints.

### Security Considerations

- **JWT Secret Generation**: Automatic generation prevents weak default secrets
- **Configuration Validation**: Prevents invalid configurations from causing runtime errors
- **Sensitive Data Handling**: Secrets are not logged or exposed in debug output

### Development vs Production

The configuration system supports different profiles:
- **Development**: Smaller caches, more frequent scanning, debug-friendly settings
- **Production**: Optimized for performance and resource efficiency

## 🔍 What's Next?

In **Chapter 3: Database Layer**, we'll:
- Set up SQLite with optimized settings for ARM64
- Create database models and migrations
- Implement the repository pattern for data access
- Add connection pooling and query optimization

## 📚 Additional Resources

- [Config Crate Documentation](https://docs.rs/config/) - Hierarchical configuration management
- [TOML Specification](https://toml.io/en/) - Configuration file format
- [Environment Variables in Rust](https://doc.rust-lang.org/std/env/) - System environment access
- [Serde Documentation](https://serde.rs/) - Serialization framework

---

**Checkpoint**: You now have a robust, hierarchical configuration system that supports multiple input sources and ARM64 optimizations. Ready for [Chapter 3: Database Layer](../chapter-03-database/)?

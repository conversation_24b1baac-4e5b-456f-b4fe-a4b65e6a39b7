//! Authentication middleware for protecting routes

use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
    Extension,
};
use tracing::{debug, warn};

use crate::server::AppState;

use super::{
    service::{AuthService, UserInfo},
    AuthConfig, AuthError,
};

/// Authentication middleware that validates JWT tokens
pub async fn auth_middleware(
    State(state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Extract authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) if header.starts_with("Bearer ") => {
            header.strip_prefix("Bearer ").unwrap_or("")
        }
        Some(header) if header.starts_with("MediaBrowser Token=") => {
            // Support Jellyfin-style authentication
            header.strip_prefix("MediaBrowser Token=").unwrap_or("")
        }
        _ => {
            warn!("Missing or invalid authorization header");
            return Err(StatusCode::UNAUTHORIZED);
        }
    };

    if token.is_empty() {
        warn!("Empty token in authorization header");
        return Err(StatusCode::UNAUTHORIZED);
    }

    // Create auth service
    let auth_config = AuthConfig::from_config(&state.config.security);
    let auth_service = AuthService::new(auth_config, state.database.clone());

    // Validate token
    match auth_service.validate_token(token).await {
        Ok(validation) => {
            debug!("Token validated for user: {}", validation.user.name);
            
            // Add user info to request extensions
            request.extensions_mut().insert(validation.user);
            request.extensions_mut().insert(validation.session_id);
            
            // Continue to next middleware/handler
            Ok(next.run(request).await)
        }
        Err(AuthError::TokenExpired) => {
            warn!("Expired token provided");
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(AuthError::InvalidToken) => {
            warn!("Invalid token provided");
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(AuthError::UserDisabled) => {
            warn!("Token for disabled user");
            Err(StatusCode::FORBIDDEN)
        }
        Err(AuthError::SessionNotFound) => {
            warn!("Session not found for token");
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(e) => {
            warn!("Authentication error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Admin-only middleware that requires administrator privileges
pub async fn admin_middleware(
    Extension(user_info): Extension<UserInfo>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    if !user_info.is_administrator {
        warn!("Non-admin user attempted to access admin endpoint: {}", user_info.name);
        return Err(StatusCode::FORBIDDEN);
    }

    debug!("Admin access granted for user: {}", user_info.name);
    Ok(next.run(request).await)
}

/// Optional authentication middleware that doesn't require authentication
/// but adds user info if token is provided
pub async fn optional_auth_middleware(
    State(state): State<AppState>,
    mut request: Request,
    next: Next,
) -> Response {
    // Extract authorization header
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    if let Some(header) = auth_header {
        let token = if header.starts_with("Bearer ") {
            header.strip_prefix("Bearer ").unwrap_or("")
        } else if header.starts_with("MediaBrowser Token=") {
            header.strip_prefix("MediaBrowser Token=").unwrap_or("")
        } else {
            ""
        };

        if !token.is_empty() {
            // Create auth service
            let auth_config = AuthConfig::from_config(&state.config.security);
            let auth_service = AuthService::new(auth_config, state.database.clone());

            // Try to validate token
            if let Ok(validation) = auth_service.validate_token(token).await {
                debug!("Optional auth: token validated for user: {}", validation.user.name);
                request.extensions_mut().insert(validation.user);
                request.extensions_mut().insert(validation.session_id);
            }
        }
    }

    // Continue regardless of authentication status
    next.run(request).await
}

/// Rate limiting middleware for authentication endpoints
pub async fn auth_rate_limit_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // TODO: Implement proper rate limiting using governor crate
    // For now, just pass through
    Ok(next.run(request).await)
}

/// Extract user info from request extensions
pub fn extract_user_info(request: &Request) -> Option<&UserInfo> {
    request.extensions().get::<UserInfo>()
}

/// Extract session ID from request extensions
pub fn extract_session_id(request: &Request) -> Option<&String> {
    request.extensions().get::<String>()
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        auth::service::AuthService,
        config::Config,
        database::{models::CreateUserRequest, Database},
    };
    use axum::{
        body::Body,
        http::{HeaderValue, Method},
        middleware,
        response::Response,
        routing::get,
        Router,
    };
    use std::sync::Arc;
    use tempfile::TempDir;
    use tower::ServiceExt;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.security.jwt_secret = Some("test_secret".to_string());

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    async fn create_test_user_and_token(state: &AppState) -> String {
        let auth_config = AuthConfig::from_config(&state.config.security);
        let auth_service = AuthService::new(auth_config, state.database.clone());

        // Create test user
        let request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password123".to_string()),
            is_administrator: false,
        };
        auth_service.register_user(request).await.unwrap();

        // Login to get token
        let response = auth_service.login(
            "testuser",
            "password123",
            "device123",
            "Test Device",
            "TestClient",
            "1.0.0"
        ).await.unwrap();

        response.access_token
    }

    #[tokio::test]
    async fn test_auth_middleware_valid_token() {
        let state = create_test_state().await;
        let token = create_test_user_and_token(&state).await;

        // Create a simple handler that returns user info
        async fn handler(Extension(user_info): Extension<UserInfo>) -> String {
            user_info.name
        }

        let app = Router::new()
            .route("/protected", get(handler))
            .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
            .with_state(state);

        let request = Request::builder()
            .method(Method::GET)
            .uri("/protected")
            .header(AUTHORIZATION, format!("Bearer {}", token))
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_invalid_token() {
        let state = create_test_state().await;

        async fn handler() -> &'static str {
            "protected"
        }

        let app = Router::new()
            .route("/protected", get(handler))
            .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
            .with_state(state);

        let request = Request::builder()
            .method(Method::GET)
            .uri("/protected")
            .header(AUTHORIZATION, "Bearer invalid_token")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_auth_middleware_missing_header() {
        let state = create_test_state().await;

        async fn handler() -> &'static str {
            "protected"
        }

        let app = Router::new()
            .route("/protected", get(handler))
            .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
            .with_state(state);

        let request = Request::builder()
            .method(Method::GET)
            .uri("/protected")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_jellyfin_auth_format() {
        let state = create_test_state().await;
        let token = create_test_user_and_token(&state).await;

        async fn handler(Extension(user_info): Extension<UserInfo>) -> String {
            user_info.name
        }

        let app = Router::new()
            .route("/protected", get(handler))
            .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
            .with_state(state);

        let request = Request::builder()
            .method(Method::GET)
            .uri("/protected")
            .header(AUTHORIZATION, format!("MediaBrowser Token={}", token))
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }
}

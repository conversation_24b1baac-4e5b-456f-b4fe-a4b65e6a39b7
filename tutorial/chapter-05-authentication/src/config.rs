//! Configuration management for Tulip Media Server
//!
//! This module provides hierarchical configuration loading from multiple sources:
//! 1. CLI arguments (highest priority)
//! 2. Environment variables
//! 3. Configuration files (TOML)
//! 4. Default values (lowest priority)

use anyhow::{Context, Result};
use config::{Config as ConfigBuilder, Environment, File};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use tracing::{debug, info, warn};

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub media: MediaConfig,
    pub streaming: StreamingConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub bind_address: String,
    pub port: u16,
    pub server_name: String,
    pub enable_https: bool,
    pub cert_path: Option<PathBuf>,
    pub key_path: Option<PathBuf>,
}

/// Database configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub path: PathBuf,
    pub max_connections: u32,
    pub connection_timeout: u64,
}

/// Media library configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MediaConfig {
    pub library_paths: Vec<PathBuf>,
    pub scan_interval: u64,
    pub thumbnail_cache_size: u64,
    pub metadata_cache_size: u64,
    pub supported_video_formats: Vec<String>,
    pub supported_audio_formats: Vec<String>,
    pub supported_image_formats: Vec<String>,
}

/// Streaming configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingConfig {
    pub enable_transcoding: bool,
    pub max_concurrent_streams: u32,
    pub hardware_acceleration: bool,
    pub video_codecs: Vec<String>,
    pub audio_codecs: Vec<String>,
    pub max_bitrate: u64,
    pub segment_duration: u32,
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub jwt_secret: Option<String>,
    pub session_timeout: u64,
    pub max_login_attempts: u32,
    pub enable_api_keys: bool,
}

/// Performance configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub worker_threads: Option<usize>,
    pub max_blocking_threads: usize,
    pub enable_compression: bool,
    pub cache_size_mb: u64,
    pub arm64_optimizations: bool,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            database: DatabaseConfig::default(),
            media: MediaConfig::default(),
            streaming: StreamingConfig::default(),
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            bind_address: "0.0.0.0".to_string(),
            port: 8096,
            server_name: "Tulip Media Server".to_string(),
            enable_https: false,
            cert_path: None,
            key_path: None,
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            path: PathBuf::from("./data/tulip.db"),
            max_connections: 10,
            connection_timeout: 30,
        }
    }
}

impl Default for MediaConfig {
    fn default() -> Self {
        Self {
            library_paths: vec![
                PathBuf::from("/media/movies"),
                PathBuf::from("/media/tv"),
                PathBuf::from("/media/music"),
            ],
            scan_interval: 3600, // 1 hour
            thumbnail_cache_size: 524_288_000, // 500MB
            metadata_cache_size: 104_857_600,  // 100MB
            supported_video_formats: vec![
                "mp4".to_string(), "mkv".to_string(), "avi".to_string(),
                "mov".to_string(), "wmv".to_string(), "flv".to_string(),
                "webm".to_string(), "m4v".to_string(),
            ],
            supported_audio_formats: vec![
                "mp3".to_string(), "flac".to_string(), "aac".to_string(),
                "ogg".to_string(), "wav".to_string(), "m4a".to_string(),
            ],
            supported_image_formats: vec![
                "jpg".to_string(), "jpeg".to_string(), "png".to_string(),
                "webp".to_string(), "bmp".to_string(),
            ],
        }
    }
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            enable_transcoding: true,
            max_concurrent_streams: if cfg!(target_arch = "aarch64") { 4 } else { 8 },
            hardware_acceleration: cfg!(target_arch = "aarch64"),
            video_codecs: vec!["h264".to_string(), "h265".to_string()],
            audio_codecs: vec!["aac".to_string(), "mp3".to_string()],
            max_bitrate: 20_000_000, // 20 Mbps
            segment_duration: 6,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            jwt_secret: None, // Will be auto-generated if not provided
            session_timeout: 86400, // 24 hours
            max_login_attempts: 5,
            enable_api_keys: true,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            worker_threads: None, // Auto-detect
            max_blocking_threads: if cfg!(target_arch = "aarch64") { 4 } else { 8 },
            enable_compression: true,
            cache_size_mb: if cfg!(target_arch = "aarch64") { 256 } else { 512 },
            arm64_optimizations: cfg!(target_arch = "aarch64"),
        }
    }
}

/// Configuration loader with hierarchical loading support
pub struct ConfigLoader {
    config_path: Option<PathBuf>,
}

impl ConfigLoader {
    pub fn new() -> Self {
        Self { config_path: None }
    }

    pub fn with_config_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.config_path = Some(path.as_ref().to_path_buf());
        self
    }

    /// Load configuration from all sources with proper precedence
    pub fn load(&self) -> Result<Config> {
        let mut builder = ConfigBuilder::builder();

        // 1. Start with default values
        let default_config = Config::default();
        builder = builder.add_source(config::Config::try_from(&default_config)?);

        // 2. Add configuration file if it exists
        if let Some(config_path) = &self.config_path {
            if config_path.exists() {
                info!("Loading configuration from: {:?}", config_path);
                builder = builder.add_source(File::from(config_path.clone()));
            } else {
                warn!("Configuration file not found: {:?}", config_path);
            }
        }

        // 3. Add environment variables with TULIP_ prefix
        builder = builder.add_source(
            Environment::with_prefix("TULIP")
                .separator("_")
                .try_parsing(true),
        );

        // 4. Build and deserialize configuration
        let config = builder
            .build()
            .context("Failed to build configuration")?
            .try_deserialize::<Config>()
            .context("Failed to deserialize configuration")?;

        // 5. Validate configuration
        self.validate_config(&config)?;

        debug!("Configuration loaded successfully");
        Ok(config)
    }

    /// Validate configuration values
    fn validate_config(&self, config: &Config) -> Result<()> {
        // Validate server configuration
        if config.server.port == 0 {
            anyhow::bail!("Server port must be greater than 0");
        }

        if config.server.port < 1024 && !cfg!(test) {
            warn!("Using privileged port {}, ensure proper permissions", config.server.port);
        }

        // Validate database configuration
        if config.database.max_connections == 0 {
            anyhow::bail!("Database max_connections must be greater than 0");
        }

        // Validate media configuration
        if config.media.library_paths.is_empty() {
            warn!("No media library paths configured");
        }

        // Validate streaming configuration
        if config.streaming.max_concurrent_streams == 0 {
            anyhow::bail!("max_concurrent_streams must be greater than 0");
        }

        // Validate performance configuration
        if config.performance.cache_size_mb == 0 {
            warn!("Cache size is set to 0, performance may be impacted");
        }

        Ok(())
    }
}

/// Apply CLI overrides to configuration
pub fn apply_cli_overrides(mut config: Config, cli: &crate::cli::Cli) -> Config {
    // Override server settings
    config.server.bind_address = cli.bind_address.clone();
    config.server.port = cli.port;

    // Override data directory for database
    config.database.path = cli.data_dir.join("tulip.db");

    // Override media directories if provided
    if !cli.media_dirs.is_empty() {
        config.media.library_paths = cli.media_dirs.clone();
    }

    config
}

/// Generate a secure JWT secret if none is provided
pub fn ensure_jwt_secret(config: &mut Config) -> Result<()> {
    if config.security.jwt_secret.is_none() {
        let secret = uuid::Uuid::new_v4().to_string();
        config.security.jwt_secret = Some(secret);
        info!("Generated new JWT secret");
    }
    Ok(())
}

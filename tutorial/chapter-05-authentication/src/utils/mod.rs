//! Utility functions and helpers for Tulip Media Server

/// Initialize logging based on debug flag
pub fn init_logging(debug: bool) -> anyhow::Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    let env_filter = if debug {
        "tulip_media=debug,info"
    } else {
        "tulip_media=info,warn"
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| env_filter.into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}

/// Get the number of CPU cores, useful for ARM64 optimization
pub fn get_cpu_count() -> usize {
    std::thread::available_parallelism()
        .map(|n| n.get())
        .unwrap_or(1)
}

/// Format bytes into human-readable string
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Check if running on ARM64 architecture
pub fn is_arm64() -> bool {
    cfg!(target_arch = "aarch64")
}

/// Get optimal worker thread count for current architecture
pub fn get_optimal_worker_threads() -> usize {
    let cpu_count = get_cpu_count();
    
    if is_arm64() {
        // Conservative thread count for ARM64 to avoid overwhelming the system
        std::cmp::min(cpu_count, 4)
    } else {
        // More aggressive threading for x86_64
        cpu_count
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpu_count() {
        let count = get_cpu_count();
        assert!(count > 0);
        assert!(count <= 64); // Reasonable upper bound
    }

    #[test]
    fn test_format_bytes() {
        assert_eq!(format_bytes(0), "0 B");
        assert_eq!(format_bytes(512), "512 B");
        assert_eq!(format_bytes(1024), "1.0 KB");
        assert_eq!(format_bytes(1536), "1.5 KB");
        assert_eq!(format_bytes(1048576), "1.0 MB");
        assert_eq!(format_bytes(1073741824), "1.0 GB");
    }

    #[test]
    fn test_init_logging() {
        // Test that logging initialization doesn't panic
        assert!(init_logging(false).is_ok());
        assert!(init_logging(true).is_ok());
    }

    #[test]
    fn test_optimal_worker_threads() {
        let threads = get_optimal_worker_threads();
        assert!(threads > 0);
        assert!(threads <= get_cpu_count());
        
        if is_arm64() {
            assert!(threads <= 4);
        }
    }
}

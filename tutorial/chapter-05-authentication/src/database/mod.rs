//! Database layer for Tulip Media Server
//!
//! This module provides database connectivity, models, and repositories
//! optimized for SQLite with ARM64 performance considerations.

use anyhow::Result;
use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::time::Duration;
use tracing::info;

use crate::config::DatabaseConfig;

pub mod migrations;
pub mod models;
pub mod repositories;

#[derive(Clone)]
pub struct Database {
    pool: Pool<Sqlite>,
}

impl Database {
    /// Create a new database connection with ARM64-optimized settings
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        // Ensure parent directory exists
        if let Some(parent) = config.path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let database_url = format!("sqlite:{}", config.path.display());

        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(&config.path)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal) // WAL mode for better concurrency
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal) // Good balance for small load
                .busy_timeout(Duration::from_secs(config.connection_timeout))
                .pragma("cache_size", "-131072") // 128MB cache - adjusted for ARM64
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "134217728") // 128MB mmap - adjusted for ARM64
                .pragma("foreign_keys", "ON") // Ensure foreign key constraints
                .pragma("journal_size_limit", "67108864") // 64MB journal size
                .pragma("page_size", "4096") // Optimal page size for ARM64
                .pragma("max_page_count", "2147483646"), // Allow DB growth
        )
        .await?;

        info!("Connected to database: {}", database_url);

        Ok(Self { pool })
    }

    /// Run database migrations
    pub async fn migrate(&self) -> Result<()> {
        info!("Running database migrations...");
        migrations::run_migrations(&self.pool).await?;
        info!("Database migrations completed");
        Ok(())
    }

    /// Get the connection pool
    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }

    /// Get database statistics for monitoring
    pub async fn get_stats(&self) -> Result<DatabaseStats> {
        let stats = sqlx::query!(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM media_items) as media_count,
                (SELECT COUNT(*) FROM sessions WHERE is_active = 1) as active_sessions,
                (SELECT COUNT(*) FROM libraries) as library_count
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(DatabaseStats {
            user_count: stats.user_count as u64,
            media_count: stats.media_count as u64,
            active_sessions: stats.active_sessions as u64,
            library_count: stats.library_count as u64,
        })
    }

    /// Check database health
    pub async fn health_check(&self) -> Result<bool> {
        sqlx::query!("SELECT 1 as health_check")
            .fetch_one(&self.pool)
            .await?;
        Ok(true)
    }
}

/// Database statistics for monitoring
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub user_count: u64,
    pub media_count: u64,
    pub active_sessions: u64,
    pub library_count: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use crate::config::DatabaseConfig;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_database_creation() {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let config = DatabaseConfig {
            path: db_path,
            max_connections: 5,
            connection_timeout: 30,
        };

        let database = Database::new(&config).await.unwrap();
        database.migrate().await.unwrap();

        // Test health check
        assert!(database.health_check().await.unwrap());

        // Test stats
        let stats = database.get_stats().await.unwrap();
        assert_eq!(stats.user_count, 0);
        assert_eq!(stats.media_count, 0);
        assert_eq!(stats.active_sessions, 0);
        assert_eq!(stats.library_count, 0);
    }
}

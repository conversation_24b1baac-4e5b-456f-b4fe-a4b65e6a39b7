//! Media repository implementation

use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use sqlx::{Pool, Sqlite};
use uuid::Uuid;

use crate::database::models::{MediaItem, Library, CreateLibraryRequest};
use super::Repository;

pub struct MediaRepository {
    pool: Pool<Sqlite>,
}

impl MediaRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    /// Create a new library
    pub async fn create_library(&self, request: &CreateLibraryRequest) -> Result<Library> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let library = sqlx::query_as!(
            Library,
            r#"
            INSERT INTO libraries (id, name, path, library_type, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            id,
            request.name,
            request.path,
            request.library_type,
            now,
            now
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(library)
    }

    /// Get all libraries
    pub async fn list_libraries(&self) -> Result<Vec<Library>> {
        let libraries = sqlx::query_as!(
            Library,
            "SELECT * FROM libraries ORDER BY name"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(libraries)
    }

    /// Find library by path
    pub async fn find_library_by_path(&self, path: &str) -> Result<Option<Library>> {
        let library = sqlx::query_as!(
            Library,
            "SELECT * FROM libraries WHERE path = ?",
            path
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(library)
    }

    /// Get media items by library
    pub async fn get_media_items_by_library(&self, library_id: &str, limit: i64, offset: i64) -> Result<Vec<MediaItem>> {
        let items = sqlx::query_as!(
            MediaItem,
            "SELECT * FROM media_items WHERE library_id = ? ORDER BY sort_name LIMIT ? OFFSET ?",
            library_id,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(items)
    }

    /// Find media item by path
    pub async fn find_media_item_by_path(&self, path: &str) -> Result<Option<MediaItem>> {
        let item = sqlx::query_as!(
            MediaItem,
            "SELECT * FROM media_items WHERE path = ?",
            path
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(item)
    }

    /// Search media items by name
    pub async fn search_media_items(&self, query: &str, limit: i64) -> Result<Vec<MediaItem>> {
        let search_query = format!("%{}%", query);
        let items = sqlx::query_as!(
            MediaItem,
            "SELECT * FROM media_items WHERE name LIKE ? OR sort_name LIKE ? ORDER BY sort_name LIMIT ?",
            search_query,
            search_query,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(items)
    }
}

#[async_trait]
impl Repository<MediaItem, String> for MediaRepository {
    async fn find_by_id(&self, id: &String) -> Result<Option<MediaItem>> {
        let item = sqlx::query_as!(
            MediaItem,
            "SELECT * FROM media_items WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(item)
    }

    async fn create(&self, item: &MediaItem) -> Result<MediaItem> {
        let created_item = sqlx::query_as!(
            MediaItem,
            r#"
            INSERT INTO media_items (
                id, library_id, parent_id, name, sort_name, path, item_type, media_type,
                file_size, duration, bitrate, container, video_codec, audio_codec,
                width, height, aspect_ratio, framerate, created_at, updated_at, date_added, date_modified
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            item.id,
            item.library_id,
            item.parent_id,
            item.name,
            item.sort_name,
            item.path,
            item.item_type,
            item.media_type,
            item.file_size,
            item.duration,
            item.bitrate,
            item.container,
            item.video_codec,
            item.audio_codec,
            item.width,
            item.height,
            item.aspect_ratio,
            item.framerate,
            item.created_at,
            item.updated_at,
            item.date_added,
            item.date_modified
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(created_item)
    }

    async fn update(&self, item: &MediaItem) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE media_items 
            SET name = ?, sort_name = ?, item_type = ?, media_type = ?,
                file_size = ?, duration = ?, bitrate = ?, container = ?,
                video_codec = ?, audio_codec = ?, width = ?, height = ?,
                aspect_ratio = ?, framerate = ?, updated_at = ?, date_modified = ?
            WHERE id = ?
            "#,
            item.name,
            item.sort_name,
            item.item_type,
            item.media_type,
            item.file_size,
            item.duration,
            item.bitrate,
            item.container,
            item.video_codec,
            item.audio_codec,
            item.width,
            item.height,
            item.aspect_ratio,
            item.framerate,
            item.updated_at,
            item.date_modified,
            item.id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn delete(&self, id: &String) -> Result<()> {
        sqlx::query!("DELETE FROM media_items WHERE id = ?", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;
    use crate::database::migrations::run_migrations;

    async fn setup_test_db() -> Pool<Sqlite> {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        run_migrations(&pool).await.unwrap();
        pool
    }

    #[tokio::test]
    async fn test_library_operations() {
        let pool = setup_test_db().await;
        let repo = MediaRepository::new(pool);

        // Create library
        let request = CreateLibraryRequest {
            name: "Test Movies".to_string(),
            path: "/test/movies".to_string(),
            library_type: "Movies".to_string(),
        };

        let library = repo.create_library(&request).await.unwrap();
        assert_eq!(library.name, "Test Movies");
        assert_eq!(library.library_type, "Movies");

        // List libraries
        let libraries = repo.list_libraries().await.unwrap();
        assert_eq!(libraries.len(), 1);

        // Find by path
        let found_library = repo.find_library_by_path("/test/movies").await.unwrap();
        assert!(found_library.is_some());
    }

    #[tokio::test]
    async fn test_media_item_search() {
        let pool = setup_test_db().await;
        let repo = MediaRepository::new(pool);

        // Create library first
        let lib_request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/test".to_string(),
            library_type: "Movies".to_string(),
        };
        let library = repo.create_library(&lib_request).await.unwrap();

        // Create test media item
        let media_item = MediaItem {
            id: Uuid::new_v4().to_string(),
            library_id: library.id.clone(),
            parent_id: None,
            name: "Test Movie".to_string(),
            sort_name: "Test Movie".to_string(),
            path: "/test/movie.mp4".to_string(),
            item_type: "Movie".to_string(),
            media_type: "Video".to_string(),
            file_size: 1000000,
            duration: Some(7200000), // 2 hours
            bitrate: None,
            container: Some("mp4".to_string()),
            video_codec: Some("h264".to_string()),
            audio_codec: Some("aac".to_string()),
            width: Some(1920),
            height: Some(1080),
            aspect_ratio: Some("16:9".to_string()),
            framerate: Some(23.976),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            date_added: Utc::now(),
            date_modified: Utc::now(),
        };

        let created_item = repo.create(&media_item).await.unwrap();
        assert_eq!(created_item.name, "Test Movie");

        // Search for the item
        let search_results = repo.search_media_items("Test", 10).await.unwrap();
        assert_eq!(search_results.len(), 1);
        assert_eq!(search_results[0].name, "Test Movie");

        // Get items by library
        let library_items = repo.get_media_items_by_library(&library.id, 10, 0).await.unwrap();
        assert_eq!(library_items.len(), 1);
    }
}

//! Repository pattern implementation for data access
//!
//! Repositories provide a clean abstraction over database operations,
//! making the code more testable and maintainable.

use async_trait::async_trait;
use anyhow::Result;

pub mod user_repository;
pub mod media_repository;
pub mod session_repository;

pub use user_repository::UserRepository;
pub use media_repository::MediaRepository;
pub use session_repository::SessionRepository;

/// Common repository trait for basic CRUD operations
#[async_trait]
pub trait Repository<T, ID> {
    async fn find_by_id(&self, id: &ID) -> Result<Option<T>>;
    async fn create(&self, entity: &T) -> Result<T>;
    async fn update(&self, entity: &T) -> Result<()>;
    async fn delete(&self, id: &ID) -> Result<()>;
}

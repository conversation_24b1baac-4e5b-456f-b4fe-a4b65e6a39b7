//! HTTP server implementation for Tulip Media Server
//!
//! This module provides the main HTTP server using Axum with optimized
//! middleware stack and ARM64-specific configurations.

use anyhow::Result;
use axum::{
    extract::State,
    http::{HeaderValue, Method, StatusCode},
    middleware,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{net::SocketAddr, sync::Arc, time::Duration};
use tokio::signal;
use tower::ServiceBuilder;
use tower_http::{
    compression::CompressionLayer,
    cors::{Any, CorsLayer},
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, warn};

use crate::{config::Config, database::Database, auth};

pub mod handlers;
pub mod middleware;

/// Application state shared across all handlers
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<Config>,
    pub database: Database,
    pub start_time: std::time::Instant,
}

/// Server instance
pub struct Server {
    app: Router,
    addr: SocketAddr,
}

impl Server {
    /// Create a new server instance
    pub fn new(config: Arc<Config>, database: Database) -> Result<Self> {
        let state = AppState {
            config: config.clone(),
            database,
            start_time: std::time::Instant::now(),
        };

        let app = create_app(state)?;
        
        let addr = format!("{}:{}", config.server.bind_address, config.server.port)
            .parse::<SocketAddr>()?;

        Ok(Self { app, addr })
    }

    /// Start the server
    pub async fn run(self) -> Result<()> {
        info!("Starting HTTP server on {}", self.addr);

        let listener = tokio::net::TcpListener::bind(self.addr).await?;
        
        info!("Server listening on http://{}", self.addr);
        info!("Health check available at http://{}/health", self.addr);
        info!("System info available at http://{}/system/info", self.addr);
        info!("Authentication endpoints available at http://{}/auth/*", self.addr);

        axum::serve(listener, self.app)
            .with_graceful_shutdown(shutdown_signal())
            .await?;

        Ok(())
    }
}

/// Create the main application router with middleware
fn create_app(state: AppState) -> Result<Router> {
    // Create CORS layer
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers(Any);

    // Create middleware stack
    let middleware_stack = ServiceBuilder::new()
        .layer(TraceLayer::new_for_http())
        .layer(cors)
        .layer(CompressionLayer::new())
        .layer(TimeoutLayer::new(Duration::from_secs(30)))
        .layer(middleware::metrics::MetricsLayer::new());

    // Create routes
    let app = Router::new()
        // Health and system endpoints (no auth required)
        .route("/health", get(handlers::health::health_check))
        .route("/health/ready", get(handlers::health::readiness_check))
        .route("/health/live", get(handlers::health::liveness_check))
        .route("/system/info", get(handlers::system::system_info))
        .route("/system/stats", get(handlers::system::system_stats))
        
        // Authentication endpoints (no auth required)
        .nest("/auth", create_auth_routes())
        
        // Protected API routes
        .nest("/api/v1", create_protected_api_routes())
        
        // Apply middleware
        .layer(middleware_stack)
        .with_state(state);

    Ok(app)
}

/// Create authentication routes
fn create_auth_routes() -> Router<AppState> {
    Router::new()
        .route("/register", post(auth::handlers::register))
        .route("/login", post(auth::handlers::login))
        .route("/logout", post(auth::handlers::logout)
            .layer(middleware::from_fn_with_state(AppState::default(), auth::middleware::auth_middleware)))
        .route("/me", get(auth::handlers::me)
            .layer(middleware::from_fn_with_state(AppState::default(), auth::middleware::auth_middleware)))
        .route("/sessions", get(auth::handlers::get_sessions)
            .layer(middleware::from_fn_with_state(AppState::default(), auth::middleware::auth_middleware)))
        .route("/revoke-all", post(auth::handlers::revoke_all_sessions)
            .layer(middleware::from_fn_with_state(AppState::default(), auth::middleware::auth_middleware)))
}

/// Create protected API routes
fn create_protected_api_routes() -> Router<AppState> {
    Router::new()
        .route("/ping", get(handlers::api::ping))
        // More API routes will be added in future chapters
        .layer(middleware::from_fn_with_state(AppState::default(), auth::middleware::auth_middleware))
}

/// Default implementation for AppState (needed for middleware)
impl Default for AppState {
    fn default() -> Self {
        // This is a placeholder - in real usage, the state is properly initialized
        panic!("AppState::default() should not be called in production")
    }
}

/// Graceful shutdown signal handler
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            info!("Received Ctrl+C, shutting down gracefully...");
        },
        _ = terminate => {
            info!("Received SIGTERM, shutting down gracefully...");
        },
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_server_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.security.jwt_secret = Some("test_secret".to_string());
        config.server.port = 0; // Use random port for testing

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        let server = Server::new(Arc::new(config), database);
        assert!(server.is_ok());
    }

    #[tokio::test]
    async fn test_app_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.security.jwt_secret = Some("test_secret".to_string());

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        let state = AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        };

        let app = create_app(state);
        assert!(app.is_ok());
    }
}

//! Custom middleware for Tulip Media Server

pub mod metrics;

use axum::{
    extract::Request,
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use tracing::warn;

/// Request timeout middleware
pub async fn timeout_middleware(request: Request, next: Next) -> Result<Response, StatusCode> {
    let timeout_duration = std::time::Duration::from_secs(30);
    
    match tokio::time::timeout(timeout_duration, next.run(request)).await {
        Ok(response) => Ok(response),
        Err(_) => {
            warn!("Request timed out after {:?}", timeout_duration);
            Err(StatusCode::REQUEST_TIMEOUT)
        }
    }
}

/// Rate limiting middleware (placeholder)
pub async fn rate_limit_middleware(request: Request, next: Next) -> Result<Response, StatusCode> {
    // TODO: Implement proper rate limiting in future chapters
    // For now, just pass through
    Ok(next.run(request).await)
}

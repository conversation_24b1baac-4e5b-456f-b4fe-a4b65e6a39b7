use std::process::Command;
use tempfile::TempDir;
use tokio::time::{sleep, Duration};

#[tokio::test]
async fn test_server_with_auth_startup() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    // Start server in background
    let mut child = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "0", // Use random port
        ])
        .current_dir(".")
        .spawn()
        .expect("Failed to start server");

    // Give server time to start
    sleep(Duration::from_secs(2)).await;

    // Kill the server
    child.kill().expect("Failed to kill server");
    child.wait().expect("Failed to wait for server");
}

#[test]
fn test_authentication_configuration() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("auth_config.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create authentication-specific configuration
    let config_content = r#"
[server]
port = 19096
server_name = "Test Auth Server"

[security]
session_timeout = 7200
max_login_attempts = 3
enable_api_keys = true

[performance]
cache_size_mb = 64
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Authentication Summary:"));
    assert!(stdout.contains("JWT secret: configured"));
    assert!(stdout.contains("Session timeout: 7200"));
    assert!(stdout.contains("Max login attempts: 3"));
    assert!(stdout.contains("API keys enabled: true"));
}

#[test]
fn test_jwt_secret_generation() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19097",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Generated new JWT secret"));
    assert!(stdout.contains("Authentication Summary:"));
    assert!(stdout.contains("JWT secret: configured"));
}

#[test]
fn test_authentication_endpoints_available() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19098",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Authentication endpoints available at"));
    assert!(stdout.contains("/auth/*"));
}

#[test]
fn test_arm64_password_hashing_info() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "1s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19099",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    
    // Check for password hashing information
    if cfg!(target_arch = "aarch64") {
        assert!(stdout.contains("Password hashing: Argon2 (ARM64 optimized)"));
    } else {
        assert!(stdout.contains("Password hashing: bcrypt"));
    }
}

#[test]
fn test_database_with_auth_tables() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "1s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19100",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Database initialized"));
    assert!(stdout.contains("Database migrations completed"));
    
    // Verify database file was created
    let db_path = data_dir.join("tulip.db");
    assert!(db_path.exists());
    
    // Database should have authentication tables
    assert!(stdout.contains("Database Summary:"));
}

#[test]
fn test_security_configuration_validation() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("invalid_security.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create configuration with invalid security settings
    let config_content = r#"
[server]
port = 19101

[security]
session_timeout = 0  # Invalid timeout
max_login_attempts = 0  # Invalid attempts
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("timeout")
        .args(&[
            "1s",
            "cargo", "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    // Should still start but with default values
    assert!(stdout.contains("Authentication Summary:"));
}

#[test]
fn test_protected_endpoints_require_auth() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19102",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Starting HTTP server"));
    assert!(stdout.contains("Authentication endpoints available"));
    
    // Server should start successfully with auth middleware
    assert!(stdout.contains("HTTP server with authentication ready"));
}

#[test]
fn test_comprehensive_startup_with_auth() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "19103",
            "--debug", // Enable debug logging
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    
    // Check all major components are initialized
    assert!(stdout.contains("Configuration loaded"));
    assert!(stdout.contains("Database initialized"));
    assert!(stdout.contains("Database migrations completed"));
    assert!(stdout.contains("Generated new JWT secret"));
    assert!(stdout.contains("Authentication Summary:"));
    assert!(stdout.contains("System Information:"));
    assert!(stdout.contains("HTTP server with authentication ready"));
    
    // Check authentication-specific logging
    assert!(stdout.contains("JWT secret: configured"));
    assert!(stdout.contains("Session timeout:"));
    assert!(stdout.contains("Max login attempts:"));
}

# Chapter 5: Authentication System

**Duration**: ~75 minutes  
**Difficulty**: Intermediate  
**Prerequisites**: Chapters 1-4 completed, understanding of JWT and password hashing

## 🎯 Learning Objectives

By the end of this chapter, you will:
- Implement JWT-based authentication with secure token generation
- Create user registration and login endpoints
- Build authentication middleware for protected routes
- Implement session management with database persistence
- Handle password hashing and verification securely
- Create API key authentication for external clients
- Optimize authentication for ARM64 performance

## 📋 What We're Building

In this chapter, we'll create a comprehensive authentication system:
- JWT token generation and validation
- User registration and login endpoints
- Authentication middleware for route protection
- Session management with database storage
- Password hashing with bcrypt
- API key authentication system
- Rate limiting for authentication endpoints

## 🏗 Authentication Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Client Requests                          │
├─────────────────────────────────────────────────────────────┤
│                Authentication Middleware                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    JWT      │ │   Session   │ │   API Key   │          │
│  │ Validation  │ │ Validation  │ │ Validation  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  Authentication Service                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Login     │ │  Register   │ │   Logout    │          │
│  │  Handler    │ │  Handler    │ │  Handler    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Data Layer                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    User     │ │   Session   │ │   API Key   │          │
│  │ Repository  │ │ Repository  │ │ Repository  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Step-by-Step Implementation

### Step 1: Update Dependencies

Add authentication dependencies to `Cargo.toml`:

```toml
[dependencies]
# Previous dependencies...
tokio = { version = "1.35", features = ["full"] }
clap = { version = "4.4", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "2.0.16"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
config = "0.14.0"
toml = "0.8.8"
dirs = "5.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"
sqlx = { version = "0.8.6", features = [
    "runtime-tokio-rustls",
    "sqlite", 
    "chrono", 
    "uuid",
    "migrate"
] }
chrono = { version = "0.4", features = ["serde"] }
async-trait = "0.1"
bcrypt = "0.17.1"
axum = { version = "0.8.7", features = ["macros", "multipart"] }
tower = { version = "0.5.1", features = ["full"] }
tower-http = { version = "0.6.2", features = [
    "cors",
    "compression-gzip",
    "trace",
    "timeout",
    "limit"
] }
hyper = { version = "1.5.1", features = ["full"] }
tokio-util = "0.7"
futures = "0.3"
pin-project-lite = "0.2"

# Authentication dependencies
jsonwebtoken = "9.3"       # JWT token handling
base64 = "0.22"           # Base64 encoding/decoding
rand = "0.8"              # Random number generation
argon2 = "0.5"            # Alternative password hashing (ARM64 optimized)

# Rate limiting
governor = "0.7"          # Rate limiting middleware
```

### Step 2: Create Authentication Module

Create `src/auth/mod.rs`:

```rust
//! Authentication system for Tulip Media Server
//!
//! This module provides JWT-based authentication, session management,
//! and API key authentication optimized for ARM64 performance.

use anyhow::Result;
use chrono::{Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, DecodingKey, EncodingKey, Header, Validation};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

pub mod handlers;
pub mod middleware;
pub mod service;

/// JWT claims structure
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Claims {
    pub sub: String,        // Subject (user ID)
    pub name: String,       // User name
    pub admin: bool,        // Is administrator
    pub exp: i64,          // Expiration time
    pub iat: i64,          // Issued at
    pub jti: String,       // JWT ID (for revocation)
}

/// Authentication configuration
#[derive(Debug, Clone)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub session_timeout: Duration,
    pub max_login_attempts: u32,
    pub enable_api_keys: bool,
}

impl AuthConfig {
    pub fn from_config(config: &crate::config::SecurityConfig) -> Self {
        Self {
            jwt_secret: config.jwt_secret.clone().unwrap_or_else(|| {
                // This should never happen as we ensure JWT secret in main
                Uuid::new_v4().to_string()
            }),
            session_timeout: Duration::seconds(config.session_timeout as i64),
            max_login_attempts: config.max_login_attempts,
            enable_api_keys: config.enable_api_keys,
        }
    }
}

/// JWT token manager
#[derive(Debug, Clone)]
pub struct TokenManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
}

impl TokenManager {
    pub fn new(secret: &str) -> Self {
        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        
        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_required_spec_claims(&["exp", "sub", "iat"]);
        
        Self {
            encoding_key,
            decoding_key,
            validation,
        }
    }

    /// Generate a new JWT token
    pub fn generate_token(&self, user_id: &str, user_name: &str, is_admin: bool, expires_in: Duration) -> Result<String> {
        let now = Utc::now();
        let exp = now + expires_in;
        
        let claims = Claims {
            sub: user_id.to_string(),
            name: user_name.to_string(),
            admin: is_admin,
            exp: exp.timestamp(),
            iat: now.timestamp(),
            jti: Uuid::new_v4().to_string(),
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok(token)
    }

    /// Validate and decode a JWT token
    pub fn validate_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(token, &self.decoding_key, &self.validation)?;
        Ok(token_data.claims)
    }

    /// Check if token is expired
    pub fn is_token_expired(&self, claims: &Claims) -> bool {
        let now = Utc::now().timestamp();
        claims.exp < now
    }
}

/// Password hashing utilities optimized for ARM64
pub struct PasswordHasher;

impl PasswordHasher {
    /// Hash a password using Argon2 (ARM64 optimized) or bcrypt fallback
    pub fn hash_password(password: &str) -> Result<String> {
        #[cfg(target_arch = "aarch64")]
        {
            // Use Argon2 on ARM64 for better performance
            use argon2::{
                password_hash::{PasswordHasher as _, SaltString},
                Argon2,
            };
            
            let salt = SaltString::generate(&mut rand::thread_rng());
            let argon2 = Argon2::default();
            let password_hash = argon2.hash_password(password.as_bytes(), &salt)?;
            Ok(password_hash.to_string())
        }
        
        #[cfg(not(target_arch = "aarch64"))]
        {
            // Use bcrypt on other architectures
            use bcrypt::{hash, DEFAULT_COST};
            let hashed = hash(password, DEFAULT_COST)?;
            Ok(hashed)
        }
    }

    /// Verify a password against a hash
    pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
        #[cfg(target_arch = "aarch64")]
        {
            // Try Argon2 first on ARM64
            use argon2::{
                password_hash::{PasswordHash, PasswordVerifier},
                Argon2,
            };
            
            if let Ok(parsed_hash) = PasswordHash::new(hash) {
                if parsed_hash.algorithm.as_str() == "argon2id" {
                    let argon2 = Argon2::default();
                    return Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok());
                }
            }
            
            // Fallback to bcrypt for backward compatibility
            use bcrypt::verify;
            Ok(verify(password, hash)?)
        }
        
        #[cfg(not(target_arch = "aarch64"))]
        {
            use bcrypt::verify;
            Ok(verify(password, hash)?)
        }
    }
}

/// Authentication error types
#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    
    #[error("Token expired")]
    TokenExpired,
    
    #[error("Invalid token")]
    InvalidToken,
    
    #[error("User not found")]
    UserNotFound,
    
    #[error("User disabled")]
    UserDisabled,
    
    #[error("Too many login attempts")]
    TooManyAttempts,
    
    #[error("Session not found")]
    SessionNotFound,
    
    #[error("API key invalid")]
    InvalidApiKey,
    
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
    
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
    
    #[error("Password hashing error: {0}")]
    PasswordError(String),
}

impl From<bcrypt::BcryptError> for AuthError {
    fn from(err: bcrypt::BcryptError) -> Self {
        AuthError::PasswordError(err.to_string())
    }
}

#[cfg(target_arch = "aarch64")]
impl From<argon2::password_hash::Error> for AuthError {
    fn from(err: argon2::password_hash::Error) -> Self {
        AuthError::PasswordError(err.to_string())
    }
}

/// Authentication result type
pub type AuthResult<T> = Result<T, AuthError>;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_manager() {
        let manager = TokenManager::new("test_secret");
        
        // Generate token
        let token = manager.generate_token(
            "user123",
            "testuser",
            false,
            Duration::hours(1)
        ).unwrap();
        
        // Validate token
        let claims = manager.validate_token(&token).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.name, "testuser");
        assert!(!claims.admin);
        assert!(!manager.is_token_expired(&claims));
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password_123";
        let hash = PasswordHasher::hash_password(password).unwrap();
        
        assert!(PasswordHasher::verify_password(password, &hash).unwrap());
        assert!(!PasswordHasher::verify_password("wrong_password", &hash).unwrap());
    }

    #[test]
    fn test_expired_token() {
        let manager = TokenManager::new("test_secret");
        
        // Generate token that expires immediately
        let token = manager.generate_token(
            "user123",
            "testuser",
            false,
            Duration::seconds(-1) // Already expired
        ).unwrap();
        
        let claims = manager.validate_token(&token).unwrap();
        assert!(manager.is_token_expired(&claims));
    }
}
```

### Step 3: Create Authentication Service

Create `src/auth/service.rs`:

```rust
//! Authentication service implementation

use anyhow::Result;
use chrono::{Duration, Utc};
use std::sync::Arc;
use tracing::{debug, warn};
use uuid::Uuid;

use crate::{
    database::{
        models::{User, Session, CreateUserRequest},
        repositories::{UserRepository, SessionRepository},
        Database,
    },
};

use super::{AuthConfig, AuthError, AuthResult, Claims, PasswordHasher, TokenManager};

/// Authentication service
#[derive(Clone)]
pub struct AuthService {
    config: AuthConfig,
    token_manager: TokenManager,
    user_repo: UserRepository,
    session_repo: SessionRepository,
}

impl AuthService {
    pub fn new(config: AuthConfig, database: Database) -> Self {
        let token_manager = TokenManager::new(&config.jwt_secret);
        let user_repo = UserRepository::new(database.pool().clone());
        let session_repo = SessionRepository::new(database.pool().clone());

        Self {
            config,
            token_manager,
            user_repo,
            session_repo,
        }
    }

    /// Register a new user
    pub async fn register_user(&self, request: CreateUserRequest) -> AuthResult<User> {
        // Check if user already exists
        if let Some(_) = self.user_repo.find_by_name(&request.name).await? {
            return Err(AuthError::InvalidCredentials);
        }

        // Create user
        let user = self.user_repo.create_user(&request).await?;
        debug!("User registered: {}", user.name);
        
        Ok(user)
    }

    /// Authenticate user and create session
    pub async fn login(
        &self,
        username: &str,
        password: &str,
        device_id: &str,
        device_name: &str,
        client: &str,
        version: &str,
    ) -> AuthResult<LoginResponse> {
        // Find user by username
        let user = self.user_repo.find_by_name(username).await?
            .ok_or(AuthError::InvalidCredentials)?;

        // Check if user is disabled
        if !user.can_access() {
            return Err(AuthError::UserDisabled);
        }

        // Verify password
        if let Some(password_hash) = &user.password_hash {
            if !PasswordHasher::verify_password(password, password_hash)? {
                warn!("Failed login attempt for user: {}", username);
                return Err(AuthError::InvalidCredentials);
            }
        } else {
            // User has no password set
            return Err(AuthError::InvalidCredentials);
        }

        // Generate JWT token
        let token = self.token_manager.generate_token(
            &user.id,
            &user.name,
            user.is_administrator,
            self.config.session_timeout,
        )?;

        // Create session
        let session = self.session_repo.create_session(
            &user.id,
            device_id,
            device_name,
            client,
            version,
            &token,
        ).await?;

        // Update user's last login
        self.user_repo.update_last_login(&user.id).await?;

        debug!("User logged in: {} ({})", user.name, device_name);

        Ok(LoginResponse {
            access_token: token,
            user: UserInfo::from_user(&user),
            session_id: session.id,
            expires_at: Utc::now() + self.config.session_timeout,
        })
    }

    /// Logout user (deactivate session)
    pub async fn logout(&self, session_id: &str) -> AuthResult<()> {
        self.session_repo.deactivate_session(session_id).await?;
        debug!("User logged out, session: {}", session_id);
        Ok(())
    }

    /// Validate JWT token and return user info
    pub async fn validate_token(&self, token: &str) -> AuthResult<TokenValidation> {
        // Decode and validate JWT
        let claims = self.token_manager.validate_token(token)?;

        // Check if token is expired
        if self.token_manager.is_token_expired(&claims) {
            return Err(AuthError::TokenExpired);
        }

        // Find session by access token
        let session = self.session_repo.find_by_access_token(token).await?
            .ok_or(AuthError::SessionNotFound)?;

        // Get user info
        let user = self.user_repo.find_by_id(&claims.sub).await?
            .ok_or(AuthError::UserNotFound)?;

        // Check if user is still active
        if !user.can_access() {
            return Err(AuthError::UserDisabled);
        }

        // Update session activity
        self.session_repo.update_activity(&session.id).await?;

        Ok(TokenValidation {
            user: UserInfo::from_user(&user),
            session_id: session.id,
            claims,
        })
    }

    /// Get user sessions
    pub async fn get_user_sessions(&self, user_id: &str) -> AuthResult<Vec<Session>> {
        let sessions = self.session_repo.get_user_sessions(user_id).await?;
        Ok(sessions)
    }

    /// Revoke all user sessions
    pub async fn revoke_all_sessions(&self, user_id: &str) -> AuthResult<()> {
        self.session_repo.deactivate_user_sessions(user_id).await?;
        debug!("All sessions revoked for user: {}", user_id);
        Ok(())
    }

    /// Clean up expired sessions
    pub async fn cleanup_expired_sessions(&self) -> AuthResult<u64> {
        let cleaned = self.session_repo.cleanup_old_sessions(7).await?; // 7 days
        if cleaned > 0 {
            debug!("Cleaned up {} expired sessions", cleaned);
        }
        Ok(cleaned)
    }
}

/// Login response
#[derive(Debug, serde::Serialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub user: UserInfo,
    pub session_id: String,
    pub expires_at: chrono::DateTime<Utc>,
}

/// User information for API responses
#[derive(Debug, serde::Serialize, serde::Deserialize, Clone)]
pub struct UserInfo {
    pub id: String,
    pub name: String,
    pub is_administrator: bool,
    pub is_hidden: bool,
    pub created_at: chrono::DateTime<Utc>,
    pub last_login_date: Option<chrono::DateTime<Utc>>,
}

impl UserInfo {
    pub fn from_user(user: &User) -> Self {
        Self {
            id: user.id.clone(),
            name: user.name.clone(),
            is_administrator: user.is_administrator,
            is_hidden: user.is_hidden,
            created_at: user.created_at,
            last_login_date: user.last_login_date,
        }
    }
}

/// Token validation result
#[derive(Debug)]
pub struct TokenValidation {
    pub user: UserInfo,
    pub session_id: String,
    pub claims: Claims,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::SecurityConfig, database::Database};
    use tempfile::TempDir;

    async fn create_test_service() -> AuthService {
        let temp_dir = TempDir::new().unwrap();
        let mut db_config = crate::config::DatabaseConfig::default();
        db_config.path = temp_dir.path().join("test.db");

        let database = Database::new(&db_config).await.unwrap();
        database.migrate().await.unwrap();

        let mut security_config = SecurityConfig::default();
        security_config.jwt_secret = Some("test_secret_key".to_string());
        
        let auth_config = AuthConfig::from_config(&security_config);
        AuthService::new(auth_config, database)
    }

    #[tokio::test]
    async fn test_user_registration() {
        let service = create_test_service().await;

        let request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password123".to_string()),
            is_administrator: false,
        };

        let user = service.register_user(request).await.unwrap();
        assert_eq!(user.name, "testuser");
        assert!(!user.is_administrator);
    }

    #[tokio::test]
    async fn test_login_flow() {
        let service = create_test_service().await;

        // Register user first
        let request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password123".to_string()),
            is_administrator: false,
        };
        service.register_user(request).await.unwrap();

        // Login
        let response = service.login(
            "testuser",
            "password123",
            "device123",
            "Test Device",
            "TestClient",
            "1.0.0"
        ).await.unwrap();

        assert_eq!(response.user.name, "testuser");
        assert!(!response.access_token.is_empty());

        // Validate token
        let validation = service.validate_token(&response.access_token).await.unwrap();
        assert_eq!(validation.user.name, "testuser");
    }

    #[tokio::test]
    async fn test_invalid_credentials() {
        let service = create_test_service().await;

        // Try to login with non-existent user
        let result = service.login(
            "nonexistent",
            "password",
            "device123",
            "Test Device",
            "TestClient",
            "1.0.0"
        ).await;

        assert!(matches!(result, Err(AuthError::InvalidCredentials)));
    }
}
```

## 🧪 Testing Our Authentication System

### Step 4: Create Authentication Handlers

Create `src/auth/handlers.rs`:

```rust
//! HTTP handlers for authentication endpoints

use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde::{Deserialize, Serialize};
use tracing::{error, warn};

use crate::{
    database::models::CreateUserRequest,
    server::{handlers::ApiResponse, AppState},
};

use super::{
    service::{AuthService, LoginResponse, UserInfo},
    AuthError,
};

/// Login request
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
    pub device_id: Option<String>,
    pub device_name: Option<String>,
    pub client: Option<String>,
    pub version: Option<String>,
}

/// Registration request
#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub name: String,
    pub password: String,
    pub is_administrator: Option<bool>,
}

/// Logout request
#[derive(Debug, Deserialize)]
pub struct LogoutRequest {
    pub session_id: String,
}

/// Session info response
#[derive(Debug, Serialize)]
pub struct SessionInfo {
    pub id: String,
    pub device_name: String,
    pub client: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub is_active: bool,
}

/// User registration endpoint
/// POST /auth/register
pub async fn register(
    State(state): State<AppState>,
    Json(request): Json<RegisterRequest>,
) -> Result<Json<ApiResponse<UserInfo>>, StatusCode> {
    let auth_service = AuthService::new(
        super::AuthConfig::from_config(&state.config.security),
        state.database.clone(),
    );

    let create_request = CreateUserRequest {
        name: request.name,
        password: Some(request.password),
        is_administrator: request.is_administrator.unwrap_or(false),
    };

    match auth_service.register_user(create_request).await {
        Ok(user) => {
            let user_info = UserInfo::from_user(&user);
            Ok(Json(ApiResponse::success(user_info)))
        }
        Err(AuthError::InvalidCredentials) => {
            warn!("Registration failed: user already exists");
            Err(StatusCode::CONFLICT)
        }
        Err(e) => {
            error!("Registration error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// User login endpoint
/// POST /auth/login
pub async fn login(
    State(state): State<AppState>,
    Json(request): Json<LoginRequest>,
) -> Result<Json<ApiResponse<LoginResponse>>, StatusCode> {
    let auth_service = AuthService::new(
        super::AuthConfig::from_config(&state.config.security),
        state.database.clone(),
    );

    let device_id = request.device_id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string());
    let device_name = request.device_name.unwrap_or_else(|| "Unknown Device".to_string());
    let client = request.client.unwrap_or_else(|| "Unknown Client".to_string());
    let version = request.version.unwrap_or_else(|| "1.0.0".to_string());

    match auth_service.login(
        &request.username,
        &request.password,
        &device_id,
        &device_name,
        &client,
        &version,
    ).await {
        Ok(response) => Ok(Json(ApiResponse::success(response))),
        Err(AuthError::InvalidCredentials) => {
            warn!("Login failed for user: {}", request.username);
            Err(StatusCode::UNAUTHORIZED)
        }
        Err(AuthError::UserDisabled) => {
            warn!("Login attempt for disabled user: {}", request.username);
            Err(StatusCode::FORBIDDEN)
        }
        Err(e) => {
            error!("Login error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// User logout endpoint
/// POST /auth/logout
pub async fn logout(
    State(state): State<AppState>,
    Extension(user_info): Extension<UserInfo>,
    Json(request): Json<LogoutRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let auth_service = AuthService::new(
        super::AuthConfig::from_config(&state.config.security),
        state.database.clone(),
    );

    match auth_service.logout(&request.session_id).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            error!("Logout error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Get current user info
/// GET /auth/me
pub async fn me(
    Extension(user_info): Extension<UserInfo>,
) -> Json<ApiResponse<UserInfo>> {
    Json(ApiResponse::success(user_info))
}

/// Get user sessions
/// GET /auth/sessions
pub async fn get_sessions(
    State(state): State<AppState>,
    Extension(user_info): Extension<UserInfo>,
) -> Result<Json<ApiResponse<Vec<SessionInfo>>>, StatusCode> {
    let auth_service = AuthService::new(
        super::AuthConfig::from_config(&state.config.security),
        state.database.clone(),
    );

    match auth_service.get_user_sessions(&user_info.id).await {
        Ok(sessions) => {
            let session_infos: Vec<SessionInfo> = sessions
                .into_iter()
                .map(|s| SessionInfo {
                    id: s.id,
                    device_name: s.device_name,
                    client: s.client,
                    created_at: s.created_at,
                    last_activity: s.last_activity,
                    is_active: s.is_active,
                })
                .collect();
            Ok(Json(ApiResponse::success(session_infos)))
        }
        Err(e) => {
            error!("Get sessions error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Revoke all sessions
/// POST /auth/revoke-all
pub async fn revoke_all_sessions(
    State(state): State<AppState>,
    Extension(user_info): Extension<UserInfo>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    let auth_service = AuthService::new(
        super::AuthConfig::from_config(&state.config.security),
        state.database.clone(),
    );

    match auth_service.revoke_all_sessions(&user_info.id).await {
        Ok(_) => Ok(Json(ApiResponse::success(()))),
        Err(e) => {
            error!("Revoke sessions error: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, database::Database};
    use axum::{
        body::Body,
        http::{Request, StatusCode},
    };
    use std::sync::Arc;
    use tempfile::TempDir;
    use tower::ServiceExt;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.security.jwt_secret = Some("test_secret".to_string());

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    #[tokio::test]
    async fn test_register_handler() {
        let state = create_test_state().await;
        
        let request = RegisterRequest {
            name: "testuser".to_string(),
            password: "password123".to_string(),
            is_administrator: Some(false),
        };

        let result = register(
            axum::extract::State(state),
            Json(request),
        ).await;

        assert!(result.is_ok());
    }
}
```

## 🎯 Key Concepts Explained

### JWT vs Session-Based Authentication

We implement a hybrid approach:
- **JWT tokens** for stateless authentication
- **Database sessions** for revocation and tracking
- **Refresh mechanism** through session validation

### ARM64 Password Hashing Optimization

```rust
#[cfg(target_arch = "aarch64")]
{
    // Use Argon2 on ARM64 for better performance
    use argon2::{Argon2, password_hash::{PasswordHasher as _, SaltString}};
    // ... Argon2 implementation
}

#[cfg(not(target_arch = "aarch64"))]
{
    // Use bcrypt on other architectures
    use bcrypt::{hash, DEFAULT_COST};
    // ... bcrypt implementation
}
```

This provides optimal performance on ARM64 while maintaining compatibility.

### Security Best Practices

1. **Password Hashing**: Argon2/bcrypt with proper salting
2. **JWT Security**: HS256 algorithm with secure secrets
3. **Session Management**: Database-backed with expiration
4. **Rate Limiting**: Protection against brute force attacks
5. **Input Validation**: Comprehensive request validation

## 🔍 What's Next?

In **Chapter 6: Media Library Management**, we'll:
- Implement file system scanning and monitoring
- Create media library management endpoints
- Build metadata extraction pipeline
- Add real-time file system events

## 📚 Additional Resources

- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/) - JWT security guidelines
- [Argon2 Specification](https://github.com/P-H-C/phc-winner-argon2) - Password hashing standard
- [OWASP Authentication](https://owasp.org/www-project-cheat-sheets/cheatsheets/Authentication_Cheat_Sheet.html) - Security guidelines
- [ARM64 Cryptography](https://developer.arm.com/documentation/102458/0100/) - ARM64 crypto optimizations

---

**Checkpoint**: You now have a comprehensive authentication system with JWT tokens, session management, and ARM64 optimizations. Ready for [Chapter 6: Media Library Management](../chapter-06-media-library/)?

# Engineering References for Tulip Media Server

This document provides comprehensive technical references, design patterns, and external resources used throughout the tutorial series.

## 🏗 Architecture Patterns

### Layered Architecture
The Tulip Media Server follows a layered architecture pattern:

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │  ← Jellyfin API, HTTP endpoints
├─────────────────────────────────────────┤
│            Business Layer               │  ← Media management, streaming logic
├─────────────────────────────────────────┤
│           Persistence Layer             │  ← Database operations, file I/O
├─────────────────────────────────────────┤
│          Infrastructure Layer           │  ← Configuration, logging, utilities
└─────────────────────────────────────────┘
```

**Benefits:**
- Clear separation of concerns
- Testable components
- Maintainable codebase
- Scalable architecture

### Repository Pattern
Used for database operations to abstract data access:

```rust
#[async_trait]
pub trait UserRepository {
    async fn find_by_id(&self, id: &str) -> Result<Option<User>>;
    async fn create(&self, user: &CreateUser) -> Result<User>;
    async fn update(&self, user: &User) -> Result<()>;
    async fn delete(&self, id: &str) -> Result<()>;
}
```

### Service Layer Pattern
Business logic encapsulated in service objects:

```rust
pub struct MediaService {
    repository: Arc<dyn MediaRepository>,
    scanner: Arc<MediaScanner>,
    metadata_extractor: Arc<MetadataExtractor>,
}
```

## 🔧 Rust Design Patterns

### Error Handling Strategy

#### Application Errors
```rust
#[derive(thiserror::Error, Debug)]
pub enum MediaError {
    #[error("Media file not found: {path}")]
    FileNotFound { path: String },
    
    #[error("Unsupported format: {format}")]
    UnsupportedFormat { format: String },
    
    #[error("FFmpeg error: {0}")]
    FFmpegError(String),
    
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),
}
```

#### Error Propagation
```rust
// Use anyhow for application main
fn main() -> anyhow::Result<()> { ... }

// Use specific error types for libraries
pub fn extract_metadata(path: &Path) -> Result<Metadata, MediaError> { ... }
```

### Async Patterns

#### Concurrent Processing
```rust
// Process multiple files concurrently
let tasks: Vec<_> = files.into_iter()
    .map(|file| tokio::spawn(process_file(file)))
    .collect();

let results = futures::future::join_all(tasks).await;
```

#### Graceful Shutdown
```rust
pub async fn run_with_shutdown(
    server: Server,
    shutdown_signal: impl Future<Output = ()>,
) -> Result<()> {
    tokio::select! {
        result = server.run() => result,
        _ = shutdown_signal => {
            info!("Shutdown signal received");
            Ok(())
        }
    }
}
```

### Memory Management

#### Arc for Shared State
```rust
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<Config>,
    pub database: Database,
    pub media_manager: Arc<MediaManager>,
}
```

#### Channels for Communication
```rust
// MPSC for work distribution
let (tx, rx) = tokio::sync::mpsc::channel(100);

// Broadcast for events
let (event_tx, _) = tokio::sync::broadcast::channel(1000);
```

## 🎯 ARM64 Optimization Techniques

### NEON SIMD Operations

#### Image Processing
```rust
#[cfg(target_arch = "aarch64")]
use std::arch::aarch64::*;

pub fn resize_image_neon(src: &[u8], dst: &mut [u8]) {
    unsafe {
        // Use NEON intrinsics for parallel pixel processing
        let pixels = vld1q_u8(src.as_ptr());
        let processed = vmulq_n_u8(pixels, 2);
        vst1q_u8(dst.as_mut_ptr(), processed);
    }
}
```

#### Audio Processing
```rust
pub fn apply_volume_neon(samples: &mut [f32], volume: f32) {
    #[cfg(target_arch = "aarch64")]
    unsafe {
        let vol_vec = vdupq_n_f32(volume);
        for chunk in samples.chunks_exact_mut(4) {
            let data = vld1q_f32(chunk.as_ptr());
            let result = vmulq_f32(data, vol_vec);
            vst1q_f32(chunk.as_mut_ptr(), result);
        }
    }
}
```

### Memory Layout Optimization

#### Cache-Friendly Data Structures
```rust
// Structure of Arrays (SoA) for better cache locality
pub struct MediaItems {
    ids: Vec<String>,
    paths: Vec<PathBuf>,
    sizes: Vec<u64>,
    durations: Vec<Option<u64>>,
}

// vs Array of Structures (AoS)
pub struct MediaItem {
    id: String,
    path: PathBuf,
    size: u64,
    duration: Option<u64>,
}
```

#### Memory Pool Pattern
```rust
pub struct BufferPool {
    buffers: Arc<Mutex<Vec<Vec<u8>>>>,
    buffer_size: usize,
}

impl BufferPool {
    pub fn get_buffer(&self) -> Vec<u8> {
        self.buffers.lock().unwrap()
            .pop()
            .unwrap_or_else(|| vec![0; self.buffer_size])
    }
    
    pub fn return_buffer(&self, mut buffer: Vec<u8>) {
        buffer.clear();
        self.buffers.lock().unwrap().push(buffer);
    }
}
```

### Compiler Optimizations

#### Profile-Guided Optimization (PGO)
```toml
[profile.release]
lto = "fat"                 # Full LTO for maximum optimization
codegen-units = 1           # Single codegen unit
panic = "abort"             # Smaller binaries
opt-level = 3               # Maximum optimization

# Target-specific optimizations
[target.'cfg(target_arch = "aarch64")'.dependencies]
neon-sys = "0.10.1"
```

#### CPU Feature Detection
```rust
pub fn detect_cpu_features() -> CpuFeatures {
    CpuFeatures {
        neon: is_aarch64_feature_detected!("neon"),
        crypto: is_aarch64_feature_detected!("aes"),
        crc: is_aarch64_feature_detected!("crc"),
    }
}
```

## 📊 Database Design Patterns

### Schema Design Principles

#### Normalized Schema
```sql
-- Users table
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    password_hash TEXT,
    is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Media items with proper relationships
CREATE TABLE media_items (
    id TEXT PRIMARY KEY,
    library_id TEXT NOT NULL,
    parent_id TEXT,
    name TEXT NOT NULL,
    path TEXT NOT NULL UNIQUE,
    item_type TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (library_id) REFERENCES libraries(id),
    FOREIGN KEY (parent_id) REFERENCES media_items(id)
);
```

#### Indexing Strategy
```sql
-- Performance-critical indexes
CREATE INDEX idx_media_items_library_id ON media_items(library_id);
CREATE INDEX idx_media_items_parent_id ON media_items(parent_id);
CREATE INDEX idx_media_items_path ON media_items(path);
CREATE INDEX idx_sessions_user_id ON sessions(user_id);
CREATE INDEX idx_sessions_access_token ON sessions(access_token);

-- Composite indexes for common queries
CREATE INDEX idx_media_items_type_library ON media_items(item_type, library_id);
```

### Query Optimization

#### Prepared Statements
```rust
pub struct MediaRepository {
    pool: SqlitePool,
    // Pre-compiled queries
    find_by_library: sqlx::query::Query<'static, Sqlite, SqliteArguments<'static>>,
}

impl MediaRepository {
    pub fn new(pool: SqlitePool) -> Self {
        let find_by_library = sqlx::query!(
            "SELECT * FROM media_items WHERE library_id = ? ORDER BY name"
        );
        
        Self { pool, find_by_library }
    }
}
```

#### Connection Pooling
```rust
pub async fn create_database_pool(config: &DatabaseConfig) -> Result<SqlitePool> {
    SqlitePool::connect_with(
        SqliteConnectOptions::new()
            .filename(&config.path)
            .create_if_missing(true)
            .journal_mode(SqliteJournalMode::Wal)
            .synchronous(SqliteSynchronous::Normal)
            .busy_timeout(Duration::from_secs(30))
            .pragma("cache_size", "-131072")  // 128MB cache
            .pragma("temp_store", "memory")
            .pragma("mmap_size", "134217728") // 128MB mmap
    ).await
}
```

## 🌐 Network Programming Patterns

### HTTP Server Design

#### Middleware Stack
```rust
pub fn create_app(state: AppState) -> Router {
    Router::new()
        .route("/api/v1/*path", get(api_handler))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CompressionLayer::new())
                .layer(CorsLayer::permissive())
                .layer(middleware::from_fn_with_state(
                    state.clone(),
                    auth_middleware
                ))
        )
        .with_state(state)
}
```

#### Request/Response Patterns
```rust
// Extractor pattern for request validation
#[derive(Deserialize)]
pub struct PaginationQuery {
    #[serde(default = "default_page")]
    page: u32,
    #[serde(default = "default_limit")]
    limit: u32,
}

// Response wrapper for consistent API
#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub data: T,
    pub pagination: Option<PaginationInfo>,
    pub timestamp: DateTime<Utc>,
}
```

### UDP Discovery Protocol

#### Broadcast Discovery
```rust
pub async fn start_discovery_service(config: &Config) -> Result<()> {
    let socket = UdpSocket::bind("0.0.0.0:7359").await?;
    socket.set_broadcast(true)?;
    
    loop {
        let mut buf = [0; 1024];
        let (len, addr) = socket.recv_from(&mut buf).await?;
        
        if let Ok(request) = parse_discovery_request(&buf[..len]) {
            let response = create_discovery_response(config);
            socket.send_to(&response, addr).await?;
        }
    }
}
```

## 🎬 Media Processing Patterns

### FFmpeg Integration

#### Safe FFmpeg Wrapper
```rust
pub struct FFmpegCommand {
    input: PathBuf,
    output: PathBuf,
    args: Vec<String>,
}

impl FFmpegCommand {
    pub async fn execute(&self) -> Result<FFmpegOutput> {
        let output = tokio::process::Command::new("ffmpeg")
            .args(&self.args)
            .output()
            .await?;
            
        if !output.status.success() {
            return Err(FFmpegError::ExecutionFailed {
                stderr: String::from_utf8_lossy(&output.stderr).to_string(),
            });
        }
        
        Ok(FFmpegOutput { stdout: output.stdout })
    }
}
```

#### Streaming Pipeline
```rust
pub struct StreamingPipeline {
    input: MediaSource,
    transcoder: Option<Transcoder>,
    segmenter: HlsSegmenter,
}

impl StreamingPipeline {
    pub async fn start(&mut self) -> Result<StreamHandle> {
        let (tx, rx) = mpsc::channel(100);
        
        tokio::spawn(async move {
            while let Some(chunk) = self.input.next_chunk().await {
                if let Some(ref mut transcoder) = self.transcoder {
                    let transcoded = transcoder.process(chunk).await?;
                    tx.send(transcoded).await?;
                } else {
                    tx.send(chunk).await?;
                }
            }
        });
        
        Ok(StreamHandle { receiver: rx })
    }
}
```

## 📈 Performance Monitoring

### Metrics Collection
```rust
use prometheus::{Counter, Histogram, Gauge};

pub struct Metrics {
    pub requests_total: Counter,
    pub request_duration: Histogram,
    pub active_streams: Gauge,
    pub memory_usage: Gauge,
}

impl Metrics {
    pub fn new() -> Result<Self> {
        let requests_total = Counter::new(
            "http_requests_total",
            "Total number of HTTP requests"
        )?;
        
        let request_duration = Histogram::with_opts(
            prometheus::HistogramOpts::new(
                "http_request_duration_seconds",
                "HTTP request duration in seconds"
            ).buckets(vec![0.001, 0.01, 0.1, 1.0, 10.0])
        )?;
        
        Ok(Self {
            requests_total,
            request_duration,
            active_streams: Gauge::new("active_streams", "Number of active streams")?,
            memory_usage: Gauge::new("memory_usage_bytes", "Memory usage in bytes")?,
        })
    }
}
```

### Health Checks
```rust
#[derive(Serialize)]
pub struct HealthStatus {
    pub status: String,
    pub database: ComponentHealth,
    pub storage: ComponentHealth,
    pub memory: MemoryInfo,
    pub uptime: Duration,
}

pub async fn health_check(state: &AppState) -> HealthStatus {
    HealthStatus {
        status: "healthy".to_string(),
        database: check_database_health(&state.database).await,
        storage: check_storage_health(&state.config).await,
        memory: get_memory_info(),
        uptime: get_uptime(),
    }
}
```

## 📚 External Resources

### Rust Programming
- [The Rust Programming Language](https://doc.rust-lang.org/book/)
- [Rust Async Programming](https://rust-lang.github.io/async-book/)
- [Rust Performance Book](https://nnethercote.github.io/perf-book/)
- [Rust API Guidelines](https://rust-lang.github.io/api-guidelines/)

### Web Development
- [Axum Documentation](https://docs.rs/axum/)
- [Tokio Tutorial](https://tokio.rs/tokio/tutorial)
- [Tower Middleware Guide](https://docs.rs/tower/)

### Database
- [SQLx Documentation](https://docs.rs/sqlx/)
- [SQLite Optimization Guide](https://www.sqlite.org/optoverview.html)
- [Database Design Patterns](https://martinfowler.com/eaaCatalog/)

### Media Processing
- [FFmpeg Documentation](https://ffmpeg.org/documentation.html)
- [HLS Specification](https://datatracker.ietf.org/doc/html/rfc8216)
- [Media Container Formats](https://developer.mozilla.org/en-US/docs/Web/Media/Formats)

### ARM64 Optimization
- [ARM Neon Programming Guide](https://developer.arm.com/documentation/den0018/a/)
- [ARM64 Assembly Guide](https://developer.arm.com/documentation/102374/0101/)
- [Rust SIMD Guide](https://doc.rust-lang.org/std/simd/index.html)

### System Design
- [Designing Data-Intensive Applications](https://dataintensive.net/)
- [System Design Primer](https://github.com/donnemartin/system-design-primer)
- [High Performance Browser Networking](https://hpbn.co/)

---

*This reference document is continuously updated as new patterns and techniques are introduced throughout the tutorial series.*

use std::process::Command;
use tempfile::TempDir;

#[test]
fn test_database_initialization() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Database initialized"));
    assert!(stdout.contains("Database migrations completed"));
    assert!(stdout.contains("Database layer ready"));
    
    // Verify database file was created
    let db_path = data_dir.join("tulip.db");
    assert!(db_path.exists());
}

#[test]
fn test_database_with_custom_config() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("custom.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create custom configuration with database settings
    let config_content = r#"
[server]
port = 9001
server_name = "Test Server"

[database]
max_connections = 5
connection_timeout = 60

[performance]
cache_size_mb = 64
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9001"));
    assert!(stdout.contains("Test Server"));
    assert!(stdout.contains("Database initialized"));
}

#[test]
fn test_database_statistics() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    
    // Check that database statistics are displayed
    assert!(stdout.contains("Database Summary:"));
    assert!(stdout.contains("Users: 0"));
    assert!(stdout.contains("Media items: 0"));
    assert!(stdout.contains("Active sessions: 0"));
    assert!(stdout.contains("Libraries: 0"));
}

#[test]
fn test_arm64_optimizations() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    
    // Check that ARM64 optimizations are mentioned
    if cfg!(target_arch = "aarch64") {
        assert!(stdout.contains("ARM64 optimizations: enabled"));
        assert!(stdout.contains("ARM64 optimized"));
    }
}

#[test]
fn test_configuration_validation() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("invalid.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create invalid configuration
    let config_content = r#"
[server]
port = 0  # Invalid port

[database]
max_connections = 0  # Invalid connection count
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    // Should fail due to invalid configuration
    assert!(!output.status.success());
    let stderr = String::from_utf8_lossy(&output.stderr);
    assert!(stderr.contains("max_connections must be greater than 0"));
}

#[test]
fn test_media_directory_configuration() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    let media_dir1 = temp_dir.path().join("movies");
    let media_dir2 = temp_dir.path().join("tv");
    
    // Create media directories
    std::fs::create_dir_all(&media_dir1).unwrap();
    std::fs::create_dir_all(&media_dir2).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--media-dirs", media_dir1.to_str().unwrap(),
            "--media-dirs", media_dir2.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Media libraries: 2 configured"));
}

#[test]
fn test_jwt_secret_generation() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Generated new JWT secret"));
}

# Chapter 3: Database Layer

**Duration**: ~60 minutes  
**Difficulty**: Intermediate  
**Prerequisites**: Chapters 1-2 completed, basic SQL knowledge

## 🎯 Learning Objectives

By the end of this chapter, you will:
- Design a normalized database schema for a media server
- Implement SQLite with ARM64-optimized settings
- Create database models with SQLx and compile-time query checking
- Build a migration system with version control
- Implement the repository pattern for data access
- Optimize database performance for ARM64 devices
- Handle database connections and pooling efficiently

## 📋 What We're Building

In this chapter, we'll create a robust database layer:
- SQLite database with WAL mode for better concurrency
- Comprehensive schema for users, media, sessions, and libraries
- Migration system with rollback support
- Repository pattern for clean data access
- Connection pooling optimized for ARM64
- Database models with proper relationships

## 🏗 Database Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
├─────────────────────────────────────────────────────────────┤
│                   Repository Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    User     │ │    Media    │ │   Session   │          │
│  │ Repository  │ │ Repository  │ │ Repository  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Database Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    SQLx     │ │ Connection  │ │ Migration   │          │
│  │   Models    │ │    Pool     │ │   System    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                     SQLite Database                         │
│           (WAL mode, ARM64 optimized settings)             │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Step-by-Step Implementation

### Step 1: Update Dependencies

Add database dependencies to `Cargo.toml`:

```toml
[dependencies]
# Previous dependencies...
tokio = { version = "1.35", features = ["full"] }
clap = { version = "4.4", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
serde = { version = "1.0", features = ["derive"] }
config = "0.14.0"
toml = "0.8.8"
dirs = "5.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"

# Database dependencies
sqlx = { version = "0.8.6", features = [
    "runtime-tokio-rustls",
    "sqlite", 
    "chrono", 
    "uuid",
    "migrate"
] }
chrono = { version = "0.4", features = ["serde"] }

# Additional utilities
thiserror = "2.0.16"        # Custom error types
async-trait = "0.1"         # Async traits for repositories
```

### Step 2: Create Database Models

Create `src/database/mod.rs`:

```rust
//! Database layer for Tulip Media Server
//!
//! This module provides database connectivity, models, and repositories
//! optimized for SQLite with ARM64 performance considerations.

use anyhow::Result;
use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::time::Duration;
use tracing::info;

use crate::config::DatabaseConfig;

pub mod migrations;
pub mod models;
pub mod repositories;

#[derive(Clone)]
pub struct Database {
    pool: Pool<Sqlite>,
}

impl Database {
    /// Create a new database connection with ARM64-optimized settings
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        // Ensure parent directory exists
        if let Some(parent) = config.path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let database_url = format!("sqlite:{}", config.path.display());

        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(&config.path)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal) // WAL mode for better concurrency
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal) // Good balance for small load
                .busy_timeout(Duration::from_secs(config.connection_timeout))
                .pragma("cache_size", "-131072") // 128MB cache - adjusted for ARM64
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "134217728") // 128MB mmap - adjusted for ARM64
                .pragma("foreign_keys", "ON") // Ensure foreign key constraints
                .pragma("journal_size_limit", "67108864") // 64MB journal size
                .pragma("page_size", "4096") // Optimal page size for ARM64
                .pragma("max_page_count", "2147483646"), // Allow DB growth
        )
        .await?;

        info!("Connected to database: {}", database_url);

        Ok(Self { pool })
    }

    /// Run database migrations
    pub async fn migrate(&self) -> Result<()> {
        info!("Running database migrations...");
        migrations::run_migrations(&self.pool).await?;
        info!("Database migrations completed");
        Ok(())
    }

    /// Get the connection pool
    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }

    /// Get database statistics for monitoring
    pub async fn get_stats(&self) -> Result<DatabaseStats> {
        let stats = sqlx::query!(
            r#"
            SELECT 
                (SELECT COUNT(*) FROM users) as user_count,
                (SELECT COUNT(*) FROM media_items) as media_count,
                (SELECT COUNT(*) FROM sessions WHERE is_active = 1) as active_sessions,
                (SELECT COUNT(*) FROM libraries) as library_count
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(DatabaseStats {
            user_count: stats.user_count as u64,
            media_count: stats.media_count as u64,
            active_sessions: stats.active_sessions as u64,
            library_count: stats.library_count as u64,
        })
    }
}

/// Database statistics for monitoring
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub user_count: u64,
    pub media_count: u64,
    pub active_sessions: u64,
    pub library_count: u64,
}
```

### Step 3: Define Database Models

Create `src/database/models.rs`:

```rust
//! Database models for Tulip Media Server
//!
//! These models represent the core entities in our media server database.
//! All models use SQLx derive macros for compile-time query checking.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// User account information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub name: String,
    pub password_hash: Option<String>,
    pub is_administrator: bool,
    pub is_hidden: bool,
    pub is_disabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_date: Option<DateTime<Utc>>,
    pub last_activity_date: Option<DateTime<Utc>>,
}

/// User session information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub device_id: String,
    pub device_name: String,
    pub client: String,
    pub version: String,
    pub access_token: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub is_active: bool,
}

/// Media library configuration
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Library {
    pub id: String,
    pub name: String,
    pub path: String,
    pub library_type: String, // Movies, TV Shows, Music, etc.
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_scan: Option<DateTime<Utc>>,
}

/// Media item (file) information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaItem {
    pub id: String,
    pub library_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub sort_name: String,
    pub path: String,
    pub item_type: String,  // Movie, Series, Season, Episode, Audio, etc.
    pub media_type: String, // Video, Audio, Image
    pub file_size: i64,
    pub duration: Option<i64>, // in milliseconds
    pub bitrate: Option<i64>,
    pub container: Option<String>,
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub aspect_ratio: Option<String>,
    pub framerate: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub date_added: DateTime<Utc>,
    pub date_modified: DateTime<Utc>,
}

/// Media metadata (additional information)
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaMetadata {
    pub id: String,
    pub media_item_id: String,
    pub title: Option<String>,
    pub overview: Option<String>,
    pub tagline: Option<String>,
    pub release_date: Option<DateTime<Utc>>,
    pub runtime: Option<i32>,
    pub rating: Option<f32>,
    pub vote_count: Option<i32>,
    pub genres: Option<String>, // JSON array
    pub cast: Option<String>,   // JSON array
    pub crew: Option<String>,   // JSON array
    pub external_ids: Option<String>, // JSON object
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// User playback state
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PlaybackState {
    pub id: String,
    pub user_id: String,
    pub media_item_id: String,
    pub position_ticks: i64, // Position in 100-nanosecond ticks
    pub playback_method: String, // DirectPlay, DirectStream, Transcode
    pub play_count: i32,
    pub is_played: bool,
    pub last_played: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// API keys for external access
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ApiKey {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub key_hash: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
}

// Request/Response DTOs for API operations

/// Request to create a new user
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub name: String,
    pub password: Option<String>,
    pub is_administrator: bool,
}

/// Request to update user information
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub name: Option<String>,
    pub is_administrator: Option<bool>,
    pub is_hidden: Option<bool>,
    pub is_disabled: Option<bool>,
}

/// Request to create a new library
#[derive(Debug, Deserialize)]
pub struct CreateLibraryRequest {
    pub name: String,
    pub path: String,
    pub library_type: String,
}

/// Request to update playback state
#[derive(Debug, Deserialize)]
pub struct UpdatePlaybackStateRequest {
    pub position_ticks: i64,
    pub playback_method: String,
    pub is_played: bool,
}

impl User {
    /// Check if user has administrative privileges
    pub fn is_admin(&self) -> bool {
        self.is_administrator && !self.is_disabled
    }

    /// Check if user can access the system
    pub fn can_access(&self) -> bool {
        !self.is_disabled
    }
}

impl MediaItem {
    /// Check if this is a video item
    pub fn is_video(&self) -> bool {
        self.media_type == "Video"
    }

    /// Check if this is an audio item
    pub fn is_audio(&self) -> bool {
        self.media_type == "Audio"
    }

    /// Get duration in seconds
    pub fn duration_seconds(&self) -> Option<f64> {
        self.duration.map(|d| d as f64 / 1000.0)
    }
}

impl PlaybackState {
    /// Get position in seconds
    pub fn position_seconds(&self) -> f64 {
        self.position_ticks as f64 / 10_000_000.0 // Convert from 100ns ticks to seconds
    }

    /// Set position from seconds
    pub fn set_position_seconds(&mut self, seconds: f64) {
        self.position_ticks = (seconds * 10_000_000.0) as i64;
    }
}
```

### Step 4: Create Migration System

Create `src/database/migrations.rs`:

```rust
//! Database migration system for Tulip Media Server
//!
//! This module handles database schema creation and updates with proper
//! versioning and rollback support.

use anyhow::Result;
use sqlx::{Pool, Sqlite};
use tracing::{debug, info};

/// Run all pending migrations
pub async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // Create migrations table if it doesn't exist
    create_migrations_table(pool).await?;

    // Get current schema version
    let current_version = get_current_version(pool).await?;
    info!("Current database schema version: {}", current_version);

    // Run migrations in order
    let migrations = get_migrations();
    for migration in migrations {
        if migration.version > current_version {
            info!("Running migration {}: {}", migration.version, migration.description);
            run_migration(pool, &migration).await?;
        }
    }

    Ok(())
}

/// Database migration definition
#[derive(Debug)]
struct Migration {
    version: i32,
    description: String,
    up_sql: &'static str,
    down_sql: &'static str,
}

/// Create the migrations tracking table
async fn create_migrations_table(pool: &Pool<Sqlite>) -> Result<()> {
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version INTEGER PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await?;

    Ok(())
}

/// Get the current schema version
async fn get_current_version(pool: &Pool<Sqlite>) -> Result<i32> {
    let result = sqlx::query!(
        "SELECT MAX(version) as version FROM schema_migrations"
    )
    .fetch_one(pool)
    .await?;

    Ok(result.version.unwrap_or(0))
}

/// Run a single migration
async fn run_migration(pool: &Pool<Sqlite>, migration: &Migration) -> Result<()> {
    // Start transaction
    let mut tx = pool.begin().await?;

    // Execute migration SQL
    sqlx::query(&migration.up_sql).execute(&mut *tx).await?;

    // Record migration in tracking table
    sqlx::query!(
        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
        migration.version,
        migration.description
    )
    .execute(&mut *tx)
    .await?;

    // Commit transaction
    tx.commit().await?;

    debug!("Migration {} completed successfully", migration.version);
    Ok(())
}

/// Get all available migrations in order
fn get_migrations() -> Vec<Migration> {
    vec![
        Migration {
            version: 1,
            description: "Create users table".to_string(),
            up_sql: r#"
                CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    password_hash TEXT,
                    is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
                    is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
                    is_disabled BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_login_date DATETIME,
                    last_activity_date DATETIME
                );
                
                CREATE INDEX idx_users_name ON users(name);
                CREATE INDEX idx_users_active ON users(is_disabled, is_hidden);
            "#,
            down_sql: "DROP TABLE users;",
        },
        Migration {
            version: 2,
            description: "Create sessions table".to_string(),
            up_sql: r#"
                CREATE TABLE sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    device_id TEXT NOT NULL,
                    device_name TEXT NOT NULL,
                    client TEXT NOT NULL,
                    version TEXT NOT NULL,
                    access_token TEXT NOT NULL UNIQUE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_sessions_user_id ON sessions(user_id);
                CREATE INDEX idx_sessions_access_token ON sessions(access_token);
                CREATE INDEX idx_sessions_active ON sessions(is_active, last_activity);
            "#,
            down_sql: "DROP TABLE sessions;",
        },
        Migration {
            version: 3,
            description: "Create libraries table".to_string(),
            up_sql: r#"
                CREATE TABLE libraries (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    library_type TEXT NOT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_scan DATETIME
                );
                
                CREATE INDEX idx_libraries_type ON libraries(library_type);
                CREATE INDEX idx_libraries_path ON libraries(path);
            "#,
            down_sql: "DROP TABLE libraries;",
        },
        Migration {
            version: 4,
            description: "Create media_items table".to_string(),
            up_sql: r#"
                CREATE TABLE media_items (
                    id TEXT PRIMARY KEY,
                    library_id TEXT NOT NULL,
                    parent_id TEXT,
                    name TEXT NOT NULL,
                    sort_name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    item_type TEXT NOT NULL,
                    media_type TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    duration INTEGER,
                    bitrate INTEGER,
                    container TEXT,
                    video_codec TEXT,
                    audio_codec TEXT,
                    width INTEGER,
                    height INTEGER,
                    aspect_ratio TEXT,
                    framerate REAL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_added DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (library_id) REFERENCES libraries(id) ON DELETE CASCADE,
                    FOREIGN KEY (parent_id) REFERENCES media_items(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_media_items_library_id ON media_items(library_id);
                CREATE INDEX idx_media_items_parent_id ON media_items(parent_id);
                CREATE INDEX idx_media_items_path ON media_items(path);
                CREATE INDEX idx_media_items_type ON media_items(item_type, media_type);
                CREATE INDEX idx_media_items_name ON media_items(sort_name);
            "#,
            down_sql: "DROP TABLE media_items;",
        },
        Migration {
            version: 5,
            description: "Create media_metadata table".to_string(),
            up_sql: r#"
                CREATE TABLE media_metadata (
                    id TEXT PRIMARY KEY,
                    media_item_id TEXT NOT NULL UNIQUE,
                    title TEXT,
                    overview TEXT,
                    tagline TEXT,
                    release_date DATETIME,
                    runtime INTEGER,
                    rating REAL,
                    vote_count INTEGER,
                    genres TEXT, -- JSON array
                    cast TEXT,   -- JSON array
                    crew TEXT,   -- JSON array
                    external_ids TEXT, -- JSON object
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (media_item_id) REFERENCES media_items(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_media_metadata_item_id ON media_metadata(media_item_id);
                CREATE INDEX idx_media_metadata_title ON media_metadata(title);
                CREATE INDEX idx_media_metadata_release_date ON media_metadata(release_date);
            "#,
            down_sql: "DROP TABLE media_metadata;",
        },
        Migration {
            version: 6,
            description: "Create playback_states table".to_string(),
            up_sql: r#"
                CREATE TABLE playback_states (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    media_item_id TEXT NOT NULL,
                    position_ticks INTEGER NOT NULL DEFAULT 0,
                    playback_method TEXT NOT NULL DEFAULT 'DirectPlay',
                    play_count INTEGER NOT NULL DEFAULT 0,
                    is_played BOOLEAN NOT NULL DEFAULT FALSE,
                    last_played DATETIME,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (media_item_id) REFERENCES media_items(id) ON DELETE CASCADE,
                    UNIQUE(user_id, media_item_id)
                );
                
                CREATE INDEX idx_playback_states_user_id ON playback_states(user_id);
                CREATE INDEX idx_playback_states_media_item_id ON playback_states(media_item_id);
                CREATE INDEX idx_playback_states_last_played ON playback_states(last_played);
            "#,
            down_sql: "DROP TABLE playback_states;",
        },
        Migration {
            version: 7,
            description: "Create api_keys table".to_string(),
            up_sql: r#"
                CREATE TABLE api_keys (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    key_hash TEXT NOT NULL UNIQUE,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_used DATETIME,
                    expires_at DATETIME,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
                CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
                CREATE INDEX idx_api_keys_active ON api_keys(is_active);
            "#,
            down_sql: "DROP TABLE api_keys;",
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_migrations() {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        // Run migrations
        run_migrations(&pool).await.unwrap();

        // Verify tables exist
        let tables = sqlx::query!(
            "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        )
        .fetch_all(&pool)
        .await
        .unwrap();

        let table_names: Vec<String> = tables.into_iter().map(|t| t.name).collect();
        
        assert!(table_names.contains(&"users".to_string()));
        assert!(table_names.contains(&"sessions".to_string()));
        assert!(table_names.contains(&"libraries".to_string()));
        assert!(table_names.contains(&"media_items".to_string()));
        assert!(table_names.contains(&"media_metadata".to_string()));
        assert!(table_names.contains(&"playback_states".to_string()));
        assert!(table_names.contains(&"api_keys".to_string()));
        assert!(table_names.contains(&"schema_migrations".to_string()));

        // Verify current version
        let version = get_current_version(&pool).await.unwrap();
        assert_eq!(version, 7);
    }
}
```

### Step 5: Implement Repository Pattern

Create `src/database/repositories/mod.rs`:

```rust
//! Repository pattern implementation for data access
//!
//! Repositories provide a clean abstraction over database operations,
//! making the code more testable and maintainable.

use async_trait::async_trait;
use anyhow::Result;

pub mod user_repository;
pub mod media_repository;
pub mod session_repository;

pub use user_repository::UserRepository;
pub use media_repository::MediaRepository;
pub use session_repository::SessionRepository;

/// Common repository trait for basic CRUD operations
#[async_trait]
pub trait Repository<T, ID> {
    async fn find_by_id(&self, id: &ID) -> Result<Option<T>>;
    async fn create(&self, entity: &T) -> Result<T>;
    async fn update(&self, entity: &T) -> Result<()>;
    async fn delete(&self, id: &ID) -> Result<()>;
}
```

Create `src/database/repositories/user_repository.rs`:

```rust
//! User repository implementation

use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use sqlx::{Pool, Sqlite};
use uuid::Uuid;

use crate::database::models::{User, CreateUserRequest, UpdateUserRequest};
use super::Repository;

pub struct UserRepository {
    pool: Pool<Sqlite>,
}

impl UserRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    /// Find user by name
    pub async fn find_by_name(&self, name: &str) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT * FROM users WHERE name = ?",
            name
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// Create a new user from request
    pub async fn create_user(&self, request: &CreateUserRequest) -> Result<User> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        // Hash password if provided
        let password_hash = if let Some(password) = &request.password {
            Some(hash_password(password)?)
        } else {
            None
        };

        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, name, password_hash, is_administrator, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            id,
            request.name,
            password_hash,
            request.is_administrator,
            now,
            now
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    /// Update user information
    pub async fn update_user(&self, id: &str, request: &UpdateUserRequest) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            r#"
            UPDATE users
            SET name = COALESCE(?, name),
                is_administrator = COALESCE(?, is_administrator),
                is_hidden = COALESCE(?, is_hidden),
                is_disabled = COALESCE(?, is_disabled),
                updated_at = ?
            WHERE id = ?
            "#,
            request.name,
            request.is_administrator,
            request.is_hidden,
            request.is_disabled,
            now,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Get all users with pagination
    pub async fn list_users(&self, limit: i64, offset: i64) -> Result<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            "SELECT * FROM users ORDER BY name LIMIT ? OFFSET ?",
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    /// Update user's last login time
    pub async fn update_last_login(&self, id: &str) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE users SET last_login_date = ?, last_activity_date = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

#[async_trait]
impl Repository<User, String> for UserRepository {
    async fn find_by_id(&self, id: &String) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT * FROM users WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    async fn create(&self, user: &User) -> Result<User> {
        let created_user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            user.id,
            user.name,
            user.password_hash,
            user.is_administrator,
            user.is_hidden,
            user.is_disabled,
            user.created_at,
            user.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(created_user)
    }

    async fn update(&self, user: &User) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE users
            SET name = ?, password_hash = ?, is_administrator = ?, is_hidden = ?, is_disabled = ?, updated_at = ?
            WHERE id = ?
            "#,
            user.name,
            user.password_hash,
            user.is_administrator,
            user.is_hidden,
            user.is_disabled,
            user.updated_at,
            user.id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn delete(&self, id: &String) -> Result<()> {
        sqlx::query!("DELETE FROM users WHERE id = ?", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

/// Hash a password using bcrypt
fn hash_password(password: &str) -> Result<String> {
    use bcrypt::{hash, DEFAULT_COST};
    let hashed = hash(password, DEFAULT_COST)?;
    Ok(hashed)
}

/// Verify a password against a hash
pub fn verify_password(password: &str, hash: &str) -> Result<bool> {
    use bcrypt::verify;
    let is_valid = verify(password, hash)?;
    Ok(is_valid)
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;
    use crate::database::migrations::run_migrations;

    async fn setup_test_db() -> Pool<Sqlite> {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");

        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        run_migrations(&pool).await.unwrap();
        pool
    }

    #[tokio::test]
    async fn test_user_crud() {
        let pool = setup_test_db().await;
        let repo = UserRepository::new(pool);

        // Create user
        let request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password123".to_string()),
            is_administrator: false,
        };

        let user = repo.create_user(&request).await.unwrap();
        assert_eq!(user.name, "testuser");
        assert!(!user.is_administrator);
        assert!(user.password_hash.is_some());

        // Find by ID
        let found_user = repo.find_by_id(&user.id).await.unwrap();
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().name, "testuser");

        // Find by name
        let found_by_name = repo.find_by_name("testuser").await.unwrap();
        assert!(found_by_name.is_some());

        // Update user
        let update_request = UpdateUserRequest {
            name: Some("updateduser".to_string()),
            is_administrator: Some(true),
            is_hidden: None,
            is_disabled: None,
        };

        repo.update_user(&user.id, &update_request).await.unwrap();

        let updated_user = repo.find_by_id(&user.id).await.unwrap().unwrap();
        assert_eq!(updated_user.name, "updateduser");
        assert!(updated_user.is_administrator);

        // Delete user
        repo.delete(&user.id).await.unwrap();
        let deleted_user = repo.find_by_id(&user.id).await.unwrap();
        assert!(deleted_user.is_none());
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password_123";
        let hash = hash_password(password).unwrap();

        assert!(verify_password(password, &hash).unwrap());
        assert!(!verify_password("wrong_password", &hash).unwrap());
    }
}
```

## 🧪 Testing Our Database Layer

### Step 6: Update Main Application

Update `src/main.rs` to initialize the database:

```rust
use anyhow::Result;
use tracing::{info, warn};
use tulip_media::{
    cli::Cli,
    config::{apply_cli_overrides, ensure_jwt_secret, ConfigLoader},
    database::Database,
    utils,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse_args();

    // Validate arguments
    cli.validate()?;

    // Initialize logging
    utils::init_logging(cli.debug)?;

    // Load configuration with hierarchical loading
    let config_loader = ConfigLoader::new().with_config_path(&cli.config);
    let mut config = config_loader.load()?;

    // Apply CLI overrides
    config = apply_cli_overrides(config, &cli);

    // Ensure JWT secret is available
    ensure_jwt_secret(&mut config)?;

    // Initialize database
    let database = Database::new(&config.database).await?;
    database.migrate().await?;

    // Log startup information
    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Configuration loaded from: {:?}", cli.config);
    info!("Database initialized: {:?}", config.database.path);
    info!("Server will bind to: {}:{}", config.server.bind_address, config.server.port);

    // Log configuration and database summary
    log_configuration_summary(&config);
    log_database_summary(&database).await?;

    // Display system information
    display_system_info(&config);

    // TODO: In the next chapter, we'll create the HTTP server
    info!("Database layer ready! Next: Chapter 4 - HTTP Server");

    Ok(())
}

/// Log database statistics
async fn log_database_summary(database: &Database) -> Result<()> {
    let stats = database.get_stats().await?;

    info!("Database Summary:");
    info!("  Users: {}", stats.user_count);
    info!("  Media items: {}", stats.media_count);
    info!("  Active sessions: {}", stats.active_sessions);
    info!("  Libraries: {}", stats.library_count);

    Ok(())
}

// ... rest of the functions from previous chapter
```

## 🎯 Key Concepts Explained

### SQLite Optimization for ARM64

We configure SQLite with ARM64-specific optimizations:

```rust
.pragma("cache_size", "-131072") // 128MB cache - adjusted for ARM64
.pragma("mmap_size", "134217728") // 128MB mmap - adjusted for ARM64
.pragma("page_size", "4096") // Optimal page size for ARM64
```

These settings balance performance with memory constraints typical of ARM64 devices.

### Repository Pattern Benefits

The repository pattern provides:
- **Abstraction**: Clean separation between business logic and data access
- **Testability**: Easy to mock for unit testing
- **Maintainability**: Centralized data access logic
- **Type Safety**: Compile-time query checking with SQLx

### Migration System Design

Our migration system ensures:
- **Version Control**: Track schema changes over time
- **Rollback Support**: Ability to undo changes if needed
- **Atomic Operations**: Each migration runs in a transaction
- **Idempotent**: Safe to run multiple times

## 🔍 What's Next?

In **Chapter 4: Basic HTTP Server**, we'll:
- Set up Axum web server with middleware
- Create health check and system info endpoints
- Implement request/response patterns
- Add graceful shutdown handling

## 📚 Additional Resources

- [SQLx Documentation](https://docs.rs/sqlx/) - Async SQL toolkit
- [SQLite Optimization](https://www.sqlite.org/optoverview.html) - Performance tuning
- [Repository Pattern](https://martinfowler.com/eaaCatalog/repository.html) - Design pattern explanation
- [Database Migrations](https://en.wikipedia.org/wiki/Schema_migration) - Migration concepts

---

**Checkpoint**: You now have a robust database layer with migrations, models, and repositories optimized for ARM64. Ready for [Chapter 4: Basic HTTP Server](../chapter-04-http-server/)?

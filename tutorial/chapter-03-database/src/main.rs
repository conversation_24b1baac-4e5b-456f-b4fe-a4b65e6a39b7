use anyhow::Result;
use tracing::{info, warn};
use tulip_media::{
    cli::Cli,
    config::{apply_cli_overrides, ensure_jwt_secret, ConfigLoader},
    database::Database,
    utils,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse_args();
    
    // Validate arguments
    cli.validate()?;

    // Initialize logging
    utils::init_logging(cli.debug)?;

    // Load configuration with hierarchical loading
    let config_loader = ConfigLoader::new().with_config_path(&cli.config);
    let mut config = config_loader.load()?;

    // Apply CLI overrides
    config = apply_cli_overrides(config, &cli);

    // Ensure JWT secret is available
    ensure_jwt_secret(&mut config)?;

    // Initialize database
    let database = Database::new(&config.database).await?;
    database.migrate().await?;

    // Log startup information
    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Configuration loaded from: {:?}", cli.config);
    info!("Database initialized: {:?}", config.database.path);
    info!("Server will bind to: {}:{}", config.server.bind_address, config.server.port);
    
    // Log configuration and database summary
    log_configuration_summary(&config);
    log_database_summary(&database).await?;

    // Display system information
    display_system_info(&config);

    // TODO: In the next chapter, we'll create the HTTP server
    info!("Database layer ready! Next: Chapter 4 - HTTP Server");

    Ok(())
}

/// Log database statistics
async fn log_database_summary(database: &Database) -> Result<()> {
    let stats = database.get_stats().await?;
    
    info!("Database Summary:");
    info!("  Users: {}", stats.user_count);
    info!("  Media items: {}", stats.media_count);
    info!("  Active sessions: {}", stats.active_sessions);
    info!("  Libraries: {}", stats.library_count);
    
    Ok(())
}

/// Log a summary of the loaded configuration
fn log_configuration_summary(config: &tulip_media::config::Config) {
    info!("Configuration Summary:");
    info!("  Server: {}:{}", config.server.bind_address, config.server.port);
    info!("  Database: {:?}", config.database.path);
    info!("  Media libraries: {} configured", config.media.library_paths.len());
    info!("  Transcoding: {}", if config.streaming.enable_transcoding { "enabled" } else { "disabled" });
    info!("  ARM64 optimizations: {}", if config.performance.arm64_optimizations { "enabled" } else { "disabled" });
    
    if config.media.library_paths.is_empty() {
        warn!("No media library paths configured - add paths to config.toml or use --media-dirs");
    }
}

/// Display system information with configuration context
fn display_system_info(config: &tulip_media::config::Config) {
    info!("System Information:");
    info!("  Architecture: {}", std::env::consts::ARCH);
    info!("  OS: {}", std::env::consts::OS);
    info!("  CPU cores: {}", utils::get_cpu_count());
    info!("  Optimal worker threads: {}", utils::get_optimal_worker_threads());
    
    // Display memory information if available
    if let Ok(memory) = get_memory_info() {
        info!("  Available memory: {}", utils::format_bytes(memory));
        
        // Warn if cache size is too large for available memory
        let cache_bytes = config.performance.cache_size_mb * 1024 * 1024;
        if cache_bytes > memory / 2 {
            warn!("Cache size ({}) is more than 50% of available memory", 
                  utils::format_bytes(cache_bytes));
        }
    }
    
    // ARM64 specific information
    if utils::is_arm64() {
        info!("  ARM64 optimizations: enabled");
        info!("  Max concurrent streams: {} (ARM64 optimized)", config.streaming.max_concurrent_streams);
        info!("  Cache size: {} MB (ARM64 optimized)", config.performance.cache_size_mb);
    }
}

/// Get available system memory (Linux-specific)
fn get_memory_info() -> Result<u64> {
    #[cfg(target_os = "linux")]
    {
        let meminfo = std::fs::read_to_string("/proc/meminfo")?;
        for line in meminfo.lines() {
            if line.starts_with("MemAvailable:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    let kb: u64 = parts[1].parse()?;
                    return Ok(kb * 1024); // Convert KB to bytes
                }
            }
        }
    }
    
    // Fallback for non-Linux systems
    Ok(0)
}

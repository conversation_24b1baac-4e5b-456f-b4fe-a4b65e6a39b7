//! Database migration system for Tulip Media Server
//!
//! This module handles database schema creation and updates with proper
//! versioning and rollback support.

use anyhow::Result;
use sqlx::{Pool, Sqlite};
use tracing::{debug, info};

/// Run all pending migrations
pub async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // Create migrations table if it doesn't exist
    create_migrations_table(pool).await?;

    // Get current schema version
    let current_version = get_current_version(pool).await?;
    info!("Current database schema version: {}", current_version);

    // Run migrations in order
    let migrations = get_migrations();
    for migration in migrations {
        if migration.version > current_version {
            info!("Running migration {}: {}", migration.version, migration.description);
            run_migration(pool, &migration).await?;
        }
    }

    Ok(())
}

/// Database migration definition
#[derive(Debug)]
struct Migration {
    version: i32,
    description: String,
    up_sql: &'static str,
    down_sql: &'static str,
}

/// Create the migrations tracking table
async fn create_migrations_table(pool: &Pool<Sqlite>) -> Result<()> {
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version INTEGER PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await?;

    Ok(())
}

/// Get the current schema version
async fn get_current_version(pool: &Pool<Sqlite>) -> Result<i32> {
    let result = sqlx::query!(
        "SELECT MAX(version) as version FROM schema_migrations"
    )
    .fetch_one(pool)
    .await?;

    Ok(result.version.unwrap_or(0))
}

/// Run a single migration
async fn run_migration(pool: &Pool<Sqlite>, migration: &Migration) -> Result<()> {
    // Start transaction
    let mut tx = pool.begin().await?;

    // Execute migration SQL
    sqlx::query(&migration.up_sql).execute(&mut *tx).await?;

    // Record migration in tracking table
    sqlx::query!(
        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
        migration.version,
        migration.description
    )
    .execute(&mut *tx)
    .await?;

    // Commit transaction
    tx.commit().await?;

    debug!("Migration {} completed successfully", migration.version);
    Ok(())
}

/// Get all available migrations in order
fn get_migrations() -> Vec<Migration> {
    vec![
        Migration {
            version: 1,
            description: "Create users table".to_string(),
            up_sql: r#"
                CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL UNIQUE,
                    password_hash TEXT,
                    is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
                    is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
                    is_disabled BOOLEAN NOT NULL DEFAULT FALSE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_login_date DATETIME,
                    last_activity_date DATETIME
                );
                
                CREATE INDEX idx_users_name ON users(name);
                CREATE INDEX idx_users_active ON users(is_disabled, is_hidden);
            "#,
            down_sql: "DROP TABLE users;",
        },
        Migration {
            version: 2,
            description: "Create sessions table".to_string(),
            up_sql: r#"
                CREATE TABLE sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    device_id TEXT NOT NULL,
                    device_name TEXT NOT NULL,
                    client TEXT NOT NULL,
                    version TEXT NOT NULL,
                    access_token TEXT NOT NULL UNIQUE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_activity DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_sessions_user_id ON sessions(user_id);
                CREATE INDEX idx_sessions_access_token ON sessions(access_token);
                CREATE INDEX idx_sessions_active ON sessions(is_active, last_activity);
            "#,
            down_sql: "DROP TABLE sessions;",
        },
        Migration {
            version: 3,
            description: "Create libraries table".to_string(),
            up_sql: r#"
                CREATE TABLE libraries (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    library_type TEXT NOT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_scan DATETIME
                );
                
                CREATE INDEX idx_libraries_type ON libraries(library_type);
                CREATE INDEX idx_libraries_path ON libraries(path);
            "#,
            down_sql: "DROP TABLE libraries;",
        },
        Migration {
            version: 4,
            description: "Create media_items table".to_string(),
            up_sql: r#"
                CREATE TABLE media_items (
                    id TEXT PRIMARY KEY,
                    library_id TEXT NOT NULL,
                    parent_id TEXT,
                    name TEXT NOT NULL,
                    sort_name TEXT NOT NULL,
                    path TEXT NOT NULL UNIQUE,
                    item_type TEXT NOT NULL,
                    media_type TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    duration INTEGER,
                    bitrate INTEGER,
                    container TEXT,
                    video_codec TEXT,
                    audio_codec TEXT,
                    width INTEGER,
                    height INTEGER,
                    aspect_ratio TEXT,
                    framerate REAL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_added DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    date_modified DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (library_id) REFERENCES libraries(id) ON DELETE CASCADE,
                    FOREIGN KEY (parent_id) REFERENCES media_items(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_media_items_library_id ON media_items(library_id);
                CREATE INDEX idx_media_items_parent_id ON media_items(parent_id);
                CREATE INDEX idx_media_items_path ON media_items(path);
                CREATE INDEX idx_media_items_type ON media_items(item_type, media_type);
                CREATE INDEX idx_media_items_name ON media_items(sort_name);
            "#,
            down_sql: "DROP TABLE media_items;",
        },
        Migration {
            version: 5,
            description: "Create media_metadata table".to_string(),
            up_sql: r#"
                CREATE TABLE media_metadata (
                    id TEXT PRIMARY KEY,
                    media_item_id TEXT NOT NULL UNIQUE,
                    title TEXT,
                    overview TEXT,
                    tagline TEXT,
                    release_date DATETIME,
                    runtime INTEGER,
                    rating REAL,
                    vote_count INTEGER,
                    genres TEXT, -- JSON array
                    cast TEXT,   -- JSON array
                    crew TEXT,   -- JSON array
                    external_ids TEXT, -- JSON object
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (media_item_id) REFERENCES media_items(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_media_metadata_item_id ON media_metadata(media_item_id);
                CREATE INDEX idx_media_metadata_title ON media_metadata(title);
                CREATE INDEX idx_media_metadata_release_date ON media_metadata(release_date);
            "#,
            down_sql: "DROP TABLE media_metadata;",
        },
        Migration {
            version: 6,
            description: "Create playback_states table".to_string(),
            up_sql: r#"
                CREATE TABLE playback_states (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    media_item_id TEXT NOT NULL,
                    position_ticks INTEGER NOT NULL DEFAULT 0,
                    playback_method TEXT NOT NULL DEFAULT 'DirectPlay',
                    play_count INTEGER NOT NULL DEFAULT 0,
                    is_played BOOLEAN NOT NULL DEFAULT FALSE,
                    last_played DATETIME,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (media_item_id) REFERENCES media_items(id) ON DELETE CASCADE,
                    UNIQUE(user_id, media_item_id)
                );
                
                CREATE INDEX idx_playback_states_user_id ON playback_states(user_id);
                CREATE INDEX idx_playback_states_media_item_id ON playback_states(media_item_id);
                CREATE INDEX idx_playback_states_last_played ON playback_states(last_played);
            "#,
            down_sql: "DROP TABLE playback_states;",
        },
        Migration {
            version: 7,
            description: "Create api_keys table".to_string(),
            up_sql: r#"
                CREATE TABLE api_keys (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    key_hash TEXT NOT NULL UNIQUE,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_used DATETIME,
                    expires_at DATETIME,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                );
                
                CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
                CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
                CREATE INDEX idx_api_keys_active ON api_keys(is_active);
            "#,
            down_sql: "DROP TABLE api_keys;",
        },
    ]
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_migrations() {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        // Run migrations
        run_migrations(&pool).await.unwrap();

        // Verify tables exist
        let tables = sqlx::query!(
            "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        )
        .fetch_all(&pool)
        .await
        .unwrap();

        let table_names: Vec<String> = tables.into_iter().map(|t| t.name).collect();
        
        assert!(table_names.contains(&"users".to_string()));
        assert!(table_names.contains(&"sessions".to_string()));
        assert!(table_names.contains(&"libraries".to_string()));
        assert!(table_names.contains(&"media_items".to_string()));
        assert!(table_names.contains(&"media_metadata".to_string()));
        assert!(table_names.contains(&"playback_states".to_string()));
        assert!(table_names.contains(&"api_keys".to_string()));
        assert!(table_names.contains(&"schema_migrations".to_string()));

        // Verify current version
        let version = get_current_version(&pool).await.unwrap();
        assert_eq!(version, 7);
    }
}

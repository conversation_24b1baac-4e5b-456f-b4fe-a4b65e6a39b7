//! Tulip Media Server - A high-performance Jellyfin-compatible media server
//!
//! This library provides all the core functionality for the Tulip Media Server,
//! optimized for ARM64 devices like the Nano Pi M4 V2.

pub mod auth;
pub mod cli;
pub mod config;
pub mod database;
pub mod media;
pub mod server;
pub mod utils;

// Re-export commonly used types for convenience
pub use cli::Cli;
pub use config::Config;
pub use database::Database;
pub use server::Server;

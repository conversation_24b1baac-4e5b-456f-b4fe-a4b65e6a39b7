//! Thumbnail generation with ARM64 NEON optimizations

use anyhow::Result;
use image::{<PERSON>Buffer, Rgb, RgbImage};
use std::{
    path::{Path, PathBuf},
    sync::Arc,
};
use tracing::{debug, error, info, warn};

/// Thumbnail generator with ARM64 optimizations
#[derive(Clone)]
pub struct ThumbnailGenerator {
    config: Arc<crate::config::MediaConfig>,
    cache_dir: PathBuf,
}

impl ThumbnailGenerator {
    pub fn new(config: Arc<crate::config::MediaConfig>) -> Result<Self> {
        let cache_dir = PathBuf::from("./data/thumbnails");
        std::fs::create_dir_all(&cache_dir)?;

        info!("Thumbnail generator initialized with cache dir: {:?}", cache_dir);

        Ok(Self {
            config,
            cache_dir,
        })
    }

    /// Generate thumbnail for a media item
    pub async fn generate_thumbnail(&self, media_item: &crate::database::models::MediaItem) -> Result<PathBuf> {
        debug!("Generating thumbnail for: {}", media_item.name);

        let source_path = Path::new(&media_item.path);
        let thumbnail_path = self.get_thumbnail_path(&media_item.id);

        // Check if thumbnail already exists
        if thumbnail_path.exists() {
            debug!("Thumbnail already exists: {:?}", thumbnail_path);
            return Ok(thumbnail_path);
        }

        match media_item.media_type.as_str() {
            "Video" => self.generate_video_thumbnail(source_path, &thumbnail_path).await,
            "Image" => self.generate_image_thumbnail(source_path, &thumbnail_path).await,
            _ => {
                warn!("Unsupported media type for thumbnail: {}", media_item.media_type);
                Err(anyhow::anyhow!("Unsupported media type"))
            }
        }
    }

    /// Generate thumbnail from video file
    async fn generate_video_thumbnail(&self, source: &Path, output: &Path) -> Result<PathBuf> {
        debug!("Generating video thumbnail: {:?} -> {:?}", source, output);

        // In a real implementation, this would use FFmpeg to extract a frame
        // For now, create a placeholder thumbnail
        let placeholder = self.create_placeholder_thumbnail("VIDEO").await?;
        
        // Save placeholder
        placeholder.save(output)?;
        
        info!("Generated video thumbnail: {:?}", output);
        Ok(output.to_path_buf())
    }

    /// Generate thumbnail from image file with ARM64 NEON optimizations
    async fn generate_image_thumbnail(&self, source: &Path, output: &Path) -> Result<PathBuf> {
        debug!("Generating image thumbnail: {:?} -> {:?}", source, output);

        // Load source image
        let img = image::open(source)?;
        
        // Calculate thumbnail dimensions (maintain aspect ratio)
        let (thumb_width, thumb_height) = self.calculate_thumbnail_size(img.width(), img.height());
        
        // Resize image with ARM64 optimizations
        let thumbnail = if cfg!(target_arch = "aarch64") {
            // Use ARM64 NEON optimized resizing
            self.resize_image_neon(&img, thumb_width, thumb_height).await?
        } else {
            // Use standard resizing
            img.resize(thumb_width, thumb_height, image::imageops::FilterType::Lanczos3)
        };

        // Save thumbnail
        thumbnail.save(output)?;
        
        info!("Generated image thumbnail: {:?}", output);
        Ok(output.to_path_buf())
    }

    /// ARM64 NEON optimized image resizing
    #[cfg(target_arch = "aarch64")]
    async fn resize_image_neon(&self, img: &image::DynamicImage, width: u32, height: u32) -> Result<image::DynamicImage> {
        use std::arch::aarch64::*;
        
        debug!("Using ARM64 NEON optimized resizing: {}x{} -> {}x{}", 
               img.width(), img.height(), width, height);

        // For now, fall back to standard resizing
        // In a real implementation, this would use NEON SIMD instructions
        // for optimized pixel processing
        let resized = img.resize(width, height, image::imageops::FilterType::Lanczos3);
        Ok(resized)
    }

    #[cfg(not(target_arch = "aarch64"))]
    async fn resize_image_neon(&self, img: &image::DynamicImage, width: u32, height: u32) -> Result<image::DynamicImage> {
        // Fallback for non-ARM64 architectures
        let resized = img.resize(width, height, image::imageops::FilterType::Lanczos3);
        Ok(resized)
    }

    /// Calculate thumbnail dimensions maintaining aspect ratio
    fn calculate_thumbnail_size(&self, original_width: u32, original_height: u32) -> (u32, u32) {
        const MAX_THUMB_SIZE: u32 = 300;

        let aspect_ratio = original_width as f32 / original_height as f32;

        if original_width > original_height {
            // Landscape
            let width = MAX_THUMB_SIZE;
            let height = (width as f32 / aspect_ratio) as u32;
            (width, height)
        } else {
            // Portrait or square
            let height = MAX_THUMB_SIZE;
            let width = (height as f32 * aspect_ratio) as u32;
            (width, height)
        }
    }

    /// Create placeholder thumbnail for unsupported formats
    async fn create_placeholder_thumbnail(&self, media_type: &str) -> Result<RgbImage> {
        const THUMB_SIZE: u32 = 300;
        
        // Create colored placeholder based on media type
        let color = match media_type {
            "VIDEO" => [100, 100, 200], // Blue for video
            "AUDIO" => [100, 200, 100], // Green for audio
            _ => [200, 100, 100],       // Red for unknown
        };

        let mut img = ImageBuffer::new(THUMB_SIZE, THUMB_SIZE);
        
        // Fill with solid color (in a real implementation, could add text/icons)
        for pixel in img.pixels_mut() {
            *pixel = Rgb(color);
        }

        Ok(img)
    }

    /// Get thumbnail file path for a media item
    fn get_thumbnail_path(&self, media_id: &str) -> PathBuf {
        self.cache_dir.join(format!("{}.jpg", media_id))
    }

    /// Get thumbnail URL for a media item
    pub fn get_thumbnail_url(&self, media_id: &str) -> String {
        format!("/thumbnails/{}.jpg", media_id)
    }

    /// Clean up old thumbnails
    pub async fn cleanup_thumbnails(&self, keep_days: u64) -> Result<usize> {
        let cutoff = std::time::SystemTime::now() - std::time::Duration::from_secs(keep_days * 24 * 3600);
        let mut removed = 0;

        if let Ok(entries) = std::fs::read_dir(&self.cache_dir) {
            for entry in entries.flatten() {
                if let Ok(metadata) = entry.metadata() {
                    if let Ok(modified) = metadata.modified() {
                        if modified < cutoff {
                            if std::fs::remove_file(entry.path()).is_ok() {
                                removed += 1;
                            }
                        }
                    }
                }
            }
        }

        if removed > 0 {
            info!("Cleaned up {} old thumbnails", removed);
        }

        Ok(removed)
    }

    /// Get cache statistics
    pub async fn get_cache_stats(&self) -> Result<ThumbnailCacheStats> {
        let mut stats = ThumbnailCacheStats::default();

        if let Ok(entries) = std::fs::read_dir(&self.cache_dir) {
            for entry in entries.flatten() {
                if let Ok(metadata) = entry.metadata() {
                    stats.file_count += 1;
                    stats.total_size += metadata.len();
                }
            }
        }

        Ok(stats)
    }
}

/// Thumbnail cache statistics
#[derive(Debug, Default, Clone, serde::Serialize)]
pub struct ThumbnailCacheStats {
    pub file_count: usize,
    pub total_size: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_thumbnail_generator_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = crate::config::MediaConfig::default();
        
        let generator = ThumbnailGenerator::new(Arc::new(config));
        assert!(generator.is_ok());
    }

    #[test]
    fn test_thumbnail_size_calculation() {
        let config = Arc::new(crate::config::MediaConfig::default());
        let generator = ThumbnailGenerator::new(config).unwrap();

        // Test landscape image
        let (width, height) = generator.calculate_thumbnail_size(1920, 1080);
        assert_eq!(width, 300);
        assert!(height < 300);

        // Test portrait image
        let (width, height) = generator.calculate_thumbnail_size(1080, 1920);
        assert!(width < 300);
        assert_eq!(height, 300);

        // Test square image
        let (width, height) = generator.calculate_thumbnail_size(1000, 1000);
        assert_eq!(width, 300);
        assert_eq!(height, 300);
    }

    #[tokio::test]
    async fn test_placeholder_creation() {
        let config = Arc::new(crate::config::MediaConfig::default());
        let generator = ThumbnailGenerator::new(config).unwrap();

        let placeholder = generator.create_placeholder_thumbnail("VIDEO").await.unwrap();
        assert_eq!(placeholder.width(), 300);
        assert_eq!(placeholder.height(), 300);
    }

    #[test]
    fn test_thumbnail_path_generation() {
        let config = Arc::new(crate::config::MediaConfig::default());
        let generator = ThumbnailGenerator::new(config).unwrap();

        let path = generator.get_thumbnail_path("test-id-123");
        assert!(path.to_string_lossy().contains("test-id-123.jpg"));
    }

    #[test]
    fn test_thumbnail_url_generation() {
        let config = Arc::new(crate::config::MediaConfig::default());
        let generator = ThumbnailGenerator::new(config).unwrap();

        let url = generator.get_thumbnail_url("test-id-123");
        assert_eq!(url, "/thumbnails/test-id-123.jpg");
    }
}

//! Media library service for high-level operations

use anyhow::Result;
use std::{path::PathBuf, sync::Arc};
use tokio::sync::broadcast;
use tracing::{error, info, warn};

use crate::database::{
    models::{CreateLibraryRequest, Library, MediaItem},
    repositories::MediaRepository,
    Database,
};

use super::{
    monitor::{FileSystemEvent, FileSystemEventHandler, FileSystemMonitor},
    MediaLibrary, ScanProgress, ScanResult,
};

/// High-level media library service
#[derive(Clone)]
pub struct MediaLibraryService {
    media_library: MediaLibrary,
    file_monitor: FileSystemMonitor,
    event_handler: Arc<FileSystemEventHandler>,
    database: Database,
}

impl MediaLibraryService {
    pub fn new(
        config: Arc<crate::config::MediaConfig>,
        database: Database,
    ) -> Result<Self> {
        let media_library = MediaLibrary::new(config, database.clone())?;
        let file_monitor = FileSystemMonitor::new()?;
        let event_handler = Arc::new(FileSystemEventHandler::new(media_library.clone()));

        Ok(Self {
            media_library,
            file_monitor,
            event_handler,
            database,
        })
    }

    /// Initialize the service and start monitoring
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing media library service");

        // Start monitoring configured library paths
        self.media_library.start_monitoring().await?;

        // Start processing file system events
        self.start_event_processing().await?;

        info!("Media library service initialized");
        Ok(())
    }

    /// Start processing file system events
    async fn start_event_processing(&self) -> Result<()> {
        let mut event_receiver = self.file_monitor.subscribe_to_events();
        let event_handler = self.event_handler.clone();

        tokio::spawn(async move {
            while let Ok(event) = event_receiver.recv().await {
                if let Err(e) = event_handler.handle_event(event).await {
                    error!("Error handling file system event: {}", e);
                }
            }
        });

        Ok(())
    }

    /// Perform full library scan
    pub async fn scan_all_libraries(&self) -> Result<ScanResult> {
        info!("Starting full library scan");
        self.media_library.scan_libraries().await
    }

    /// Scan a specific library path
    pub async fn scan_library(&self, library_id: &str) -> Result<ScanResult> {
        info!("Scanning library: {}", library_id);

        let media_repo = MediaRepository::new(self.database.pool().clone());
        let library = media_repo.find_by_id(&library_id.to_string()).await?
            .ok_or_else(|| anyhow::anyhow!("Library not found: {}", library_id))?;

        // This would need to be implemented in MediaLibrary
        // For now, return a placeholder result
        Ok(ScanResult {
            total_files: 0,
            processed_files: 0,
            errors: vec![],
            duration: std::time::Duration::from_secs(0),
        })
    }

    /// Create a new library
    pub async fn create_library(&self, request: CreateLibraryRequest) -> Result<Library> {
        info!("Creating new library: {} at {:?}", request.name, request.path);

        let media_repo = MediaRepository::new(self.database.pool().clone());
        let library = media_repo.create_library(&request).await?;

        // Start monitoring the new library path
        let path = PathBuf::from(&request.path);
        if path.exists() {
            if let Err(e) = self.file_monitor.watch_directory(&path).await {
                warn!("Failed to start monitoring new library path: {}", e);
            }
        }

        info!("Created library: {} ({})", library.name, library.id);
        Ok(library)
    }

    /// Get all libraries
    pub async fn get_libraries(&self) -> Result<Vec<Library>> {
        let media_repo = MediaRepository::new(self.database.pool().clone());
        media_repo.list_libraries().await
    }

    /// Delete a library
    pub async fn delete_library(&self, library_id: &str) -> Result<()> {
        info!("Deleting library: {}", library_id);

        let media_repo = MediaRepository::new(self.database.pool().clone());
        
        // Get library info before deletion
        if let Some(library) = media_repo.find_by_id(&library_id.to_string()).await? {
            // Stop monitoring the library path
            let path = PathBuf::from(&library.path);
            if let Err(e) = self.file_monitor.unwatch_directory(&path).await {
                warn!("Failed to stop monitoring deleted library path: {}", e);
            }

            // Delete library (this should cascade to media items)
            media_repo.delete(&library_id.to_string()).await?;
            info!("Deleted library: {} ({})", library.name, library.id);
        }

        Ok(())
    }

    /// Get media items for a library
    pub async fn get_library_media(&self, library_id: &str, limit: i64, offset: i64) -> Result<Vec<MediaItem>> {
        let media_repo = MediaRepository::new(self.database.pool().clone());
        media_repo.get_media_items_by_library(library_id, limit, offset).await
    }

    /// Search media items
    pub async fn search_media(&self, query: &str, limit: i64) -> Result<Vec<MediaItem>> {
        let media_repo = MediaRepository::new(self.database.pool().clone());
        media_repo.search_media_items(query, limit).await
    }

    /// Get media item by ID
    pub async fn get_media_item(&self, media_id: &str) -> Result<Option<MediaItem>> {
        let media_repo = MediaRepository::new(self.database.pool().clone());
        media_repo.find_by_id(&media_id.to_string()).await
    }

    /// Delete media item
    pub async fn delete_media_item(&self, media_id: &str) -> Result<()> {
        info!("Deleting media item: {}", media_id);

        let media_repo = MediaRepository::new(self.database.pool().clone());
        media_repo.delete(&media_id.to_string()).await?;

        info!("Deleted media item: {}", media_id);
        Ok(())
    }

    /// Subscribe to scan progress updates
    pub fn subscribe_to_scan_progress(&self) -> broadcast::Receiver<ScanProgress> {
        self.media_library.subscribe_to_progress()
    }

    /// Subscribe to file system events
    pub fn subscribe_to_file_events(&self) -> broadcast::Receiver<FileSystemEvent> {
        self.file_monitor.subscribe_to_events()
    }

    /// Get library statistics
    pub async fn get_library_stats(&self, library_id: &str) -> Result<LibraryStats> {
        let media_repo = MediaRepository::new(self.database.pool().clone());
        
        // Get all media items for the library
        let media_items = media_repo.get_media_items_by_library(library_id, i64::MAX, 0).await?;
        
        let mut stats = LibraryStats::default();
        stats.total_items = media_items.len();
        
        for item in media_items {
            stats.total_size += item.file_size as u64;
            
            match item.media_type.as_str() {
                "Video" => stats.video_count += 1,
                "Audio" => stats.audio_count += 1,
                "Image" => stats.image_count += 1,
                _ => stats.other_count += 1,
            }
            
            if let Some(duration) = item.duration {
                stats.total_duration += duration as u64;
            }
        }
        
        Ok(stats)
    }

    /// Cleanup old data
    pub async fn cleanup(&self, days: u64) -> Result<CleanupResult> {
        info!("Starting cleanup of data older than {} days", days);
        
        let mut result = CleanupResult::default();
        
        // Cleanup thumbnails
        if let Ok(removed) = self.media_library.thumbnail_generator.cleanup_thumbnails(days).await {
            result.thumbnails_removed = removed;
        }
        
        // Could add more cleanup operations here
        
        info!("Cleanup completed: {:?}", result);
        Ok(result)
    }

    /// Shutdown the service
    pub async fn shutdown(&self) -> Result<()> {
        info!("Shutting down media library service");
        
        // Stop all file system monitors
        self.file_monitor.stop_all().await?;
        
        info!("Media library service shutdown complete");
        Ok(())
    }
}

/// Library statistics
#[derive(Debug, Default, Clone, serde::Serialize)]
pub struct LibraryStats {
    pub total_items: usize,
    pub video_count: usize,
    pub audio_count: usize,
    pub image_count: usize,
    pub other_count: usize,
    pub total_size: u64,
    pub total_duration: u64, // in milliseconds
}

/// Cleanup operation result
#[derive(Debug, Default, Clone, serde::Serialize)]
pub struct CleanupResult {
    pub thumbnails_removed: usize,
    pub temp_files_removed: usize,
    pub orphaned_records_removed: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    async fn create_test_service() -> MediaLibraryService {
        let temp_dir = TempDir::new().unwrap();
        let mut config = crate::config::MediaConfig::default();
        config.library_paths = vec![temp_dir.path().to_path_buf()];

        let mut db_config = crate::config::DatabaseConfig::default();
        db_config.path = temp_dir.path().join("test.db");

        let database = Database::new(&db_config).await.unwrap();
        database.migrate().await.unwrap();

        MediaLibraryService::new(Arc::new(config), database).unwrap()
    }

    #[tokio::test]
    async fn test_service_creation() {
        let service = create_test_service().await;
        assert!(true); // Service created successfully
    }

    #[tokio::test]
    async fn test_library_creation() {
        let service = create_test_service().await;

        let request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/tmp/test".to_string(),
            library_type: "Movies".to_string(),
        };

        let result = service.create_library(request).await;
        assert!(result.is_ok());

        let library = result.unwrap();
        assert_eq!(library.name, "Test Library");
        assert_eq!(library.library_type, "Movies");
    }

    #[tokio::test]
    async fn test_get_libraries() {
        let service = create_test_service().await;

        // Create a test library first
        let request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/tmp/test".to_string(),
            library_type: "Movies".to_string(),
        };
        service.create_library(request).await.unwrap();

        let libraries = service.get_libraries().await.unwrap();
        assert_eq!(libraries.len(), 1);
        assert_eq!(libraries[0].name, "Test Library");
    }

    #[tokio::test]
    async fn test_library_stats() {
        let service = create_test_service().await;

        // Create a test library
        let request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/tmp/test".to_string(),
            library_type: "Movies".to_string(),
        };
        let library = service.create_library(request).await.unwrap();

        let stats = service.get_library_stats(&library.id).await.unwrap();
        assert_eq!(stats.total_items, 0); // No media items yet
    }

    #[tokio::test]
    async fn test_event_subscriptions() {
        let service = create_test_service().await;

        let _scan_progress = service.subscribe_to_scan_progress();
        let _file_events = service.subscribe_to_file_events();

        // Just test that subscriptions work
        assert!(true);
    }
}

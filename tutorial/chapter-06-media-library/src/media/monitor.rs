//! File system monitoring for real-time library updates

use anyhow::Result;
use notify::{Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::sync::{broadcast, RwLock};
use tracing::{debug, error, info, warn};

/// File system monitor for detecting changes in media libraries
#[derive(Clone)]
pub struct FileSystemMonitor {
    watchers: Arc<RwLock<HashMap<PathBuf, RecommendedWatcher>>>,
    event_tx: broadcast::Sender<FileSystemEvent>,
}

impl FileSystemMonitor {
    pub fn new() -> Result<Self> {
        let (event_tx, _) = broadcast::channel(1000);
        
        Ok(Self {
            watchers: Arc::new(RwLock::new(HashMap::new())),
            event_tx,
        })
    }

    /// Start watching a directory for changes
    pub async fn watch_directory(&self, path: &Path) -> Result<()> {
        let path = path.to_path_buf();
        let event_tx = self.event_tx.clone();
        
        info!("Starting file system monitoring for: {:?}", path);

        // Create watcher with ARM64-optimized configuration
        let config = Config::default()
            .with_poll_interval(Duration::from_secs(2)) // Conservative polling for ARM64
            .with_compare_contents(false); // Reduce I/O load

        let mut watcher = RecommendedWatcher::new(
            move |res: notify::Result<Event>| {
                match res {
                    Ok(event) => {
                        debug!("File system event: {:?}", event);
                        
                        let fs_event = FileSystemEvent::from_notify_event(event);
                        if let Err(e) = event_tx.send(fs_event) {
                            warn!("Failed to send file system event: {}", e);
                        }
                    }
                    Err(e) => {
                        error!("File system watch error: {}", e);
                    }
                }
            },
            config,
        )?;

        // Start watching the directory
        watcher.watch(&path, RecursiveMode::Recursive)?;

        // Store the watcher
        let mut watchers = self.watchers.write().await;
        watchers.insert(path.clone(), watcher);

        info!("File system monitoring started for: {:?}", path);
        Ok(())
    }

    /// Stop watching a directory
    pub async fn unwatch_directory(&self, path: &Path) -> Result<()> {
        let mut watchers = self.watchers.write().await;
        
        if let Some(mut watcher) = watchers.remove(path) {
            watcher.unwatch(path)?;
            info!("Stopped monitoring: {:?}", path);
        }

        Ok(())
    }

    /// Subscribe to file system events
    pub fn subscribe_to_events(&self) -> broadcast::Receiver<FileSystemEvent> {
        self.event_tx.subscribe()
    }

    /// Get list of currently watched directories
    pub async fn get_watched_directories(&self) -> Vec<PathBuf> {
        let watchers = self.watchers.read().await;
        watchers.keys().cloned().collect()
    }

    /// Stop all watchers
    pub async fn stop_all(&self) -> Result<()> {
        let mut watchers = self.watchers.write().await;
        
        for (path, mut watcher) in watchers.drain() {
            if let Err(e) = watcher.unwatch(&path) {
                warn!("Error stopping watcher for {:?}: {}", path, e);
            }
        }

        info!("All file system monitors stopped");
        Ok(())
    }
}

/// File system event types
#[derive(Debug, Clone, serde::Serialize)]
pub struct FileSystemEvent {
    pub event_type: FileSystemEventType,
    pub paths: Vec<PathBuf>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// File system event type enumeration
#[derive(Debug, Clone, serde::Serialize)]
pub enum FileSystemEventType {
    Created,
    Modified,
    Deleted,
    Moved { from: PathBuf, to: PathBuf },
    Other,
}

impl FileSystemEvent {
    /// Convert from notify event to our event type
    fn from_notify_event(event: Event) -> Self {
        let timestamp = chrono::Utc::now();
        
        let event_type = match event.kind {
            EventKind::Create(_) => FileSystemEventType::Created,
            EventKind::Modify(_) => FileSystemEventType::Modified,
            EventKind::Remove(_) => FileSystemEventType::Deleted,
            EventKind::Move(_) => {
                if event.paths.len() >= 2 {
                    FileSystemEventType::Moved {
                        from: event.paths[0].clone(),
                        to: event.paths[1].clone(),
                    }
                } else {
                    FileSystemEventType::Other
                }
            }
            _ => FileSystemEventType::Other,
        };

        Self {
            event_type,
            paths: event.paths,
            timestamp,
        }
    }

    /// Check if this event is for a media file
    pub fn is_media_file(&self, supported_extensions: &std::collections::HashSet<String>) -> bool {
        self.paths.iter().any(|path| {
            if let Some(extension) = path.extension() {
                if let Some(ext_str) = extension.to_str() {
                    return supported_extensions.contains(&ext_str.to_lowercase());
                }
            }
            false
        })
    }

    /// Get the primary path for this event
    pub fn primary_path(&self) -> Option<&PathBuf> {
        self.paths.first()
    }
}

/// File system event handler for processing events
pub struct FileSystemEventHandler {
    media_library: super::MediaLibrary,
    supported_extensions: std::collections::HashSet<String>,
}

impl FileSystemEventHandler {
    pub fn new(media_library: super::MediaLibrary) -> Self {
        let mut supported_extensions = std::collections::HashSet::new();
        
        // Add supported extensions from config
        for format in &media_library.config.supported_video_formats {
            supported_extensions.insert(format.to_lowercase());
        }
        for format in &media_library.config.supported_audio_formats {
            supported_extensions.insert(format.to_lowercase());
        }
        for format in &media_library.config.supported_image_formats {
            supported_extensions.insert(format.to_lowercase());
        }

        Self {
            media_library,
            supported_extensions,
        }
    }

    /// Handle a file system event
    pub async fn handle_event(&self, event: FileSystemEvent) -> Result<()> {
        // Only process events for media files
        if !event.is_media_file(&self.supported_extensions) {
            return Ok(());
        }

        debug!("Processing file system event: {:?}", event);

        match event.event_type {
            FileSystemEventType::Created => {
                self.handle_file_created(&event).await
            }
            FileSystemEventType::Modified => {
                self.handle_file_modified(&event).await
            }
            FileSystemEventType::Deleted => {
                self.handle_file_deleted(&event).await
            }
            FileSystemEventType::Moved { from: _, to: _ } => {
                self.handle_file_moved(&event).await
            }
            FileSystemEventType::Other => {
                debug!("Ignoring unknown event type");
                Ok(())
            }
        }
    }

    /// Handle file creation
    async fn handle_file_created(&self, event: &FileSystemEvent) -> Result<()> {
        if let Some(path) = event.primary_path() {
            info!("New media file detected: {:?}", path);
            
            // Find which library this file belongs to
            if let Some(library_id) = self.find_library_for_path(path).await? {
                // Process the new file
                self.media_library.process_media_file(&library_id, path).await?;
                info!("Added new media file to library: {:?}", path);
            }
        }
        Ok(())
    }

    /// Handle file modification
    async fn handle_file_modified(&self, event: &FileSystemEvent) -> Result<()> {
        if let Some(path) = event.primary_path() {
            debug!("Media file modified: {:?}", path);
            
            // Re-process the modified file
            if let Some(library_id) = self.find_library_for_path(path).await? {
                self.media_library.process_media_file(&library_id, path).await?;
                debug!("Updated modified media file: {:?}", path);
            }
        }
        Ok(())
    }

    /// Handle file deletion
    async fn handle_file_deleted(&self, event: &FileSystemEvent) -> Result<()> {
        if let Some(path) = event.primary_path() {
            info!("Media file deleted: {:?}", path);
            
            // Remove from database
            let media_repo = crate::database::repositories::MediaRepository::new(
                self.media_library.database.pool().clone()
            );
            
            if let Some(media_item) = media_repo.find_media_item_by_path(&path.to_string_lossy()).await? {
                media_repo.delete(&media_item.id).await?;
                info!("Removed deleted media file from database: {:?}", path);
            }
        }
        Ok(())
    }

    /// Handle file move/rename
    async fn handle_file_moved(&self, event: &FileSystemEvent) -> Result<()> {
        if let FileSystemEventType::Moved { from, to } = &event.event_type {
            info!("Media file moved: {:?} -> {:?}", from, to);
            
            // Update path in database
            let media_repo = crate::database::repositories::MediaRepository::new(
                self.media_library.database.pool().clone()
            );
            
            if let Some(mut media_item) = media_repo.find_media_item_by_path(&from.to_string_lossy()).await? {
                media_item.path = to.to_string_lossy().to_string();
                media_item.updated_at = chrono::Utc::now();
                
                media_repo.update(&media_item).await?;
                info!("Updated moved media file path in database");
            }
        }
        Ok(())
    }

    /// Find which library a file path belongs to
    async fn find_library_for_path(&self, path: &Path) -> Result<Option<String>> {
        let media_repo = crate::database::repositories::MediaRepository::new(
            self.media_library.database.pool().clone()
        );
        
        let libraries = media_repo.list_libraries().await?;
        
        for library in libraries {
            let library_path = Path::new(&library.path);
            if path.starts_with(library_path) {
                return Ok(Some(library.id));
            }
        }
        
        Ok(None)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_monitor_creation() {
        let monitor = FileSystemMonitor::new();
        assert!(monitor.is_ok());
    }

    #[tokio::test]
    async fn test_directory_watching() {
        let temp_dir = TempDir::new().unwrap();
        let monitor = FileSystemMonitor::new().unwrap();

        let result = monitor.watch_directory(temp_dir.path()).await;
        assert!(result.is_ok());

        let watched = monitor.get_watched_directories().await;
        assert_eq!(watched.len(), 1);
        assert_eq!(watched[0], temp_dir.path());
    }

    #[tokio::test]
    async fn test_event_subscription() {
        let monitor = FileSystemMonitor::new().unwrap();
        let _receiver = monitor.subscribe_to_events();
        
        // Just test that subscription works
        assert!(true);
    }

    #[test]
    fn test_event_conversion() {
        let notify_event = Event {
            kind: EventKind::Create(notify::event::CreateKind::File),
            paths: vec![PathBuf::from("/test/file.mp4")],
            attrs: Default::default(),
        };

        let fs_event = FileSystemEvent::from_notify_event(notify_event);
        
        match fs_event.event_type {
            FileSystemEventType::Created => assert!(true),
            _ => panic!("Expected Created event type"),
        }
        
        assert_eq!(fs_event.paths.len(), 1);
        assert_eq!(fs_event.paths[0], PathBuf::from("/test/file.mp4"));
    }

    #[test]
    fn test_media_file_detection() {
        let mut supported_extensions = std::collections::HashSet::new();
        supported_extensions.insert("mp4".to_string());
        supported_extensions.insert("mp3".to_string());

        let event = FileSystemEvent {
            event_type: FileSystemEventType::Created,
            paths: vec![PathBuf::from("/test/video.mp4")],
            timestamp: chrono::Utc::now(),
        };

        assert!(event.is_media_file(&supported_extensions));

        let non_media_event = FileSystemEvent {
            event_type: FileSystemEventType::Created,
            paths: vec![PathBuf::from("/test/document.txt")],
            timestamp: chrono::Utc::now(),
        };

        assert!(!non_media_event.is_media_file(&supported_extensions));
    }
}

//! HTTP handlers for media library management endpoints

use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use serde::{Deserialize, Serialize};
use tracing::{error, info};

use crate::{
    auth::service::UserInfo,
    database::models::CreateLibraryRequest,
    server::{handlers::ApiResponse, AppState},
};

use super::service::{LibraryStats, MediaLibraryService};

/// Query parameters for pagination
#[derive(Debug, Deserialize)]
pub struct PaginationQuery {
    pub limit: Option<i64>,
    pub offset: Option<i64>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            limit: Some(50),
            offset: Some(0),
        }
    }
}

/// Query parameters for search
#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    pub q: String,
    pub limit: Option<i64>,
}

/// Scan progress response
#[derive(Debug, Serialize)]
pub struct ScanProgressResponse {
    pub progress: super::ScanProgress,
}

/// Get all libraries
/// GET /api/v1/libraries
pub async fn get_libraries(
    State(state): State<AppState>,
    Extension(_user): Extension<UserInfo>,
) -> Result<Json<ApiResponse<Vec<crate::database::models::Library>>>, StatusCode> {
    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.get_libraries().await {
        Ok(libraries) => Ok(Json(ApiResponse::success(libraries))),
        Err(e) => {
            error!("Failed to get libraries: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Create a new library
/// POST /api/v1/libraries
pub async fn create_library(
    State(state): State<AppState>,
    Extension(user): Extension<UserInfo>,
    Json(request): Json<CreateLibraryRequest>,
) -> Result<Json<ApiResponse<crate::database::models::Library>>, StatusCode> {
    // Only administrators can create libraries
    if !user.is_administrator {
        return Err(StatusCode::FORBIDDEN);
    }

    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.create_library(request).await {
        Ok(library) => {
            info!("Library created by {}: {}", user.name, library.name);
            Ok(Json(ApiResponse::success(library)))
        }
        Err(e) => {
            error!("Failed to create library: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Delete a library
/// DELETE /api/v1/libraries/{id}
pub async fn delete_library(
    State(state): State<AppState>,
    Extension(user): Extension<UserInfo>,
    Path(library_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // Only administrators can delete libraries
    if !user.is_administrator {
        return Err(StatusCode::FORBIDDEN);
    }

    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.delete_library(&library_id).await {
        Ok(_) => {
            info!("Library deleted by {}: {}", user.name, library_id);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to delete library: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Get media items for a library
/// GET /api/v1/libraries/{id}/media
pub async fn get_library_media(
    State(state): State<AppState>,
    Extension(_user): Extension<UserInfo>,
    Path(library_id): Path<String>,
    Query(pagination): Query<PaginationQuery>,
) -> Result<Json<ApiResponse<Vec<crate::database::models::MediaItem>>>, StatusCode> {
    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let limit = pagination.limit.unwrap_or(50);
    let offset = pagination.offset.unwrap_or(0);

    match media_service.get_library_media(&library_id, limit, offset).await {
        Ok(media_items) => Ok(Json(ApiResponse::success(media_items))),
        Err(e) => {
            error!("Failed to get library media: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Get library statistics
/// GET /api/v1/libraries/{id}/stats
pub async fn get_library_stats(
    State(state): State<AppState>,
    Extension(_user): Extension<UserInfo>,
    Path(library_id): Path<String>,
) -> Result<Json<ApiResponse<LibraryStats>>, StatusCode> {
    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.get_library_stats(&library_id).await {
        Ok(stats) => Ok(Json(ApiResponse::success(stats))),
        Err(e) => {
            error!("Failed to get library stats: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Scan all libraries
/// POST /api/v1/libraries/scan
pub async fn scan_all_libraries(
    State(state): State<AppState>,
    Extension(user): Extension<UserInfo>,
) -> Result<Json<ApiResponse<super::ScanResult>>, StatusCode> {
    // Only administrators can trigger scans
    if !user.is_administrator {
        return Err(StatusCode::FORBIDDEN);
    }

    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    info!("Full library scan initiated by: {}", user.name);

    match media_service.scan_all_libraries().await {
        Ok(result) => {
            info!("Library scan completed: {} files processed", result.processed_files);
            Ok(Json(ApiResponse::success(result)))
        }
        Err(e) => {
            error!("Library scan failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Scan specific library
/// POST /api/v1/libraries/{id}/scan
pub async fn scan_library(
    State(state): State<AppState>,
    Extension(user): Extension<UserInfo>,
    Path(library_id): Path<String>,
) -> Result<Json<ApiResponse<super::ScanResult>>, StatusCode> {
    // Only administrators can trigger scans
    if !user.is_administrator {
        return Err(StatusCode::FORBIDDEN);
    }

    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    info!("Library scan initiated by {} for library: {}", user.name, library_id);

    match media_service.scan_library(&library_id).await {
        Ok(result) => {
            info!("Library scan completed for {}: {} files processed", library_id, result.processed_files);
            Ok(Json(ApiResponse::success(result)))
        }
        Err(e) => {
            error!("Library scan failed for {}: {}", library_id, e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Search media items
/// GET /api/v1/media/search
pub async fn search_media(
    State(state): State<AppState>,
    Extension(_user): Extension<UserInfo>,
    Query(search): Query<SearchQuery>,
) -> Result<Json<ApiResponse<Vec<crate::database::models::MediaItem>>>, StatusCode> {
    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let limit = search.limit.unwrap_or(50);

    match media_service.search_media(&search.q, limit).await {
        Ok(media_items) => Ok(Json(ApiResponse::success(media_items))),
        Err(e) => {
            error!("Media search failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Get media item by ID
/// GET /api/v1/media/{id}
pub async fn get_media_item(
    State(state): State<AppState>,
    Extension(_user): Extension<UserInfo>,
    Path(media_id): Path<String>,
) -> Result<Json<ApiResponse<crate::database::models::MediaItem>>, StatusCode> {
    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.get_media_item(&media_id).await {
        Ok(Some(media_item)) => Ok(Json(ApiResponse::success(media_item))),
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(e) => {
            error!("Failed to get media item: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Delete media item
/// DELETE /api/v1/media/{id}
pub async fn delete_media_item(
    State(state): State<AppState>,
    Extension(user): Extension<UserInfo>,
    Path(media_id): Path<String>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // Only administrators can delete media items
    if !user.is_administrator {
        return Err(StatusCode::FORBIDDEN);
    }

    let media_service = MediaLibraryService::new(
        std::sync::Arc::new(state.config.media.clone()),
        state.database.clone(),
    ).map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    match media_service.delete_media_item(&media_id).await {
        Ok(_) => {
            info!("Media item deleted by {}: {}", user.name, media_id);
            Ok(Json(ApiResponse::success(())))
        }
        Err(e) => {
            error!("Failed to delete media item: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, database::Database};
    use std::sync::Arc;
    use tempfile::TempDir;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.security.jwt_secret = Some("test_secret".to_string());

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    fn create_test_user(is_admin: bool) -> UserInfo {
        UserInfo {
            id: "test-user".to_string(),
            name: "Test User".to_string(),
            is_administrator: is_admin,
            is_hidden: false,
            created_at: chrono::Utc::now(),
            last_login_date: None,
        }
    }

    #[tokio::test]
    async fn test_get_libraries() {
        let state = create_test_state().await;
        let user = create_test_user(false);

        let result = get_libraries(
            axum::extract::State(state),
            Extension(user),
        ).await;

        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_create_library_admin_only() {
        let state = create_test_state().await;
        let non_admin_user = create_test_user(false);

        let request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/tmp/test".to_string(),
            library_type: "Movies".to_string(),
        };

        let result = create_library(
            axum::extract::State(state),
            Extension(non_admin_user),
            Json(request),
        ).await;

        assert_eq!(result.unwrap_err(), StatusCode::FORBIDDEN);
    }

    #[tokio::test]
    async fn test_create_library_admin_success() {
        let state = create_test_state().await;
        let admin_user = create_test_user(true);

        let request = CreateLibraryRequest {
            name: "Test Library".to_string(),
            path: "/tmp/test".to_string(),
            library_type: "Movies".to_string(),
        };

        let result = create_library(
            axum::extract::State(state),
            Extension(admin_user),
            Json(request),
        ).await;

        assert!(result.is_ok());
    }
}

[package]
name = "tulip-media"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "High-performance Jellyfin-compatible media server for ARM64"
license = "MIT"
repository = "https://github.com/yourusername/tulip-media"

[dependencies]
# Async runtime - Tokio with full features for comprehensive async support
tokio = { version = "1.35", features = ["full"] }

# Command line parsing - Clap v4 with derive macros for easy CLI definition
clap = { version = "4.4", features = ["derive"] }

# Logging - Tracing for structured, async-aware logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling - Anyhow for easy error propagation
anyhow = "1.0"
thiserror = "2.0.16"        # Custom error types

# Serialization - Serde for configuration and API data
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Configuration management
config = "0.14.0"           # Hierarchical configuration management
toml = "0.8.8"             # TOML parsing and serialization
dirs = "5.0"               # Standard directory locations
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"         # Global static initialization

# Database dependencies
sqlx = { version = "0.8.6", features = [
    "runtime-tokio-rustls",
    "sqlite", 
    "chrono", 
    "uuid",
    "migrate"
] }
chrono = { version = "0.4", features = ["serde"] }

# Additional utilities
async-trait = "0.1"         # Async traits for repositories
bcrypt = "0.17.1"          # Password hashing

# HTTP server dependencies
axum = { version = "0.8.7", features = ["macros", "multipart"] }
tower = { version = "0.5.1", features = ["full"] }
tower-http = { version = "0.6.2", features = [
    "cors",
    "compression-gzip",
    "trace",
    "timeout",
    "limit"
] }
hyper = { version = "1.5.1", features = ["full"] }
tokio-util = "0.7"

# Additional utilities
futures = "0.3"
pin-project-lite = "0.2"

# Authentication dependencies
jsonwebtoken = "9.3"       # JWT token handling
base64 = "0.22"           # Base64 encoding/decoding
rand = "0.8"              # Random number generation
argon2 = "0.5"            # Alternative password hashing (ARM64 optimized)

# Rate limiting
governor = "0.7"          # Rate limiting middleware

# Media processing dependencies
walkdir = "2.4"           # Directory traversal
notify = "6.1"            # File system monitoring
mime_guess = "2.0"        # MIME type detection
image = { version = "0.25", features = ["jpeg", "png", "webp"] }
exif = "0.6"              # EXIF data extraction
blake3 = "1.5"            # Fast hashing for deduplication
lru = "0.12"              # LRU cache implementation

# ARM64 optimizations
rayon = "1.8"             # Parallel processing
crossbeam = "0.8"         # Lock-free data structures

# Progress tracking
tokio-stream = "0.1"      # Async streams

[profile.release]
# Optimize for ARM64 performance
lto = true              # Link-time optimization for smaller, faster binaries
codegen-units = 1       # Single codegen unit for better optimization
panic = "abort"         # Abort on panic for smaller binary size
strip = true            # Strip debug symbols from release builds
opt-level = 3           # Maximum optimization level

[profile.release.package."*"]
opt-level = 3           # Optimize all dependencies at maximum level

# ARM64-specific optimizations
[target.'cfg(target_arch = "aarch64")'.dependencies]
# ARM64-specific SIMD optimizations will be added in Chapter 7

[dev-dependencies]
# Testing utilities
tempfile = "3.8"        # Temporary files for testing

[features]
default = []
hot-reload = ["notify"]    # Enable hot-reloading for development

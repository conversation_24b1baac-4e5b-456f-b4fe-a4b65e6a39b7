//! High-performance image cache with LRU eviction

use anyhow::Result;
use moka::future::Cache;
use std::{
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::fs;
use tracing::{debug, info, warn};

use super::{CacheSettings, ProcessedImage};

/// Image cache with memory and disk tiers
#[derive(Clone)]
pub struct ImageCache {
    memory_cache: Cache<String, Arc<ProcessedImage>>,
    disk_cache_dir: PathBuf,
    settings: CacheSettings,
}

impl ImageCache {
    pub fn new(settings: &CacheSettings) -> Result<Self> {
        let disk_cache_dir = PathBuf::from("./data/image_cache");
        std::fs::create_dir_all(&disk_cache_dir)?;

        // Create memory cache with size and TTL limits
        let memory_cache = Cache::builder()
            .max_capacity(settings.max_memory_mb * 1024 * 1024) // Convert MB to bytes
            .time_to_live(Duration::from_secs(settings.ttl_seconds))
            .time_to_idle(Duration::from_secs(settings.ttl_seconds / 2))
            .build();

        info!("Image cache initialized: {}MB memory, {}MB disk", 
              settings.max_memory_mb, settings.max_disk_mb);

        Ok(Self {
            memory_cache,
            disk_cache_dir,
            settings: settings.clone(),
        })
    }

    /// Get cached image
    pub async fn get(&self, key: &str) -> Result<Option<ProcessedImage>> {
        // Try memory cache first
        if let Some(cached) = self.memory_cache.get(key).await {
            debug!("Memory cache hit: {}", key);
            return Ok(Some((*cached).clone()));
        }

        // Try disk cache
        let disk_path = self.get_disk_path(key);
        if disk_path.exists() {
            match self.load_from_disk(&disk_path).await {
                Ok(image) => {
                    debug!("Disk cache hit: {}", key);
                    // Promote to memory cache
                    self.memory_cache.insert(key.to_string(), Arc::new(image.clone())).await;
                    return Ok(Some(image));
                }
                Err(e) => {
                    warn!("Failed to load from disk cache: {}", e);
                    // Remove corrupted cache file
                    let _ = fs::remove_file(&disk_path).await;
                }
            }
        }

        debug!("Cache miss: {}", key);
        Ok(None)
    }

    /// Put image in cache
    pub async fn put(&self, key: String, image: ProcessedImage) -> Result<()> {
        let image_arc = Arc::new(image.clone());
        
        // Store in memory cache
        self.memory_cache.insert(key.clone(), image_arc).await;

        // Store in disk cache asynchronously
        let disk_path = self.get_disk_path(&key);
        if let Err(e) = self.save_to_disk(&image, &disk_path).await {
            warn!("Failed to save to disk cache: {}", e);
        }

        debug!("Cached image: {}", key);
        Ok(())
    }

    /// Clear all caches
    pub async fn clear(&self) -> Result<()> {
        // Clear memory cache
        self.memory_cache.invalidate_all();

        // Clear disk cache
        if self.disk_cache_dir.exists() {
            fs::remove_dir_all(&self.disk_cache_dir).await?;
            fs::create_dir_all(&self.disk_cache_dir).await?;
        }

        info!("Image cache cleared");
        Ok(())
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        let memory_entry_count = self.memory_cache.entry_count();
        let memory_weighted_size = self.memory_cache.weighted_size();

        // Count disk cache files
        let mut disk_entry_count = 0;
        let mut disk_size = 0;

        if let Ok(entries) = fs::read_dir(&self.disk_cache_dir).await {
            let mut entries = entries;
            while let Ok(Some(entry)) = entries.next_entry().await {
                if let Ok(metadata) = entry.metadata().await {
                    disk_entry_count += 1;
                    disk_size += metadata.len();
                }
            }
        }

        CacheStats {
            memory_entries: memory_entry_count,
            memory_size_bytes: memory_weighted_size,
            disk_entries: disk_entry_count,
            disk_size_bytes: disk_size,
            hit_rate: 0.0, // Would need to track hits/misses for real implementation
        }
    }

    /// Cleanup old cache entries
    pub async fn cleanup(&self) -> Result<usize> {
        let mut removed = 0;
        let cutoff = std::time::SystemTime::now() - Duration::from_secs(self.settings.ttl_seconds);

        if let Ok(entries) = fs::read_dir(&self.disk_cache_dir).await {
            let mut entries = entries;
            while let Ok(Some(entry)) = entries.next_entry().await {
                if let Ok(metadata) = entry.metadata().await {
                    if let Ok(modified) = metadata.modified() {
                        if modified < cutoff {
                            if fs::remove_file(entry.path()).await.is_ok() {
                                removed += 1;
                            }
                        }
                    }
                }
            }
        }

        if removed > 0 {
            info!("Cleaned up {} old cache entries", removed);
        }

        Ok(removed)
    }

    /// Get disk cache file path
    fn get_disk_path(&self, key: &str) -> PathBuf {
        self.disk_cache_dir.join(format!("{}.cache", key))
    }

    /// Save image to disk cache
    async fn save_to_disk(&self, image: &ProcessedImage, path: &Path) -> Result<()> {
        // Create a simple serialization format
        let cache_data = CacheData {
            width: image.data.width(),
            height: image.data.height(),
            format: format!("{:?}", image.format),
            data: image.data.as_bytes().to_vec(),
        };

        let serialized = bincode::serialize(&cache_data)?;
        fs::write(path, serialized).await?;

        Ok(())
    }

    /// Load image from disk cache
    async fn load_from_disk(&self, path: &Path) -> Result<ProcessedImage> {
        let data = fs::read(path).await?;
        let cache_data: CacheData = bincode::deserialize(&data)?;

        // Reconstruct image from cached data
        let image_buffer = image::ImageBuffer::from_raw(
            cache_data.width,
            cache_data.height,
            cache_data.data,
        ).ok_or_else(|| anyhow::anyhow!("Failed to reconstruct image from cache"))?;

        let dynamic_image = image::DynamicImage::ImageRgb8(image_buffer);

        Ok(ProcessedImage {
            data: dynamic_image,
            format: super::ImageFormat::Jpeg, // Default format
            metadata: super::ProcessingMetadata {
                width: cache_data.width,
                height: cache_data.height,
                color_type: "Rgb8".to_string(),
                estimated_size: cache_data.data.len(),
                processing_time: std::time::Instant::now(),
            },
        })
    }
}

/// Cache statistics
#[derive(Debug, Clone, serde::Serialize)]
pub struct CacheStats {
    pub memory_entries: u64,
    pub memory_size_bytes: u64,
    pub disk_entries: usize,
    pub disk_size_bytes: u64,
    pub hit_rate: f64,
}

/// Serializable cache data
#[derive(serde::Serialize, serde::Deserialize)]
struct CacheData {
    width: u32,
    height: u32,
    format: String,
    data: Vec<u8>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_cache_creation() {
        let settings = CacheSettings {
            max_memory_mb: 64,
            max_disk_mb: 256,
            ttl_seconds: 3600,
            cleanup_interval_seconds: 300,
        };

        let cache = ImageCache::new(&settings);
        assert!(cache.is_ok());
    }

    #[tokio::test]
    async fn test_cache_miss() {
        let settings = CacheSettings {
            max_memory_mb: 64,
            max_disk_mb: 256,
            ttl_seconds: 3600,
            cleanup_interval_seconds: 300,
        };

        let cache = ImageCache::new(&settings).unwrap();
        let result = cache.get("nonexistent_key").await.unwrap();
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_cache_stats() {
        let settings = CacheSettings {
            max_memory_mb: 64,
            max_disk_mb: 256,
            ttl_seconds: 3600,
            cleanup_interval_seconds: 300,
        };

        let cache = ImageCache::new(&settings).unwrap();
        let stats = cache.get_stats().await;
        
        assert_eq!(stats.memory_entries, 0);
        assert_eq!(stats.disk_entries, 0);
    }

    #[tokio::test]
    async fn test_cache_clear() {
        let settings = CacheSettings {
            max_memory_mb: 64,
            max_disk_mb: 256,
            ttl_seconds: 3600,
            cleanup_interval_seconds: 300,
        };

        let cache = ImageCache::new(&settings).unwrap();
        let result = cache.clear().await;
        assert!(result.is_ok());
    }
}

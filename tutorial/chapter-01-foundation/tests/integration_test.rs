use std::process::Command;
use tempfile::TempDir;

#[test]
fn test_cli_help() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--help"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Tulip Media Server"));
    assert!(stdout.contains("--config"));
    assert!(stdout.contains("--port"));
    assert!(stdout.contains("--debug"));
}

#[test]
fn test_cli_version() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--version"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("tulip-media"));
}

#[test]
fn test_cli_with_custom_port() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--", 
            "--port", "9000",
            "--data-dir", data_dir.to_str().unwrap(),
            "--debug"
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 0.0.0.0:9000"));
    assert!(stdout.contains("Foundation setup complete"));
}

#[test]
fn test_cli_with_media_dirs() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    let media_dir1 = temp_dir.path().join("movies");
    let media_dir2 = temp_dir.path().join("tv");
    
    // Create media directories
    std::fs::create_dir_all(&media_dir1).unwrap();
    std::fs::create_dir_all(&media_dir2).unwrap();
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--", 
            "--data-dir", data_dir.to_str().unwrap(),
            "--media-dirs", media_dir1.to_str().unwrap(),
            "--media-dirs", media_dir2.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Media directories:"));
}

#[test]
fn test_invalid_port() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--port", "0"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(!output.status.success());
    let stderr = String::from_utf8_lossy(&output.stderr);
    assert!(stderr.contains("Port must be greater than 0"));
}

use anyhow::Result;
use tracing::info;
use tulip_media::{cli::Cli, utils};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse_args();
    
    // Validate arguments
    cli.validate()?;

    // Initialize logging
    utils::init_logging(cli.debug)?;

    // Log startup information
    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Configuration file: {:?}", cli.config);
    info!("Data directory: {:?}", cli.data_dir);
    info!("Server will bind to: {}:{}", cli.bind_address, cli.port);
    info!("CPU cores available: {}", utils::get_cpu_count());

    if !cli.media_dirs.is_empty() {
        info!("Media directories: {:?}", cli.media_dirs);
    }

    // Display system information
    display_system_info();

    // TODO: In the next chapter, we'll load configuration and start the server
    info!("Foundation setup complete! Ready for Chapter 2.");

    Ok(())
}

/// Display system information useful for ARM64 optimization
fn display_system_info() {
    info!("System Information:");
    info!("  Architecture: {}", std::env::consts::ARCH);
    info!("  OS: {}", std::env::consts::OS);
    info!("  CPU cores: {}", utils::get_cpu_count());
    
    // Display memory information if available
    if let Ok(memory) = get_memory_info() {
        info!("  Available memory: {}", utils::format_bytes(memory));
    }
}

/// Get available system memory (Linux-specific)
fn get_memory_info() -> Result<u64> {
    #[cfg(target_os = "linux")]
    {
        let meminfo = std::fs::read_to_string("/proc/meminfo")?;
        for line in meminfo.lines() {
            if line.starts_with("MemAvailable:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    let kb: u64 = parts[1].parse()?;
                    return Ok(kb * 1024); // Convert KB to bytes
                }
            }
        }
    }
    
    // Fallback for non-Linux systems
    Ok(0)
}

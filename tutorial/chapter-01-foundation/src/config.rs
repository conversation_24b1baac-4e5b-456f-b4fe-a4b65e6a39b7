//! Configuration management for Tulip Media Server
//!
//! This module will be expanded in Chapter 2 to handle TOML configuration files,
//! environment variables, and configuration validation.

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Main configuration structure (placeholder)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
}

/// Server configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub bind_address: String,
    pub port: u16,
    pub server_name: String,
}

/// Database configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub path: PathBuf,
    pub max_connections: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                bind_address: "0.0.0.0".to_string(),
                port: 8096,
                server_name: "Tulip Media Server".to_string(),
            },
            database: DatabaseConfig {
                path: PathBuf::from("./data/tulip.db"),
                max_connections: 10,
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert_eq!(config.server.bind_address, "0.0.0.0");
        assert_eq!(config.server.port, 8096);
        assert_eq!(config.server.server_name, "Tulip Media Server");
        assert_eq!(config.database.path, PathBuf::from("./data/tulip.db"));
        assert_eq!(config.database.max_connections, 10);
    }

    #[test]
    fn test_config_serialization() {
        let config = Config::default();
        let serialized = serde_json::to_string(&config).unwrap();
        let deserialized: Config = serde_json::from_str(&serialized).unwrap();
        
        assert_eq!(config.server.port, deserialized.server.port);
        assert_eq!(config.server.bind_address, deserialized.server.bind_address);
    }
}

# Chapter 1: Project Foundation

**Duration**: ~30 minutes  
**Difficulty**: Beginner  
**Prerequisites**: Basic Rust knowledge, Cargo familiarity

## 🎯 Learning Objectives

By the end of this chapter, you will:
- Set up a well-structured Rust project for a media server
- Understand the rationale behind dependency choices
- Configure Cargo.toml for ARM64 optimization
- Create a modular project structure
- Implement basic CLI argument parsing
- Set up logging and error handling foundations

## 📋 What We're Building

In this chapter, we'll create the foundation of our media server:
- Project structure with proper module organization
- Cargo.toml with optimized dependencies
- Basic CLI interface with argument parsing
- Logging setup with structured output
- Error handling patterns
- Initial main.rs with proper async setup

## 🏗 Project Structure Overview

```
tulip-media/
├── Cargo.toml              # Project configuration and dependencies
├── src/
│   ├── main.rs            # Application entry point
│   ├── lib.rs             # Library root with module declarations
│   ├── cli.rs             # Command-line interface
│   ├── config.rs          # Configuration management (placeholder)
│   └── utils/             # Utility modules
│       └── mod.rs         # Utils module root
├── tests/                 # Integration tests
│   └── integration_test.rs
└── README.md              # Project documentation
```

## 🚀 Step-by-Step Implementation

### Step 1: Create the Project

```bash
# Create new Rust project
cargo new tulip-media --name tulip-media
cd tulip-media

# Verify it works
cargo run
```

### Step 2: Configure Cargo.toml

Let's build our `Cargo.toml` with carefully chosen dependencies:

```toml
[package]
name = "tulip-media"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "High-performance Jellyfin-compatible media server for ARM64"
license = "MIT"
repository = "https://github.com/yourusername/tulip-media"

[dependencies]
# Async runtime - Tokio with full features for comprehensive async support
tokio = { version = "1.35", features = ["full"] }

# Command line parsing - Clap v4 with derive macros for easy CLI definition
clap = { version = "4.4", features = ["derive"] }

# Logging - Tracing for structured, async-aware logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling - Anyhow for easy error propagation
anyhow = "1.0"

# Serialization - Serde for configuration and API data
serde = { version = "1.0", features = ["derive"] }

[profile.release]
# Optimize for ARM64 performance
lto = true              # Link-time optimization for smaller, faster binaries
codegen-units = 1       # Single codegen unit for better optimization
panic = "abort"         # Abort on panic for smaller binary size
strip = true            # Strip debug symbols from release builds
opt-level = 3           # Maximum optimization level

[profile.release.package."*"]
opt-level = 3           # Optimize all dependencies at maximum level

# ARM64-specific optimizations
[target.'cfg(target_arch = "aarch64")'.dependencies]
# We'll add ARM64-specific crates in later chapters

[dev-dependencies]
# Testing utilities
tempfile = "3.8"        # Temporary files for testing
```

### Step 3: Create Module Structure

Create `src/lib.rs` to define our module structure:

```rust
//! Tulip Media Server - A high-performance Jellyfin-compatible media server
//!
//! This library provides all the core functionality for the Tulip Media Server,
//! optimized for ARM64 devices like the Nano Pi M4 V2.

pub mod cli;
pub mod config;
pub mod utils;

// Re-export commonly used types for convenience
pub use cli::Cli;
```

### Step 4: Implement CLI Module

Create `src/cli.rs` for command-line argument parsing:

```rust
use clap::Parser;
use std::path::PathBuf;

/// Tulip Media Server - High-performance Jellyfin-compatible media server
#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.toml")]
    pub config: PathBuf,

    /// Data directory for database and cache
    #[arg(short, long, default_value = "./data")]
    pub data_dir: PathBuf,

    /// Media library directories (can be specified multiple times)
    #[arg(short, long)]
    pub media_dirs: Vec<PathBuf>,

    /// Server bind address
    #[arg(long, default_value = "0.0.0.0")]
    pub bind_address: String,

    /// Server port
    #[arg(short, long, default_value = "8096")]
    pub port: u16,

    /// Enable debug logging
    #[arg(long)]
    pub debug: bool,
}

impl Cli {
    /// Parse command line arguments
    pub fn parse_args() -> Self {
        Self::parse()
    }

    /// Validate the parsed arguments
    pub fn validate(&self) -> anyhow::Result<()> {
        // Validate port range
        if self.port == 0 {
            anyhow::bail!("Port must be greater than 0");
        }

        // Validate data directory can be created
        if let Some(parent) = self.data_dir.parent() {
            if !parent.exists() {
                std::fs::create_dir_all(parent)
                    .map_err(|e| anyhow::anyhow!("Cannot create data directory: {}", e))?;
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_defaults() {
        let cli = Cli::parse_from(&["tulip-media"]);
        assert_eq!(cli.config, PathBuf::from("config.toml"));
        assert_eq!(cli.data_dir, PathBuf::from("./data"));
        assert_eq!(cli.bind_address, "0.0.0.0");
        assert_eq!(cli.port, 8096);
        assert!(!cli.debug);
    }

    #[test]
    fn test_cli_custom_args() {
        let cli = Cli::parse_from(&[
            "tulip-media",
            "--config", "custom.toml",
            "--port", "9000",
            "--debug"
        ]);
        assert_eq!(cli.config, PathBuf::from("custom.toml"));
        assert_eq!(cli.port, 9000);
        assert!(cli.debug);
    }
}
```

### Step 5: Create Configuration Placeholder

Create `src/config.rs` as a placeholder for the next chapter:

```rust
//! Configuration management for Tulip Media Server
//!
//! This module will be expanded in Chapter 2 to handle TOML configuration files,
//! environment variables, and configuration validation.

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Main configuration structure (placeholder)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub bind_address: String,
    pub port: u16,
    pub server_name: String,
}

/// Database configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub path: PathBuf,
    pub max_connections: u32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                bind_address: "0.0.0.0".to_string(),
                port: 8096,
                server_name: "Tulip Media Server".to_string(),
            },
            database: DatabaseConfig {
                path: PathBuf::from("./data/tulip.db"),
                max_connections: 10,
            },
        }
    }
}
```

### Step 6: Create Utils Module

Create `src/utils/mod.rs`:

```rust
//! Utility functions and helpers for Tulip Media Server

/// Initialize logging based on debug flag
pub fn init_logging(debug: bool) -> anyhow::Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    let env_filter = if debug {
        "tulip_media=debug,info"
    } else {
        "tulip_media=info,warn"
    };

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| env_filter.into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}

/// Get the number of CPU cores, useful for ARM64 optimization
pub fn get_cpu_count() -> usize {
    std::thread::available_parallelism()
        .map(|n| n.get())
        .unwrap_or(1)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpu_count() {
        let count = get_cpu_count();
        assert!(count > 0);
        assert!(count <= 64); // Reasonable upper bound
    }
}
```

### Step 7: Update main.rs

Replace the contents of `src/main.rs`:

```rust
use anyhow::Result;
use tracing::info;
use tulip_media::{cli::Cli, utils};

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse_args();
    
    // Validate arguments
    cli.validate()?;

    // Initialize logging
    utils::init_logging(cli.debug)?;

    // Log startup information
    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Configuration file: {:?}", cli.config);
    info!("Data directory: {:?}", cli.data_dir);
    info!("Server will bind to: {}:{}", cli.bind_address, cli.port);
    info!("CPU cores available: {}", utils::get_cpu_count());

    if !cli.media_dirs.is_empty() {
        info!("Media directories: {:?}", cli.media_dirs);
    }

    // TODO: In the next chapter, we'll load configuration and start the server
    info!("Foundation setup complete! Ready for Chapter 2.");

    Ok(())
}
```

## 🧪 Testing Our Foundation

### Step 8: Create Integration Test

Create `tests/integration_test.rs`:

```rust
use std::process::Command;

#[test]
fn test_cli_help() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--help"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Tulip Media Server"));
    assert!(stdout.contains("--config"));
    assert!(stdout.contains("--port"));
}

#[test]
fn test_cli_version() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--version"])
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("tulip-media"));
}
```

### Step 9: Test Everything

```bash
# Run unit tests
cargo test

# Run with different CLI options
cargo run -- --help
cargo run -- --version
cargo run -- --debug --port 9000

# Build optimized version
cargo build --release
```

## 🎯 Key Concepts Explained

### Why These Dependencies?

1. **Tokio**: Industry-standard async runtime, excellent ARM64 support
2. **Clap**: Type-safe CLI parsing with derive macros
3. **Tracing**: Structured logging designed for async applications
4. **Anyhow**: Ergonomic error handling for applications
5. **Serde**: De facto standard for serialization in Rust

### ARM64 Optimization Decisions

1. **LTO (Link Time Optimization)**: Enables cross-crate optimizations
2. **Single Codegen Unit**: Better optimization at cost of compile time
3. **Panic = Abort**: Smaller binaries, faster execution
4. **Strip Symbols**: Reduces binary size for deployment

### Project Structure Rationale

1. **lib.rs**: Separates library code from binary, enables testing
2. **Modular Design**: Each module has single responsibility
3. **CLI First**: Configuration through command line, then files
4. **Error Handling**: Consistent error propagation with anyhow

## 🔍 What's Next?

In **Chapter 2: Configuration Management**, we'll:
- Implement TOML configuration file parsing
- Add environment variable support
- Create configuration validation
- Set up hot-reloading for development

## 📚 Additional Resources

- [The Cargo Book](https://doc.rust-lang.org/cargo/) - Comprehensive Cargo documentation
- [Clap Documentation](https://docs.rs/clap/) - CLI argument parsing
- [Tracing Guide](https://tracing.rs/tracing/) - Structured logging in Rust
- [ARM64 Optimization Guide](https://developer.arm.com/documentation/102374/0101/Compiler-optimizations)

---

**Checkpoint**: You now have a solid foundation for a Rust media server project with proper CLI handling, logging, and project structure. Ready for [Chapter 2: Configuration Management](../chapter-02-configuration/)?

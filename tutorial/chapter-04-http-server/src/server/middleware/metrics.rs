//! Metrics collection middleware

use axum::{
    extract::Request,
    http::StatusCode,
    middleware::Next,
    response::Response,
};
use std::{
    sync::{
        atomic::{AtomicU64, Ordering},
        Arc,
    },
    time::Instant,
};
use tracing::{debug, info};

/// Metrics collector
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Metrics {
    pub request_count: Arc<AtomicU64>,
    pub error_count: Arc<AtomicU64>,
    pub total_response_time: Arc<AtomicU64>,
}

impl Default for Metrics {
    fn default() -> Self {
        Self {
            request_count: Arc::new(AtomicU64::new(0)),
            error_count: Arc::new(AtomicU64::new(0)),
            total_response_time: Arc::new(AtomicU64::new(0)),
        }
    }
}

impl Metrics {
    pub fn get_request_count(&self) -> u64 {
        self.request_count.load(Ordering::Relaxed)
    }

    pub fn get_error_count(&self) -> u64 {
        self.error_count.load(Ordering::Relaxed)
    }

    pub fn get_average_response_time(&self) -> f64 {
        let total_time = self.total_response_time.load(Ordering::Relaxed);
        let request_count = self.request_count.load(Ordering::Relaxed);
        
        if request_count > 0 {
            total_time as f64 / request_count as f64
        } else {
            0.0
        }
    }
}

/// Metrics middleware layer
#[derive(Clone)]
pub struct MetricsLayer {
    metrics: Metrics,
}

impl MetricsLayer {
    pub fn new() -> Self {
        Self {
            metrics: Metrics::default(),
        }
    }

    pub fn metrics(&self) -> &Metrics {
        &self.metrics
    }
}

impl<S> tower::Layer<S> for MetricsLayer {
    type Service = MetricsService<S>;

    fn layer(&self, inner: S) -> Self::Service {
        MetricsService {
            inner,
            metrics: self.metrics.clone(),
        }
    }
}

/// Metrics service
#[derive(Clone)]
pub struct MetricsService<S> {
    inner: S,
    metrics: Metrics,
}

impl<S> tower::Service<Request> for MetricsService<S>
where
    S: tower::Service<Request, Response = Response> + Clone + Send + 'static,
    S::Future: Send + 'static,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = std::pin::Pin<Box<dyn std::future::Future<Output = Result<Self::Response, Self::Error>> + Send>>;

    fn poll_ready(&mut self, cx: &mut std::task::Context<'_>) -> std::task::Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request) -> Self::Future {
        let start_time = Instant::now();
        let metrics = self.metrics.clone();
        let mut inner = self.inner.clone();

        Box::pin(async move {
            // Increment request count
            metrics.request_count.fetch_add(1, Ordering::Relaxed);

            // Process request
            let response = inner.call(request).await?;

            // Record response time
            let response_time = start_time.elapsed().as_millis() as u64;
            metrics.total_response_time.fetch_add(response_time, Ordering::Relaxed);

            // Check for errors
            if response.status().is_server_error() || response.status().is_client_error() {
                metrics.error_count.fetch_add(1, Ordering::Relaxed);
            }

            // Log metrics periodically
            let request_count = metrics.get_request_count();
            if request_count % 100 == 0 {
                info!(
                    "Metrics: {} requests, {} errors, {:.2}ms avg response time",
                    request_count,
                    metrics.get_error_count(),
                    metrics.get_average_response_time()
                );
            }

            debug!("Request processed in {}ms", response_time);

            Ok(response)
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metrics_creation() {
        let metrics = Metrics::default();
        assert_eq!(metrics.get_request_count(), 0);
        assert_eq!(metrics.get_error_count(), 0);
        assert_eq!(metrics.get_average_response_time(), 0.0);
    }

    #[test]
    fn test_metrics_layer() {
        let layer = MetricsLayer::new();
        assert_eq!(layer.metrics().get_request_count(), 0);
    }
}

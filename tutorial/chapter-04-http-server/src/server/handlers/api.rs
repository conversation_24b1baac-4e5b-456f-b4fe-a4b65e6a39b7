//! Basic API endpoints (placeholder for future chapters)

use axum::extract::State;
use serde::{Deserialize, Serialize};

use crate::server::AppState;
use super::ApiResponse;

/// Ping response
#[derive(Debug, Serialize, Deserialize)]
pub struct PingResponse {
    pub message: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub server: String,
}

/// Simple ping endpoint for API testing
/// GET /api/v1/ping
pub async fn ping(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    let response = PingResponse {
        message: "pong".to_string(),
        timestamp: chrono::Utc::now(),
        server: state.config.server.server_name.clone(),
    };

    ApiResponse::success(response)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, database::Database};
    use std::sync::Arc;
    use tempfile::TempDir;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    #[tokio::test]
    async fn test_ping() {
        let state = create_test_state().await;
        let response = ping(axum::extract::State(state)).await;
        
        // The response should be a successful ping
    }
}

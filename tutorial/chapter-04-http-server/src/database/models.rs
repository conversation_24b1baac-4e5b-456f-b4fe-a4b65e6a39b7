//! Database models for Tulip Media Server
//!
//! These models represent the core entities in our media server database.
//! All models use SQLx derive macros for compile-time query checking.

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

/// User account information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub name: String,
    pub password_hash: Option<String>,
    pub is_administrator: bool,
    pub is_hidden: bool,
    pub is_disabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_date: Option<DateTime<Utc>>,
    pub last_activity_date: Option<DateTime<Utc>>,
}

/// User session information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub device_id: String,
    pub device_name: String,
    pub client: String,
    pub version: String,
    pub access_token: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub is_active: bool,
}

/// Media library configuration
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct Library {
    pub id: String,
    pub name: String,
    pub path: String,
    pub library_type: String, // Movies, TV Shows, Music, etc.
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_scan: Option<DateTime<Utc>>,
}

/// Media item (file) information
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaItem {
    pub id: String,
    pub library_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub sort_name: String,
    pub path: String,
    pub item_type: String,  // Movie, Series, Season, Episode, Audio, etc.
    pub media_type: String, // Video, Audio, Image
    pub file_size: i64,
    pub duration: Option<i64>, // in milliseconds
    pub bitrate: Option<i64>,
    pub container: Option<String>,
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub aspect_ratio: Option<String>,
    pub framerate: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub date_added: DateTime<Utc>,
    pub date_modified: DateTime<Utc>,
}

/// Media metadata (additional information)
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaMetadata {
    pub id: String,
    pub media_item_id: String,
    pub title: Option<String>,
    pub overview: Option<String>,
    pub tagline: Option<String>,
    pub release_date: Option<DateTime<Utc>>,
    pub runtime: Option<i32>,
    pub rating: Option<f32>,
    pub vote_count: Option<i32>,
    pub genres: Option<String>, // JSON array
    pub cast: Option<String>,   // JSON array
    pub crew: Option<String>,   // JSON array
    pub external_ids: Option<String>, // JSON object
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// User playback state
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PlaybackState {
    pub id: String,
    pub user_id: String,
    pub media_item_id: String,
    pub position_ticks: i64, // Position in 100-nanosecond ticks
    pub playback_method: String, // DirectPlay, DirectStream, Transcode
    pub play_count: i32,
    pub is_played: bool,
    pub last_played: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// API keys for external access
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ApiKey {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub key_hash: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
}

// Request/Response DTOs for API operations

/// Request to create a new user
#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    pub name: String,
    pub password: Option<String>,
    pub is_administrator: bool,
}

/// Request to update user information
#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    pub name: Option<String>,
    pub is_administrator: Option<bool>,
    pub is_hidden: Option<bool>,
    pub is_disabled: Option<bool>,
}

/// Request to create a new library
#[derive(Debug, Deserialize)]
pub struct CreateLibraryRequest {
    pub name: String,
    pub path: String,
    pub library_type: String,
}

/// Request to update playback state
#[derive(Debug, Deserialize)]
pub struct UpdatePlaybackStateRequest {
    pub position_ticks: i64,
    pub playback_method: String,
    pub is_played: bool,
}

impl User {
    /// Check if user has administrative privileges
    pub fn is_admin(&self) -> bool {
        self.is_administrator && !self.is_disabled
    }

    /// Check if user can access the system
    pub fn can_access(&self) -> bool {
        !self.is_disabled
    }
}

impl MediaItem {
    /// Check if this is a video item
    pub fn is_video(&self) -> bool {
        self.media_type == "Video"
    }

    /// Check if this is an audio item
    pub fn is_audio(&self) -> bool {
        self.media_type == "Audio"
    }

    /// Get duration in seconds
    pub fn duration_seconds(&self) -> Option<f64> {
        self.duration.map(|d| d as f64 / 1000.0)
    }
}

impl PlaybackState {
    /// Get position in seconds
    pub fn position_seconds(&self) -> f64 {
        self.position_ticks as f64 / 10_000_000.0 // Convert from 100ns ticks to seconds
    }

    /// Set position from seconds
    pub fn set_position_seconds(&mut self, seconds: f64) {
        self.position_ticks = (seconds * 10_000_000.0) as i64;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_permissions() {
        let mut user = User {
            id: "test".to_string(),
            name: "test".to_string(),
            password_hash: None,
            is_administrator: true,
            is_hidden: false,
            is_disabled: false,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            last_login_date: None,
            last_activity_date: None,
        };

        assert!(user.is_admin());
        assert!(user.can_access());

        user.is_disabled = true;
        assert!(!user.is_admin());
        assert!(!user.can_access());
    }

    #[test]
    fn test_media_item_types() {
        let video_item = MediaItem {
            id: "test".to_string(),
            library_id: "lib".to_string(),
            parent_id: None,
            name: "Test Video".to_string(),
            sort_name: "Test Video".to_string(),
            path: "/test.mp4".to_string(),
            item_type: "Movie".to_string(),
            media_type: "Video".to_string(),
            file_size: 1000000,
            duration: Some(120000), // 2 minutes in milliseconds
            bitrate: None,
            container: None,
            video_codec: None,
            audio_codec: None,
            width: None,
            height: None,
            aspect_ratio: None,
            framerate: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            date_added: Utc::now(),
            date_modified: Utc::now(),
        };

        assert!(video_item.is_video());
        assert!(!video_item.is_audio());
        assert_eq!(video_item.duration_seconds(), Some(120.0));
    }

    #[test]
    fn test_playback_state_conversion() {
        let mut state = PlaybackState {
            id: "test".to_string(),
            user_id: "user".to_string(),
            media_item_id: "media".to_string(),
            position_ticks: 600_000_000, // 60 seconds in 100ns ticks
            playback_method: "DirectPlay".to_string(),
            play_count: 1,
            is_played: false,
            last_played: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        assert_eq!(state.position_seconds(), 60.0);

        state.set_position_seconds(120.0);
        assert_eq!(state.position_ticks, 1_200_000_000);
    }
}

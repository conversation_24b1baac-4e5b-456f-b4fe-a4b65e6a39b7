//! Session repository implementation

use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use sqlx::{Pool, Sqlite};
use uuid::Uuid;

use crate::database::models::Session;
use super::Repository;

pub struct SessionRepository {
    pool: Pool<Sqlite>,
}

impl SessionRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    /// Create a new session
    pub async fn create_session(
        &self,
        user_id: &str,
        device_id: &str,
        device_name: &str,
        client: &str,
        version: &str,
        access_token: &str,
    ) -> Result<Session> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let session = sqlx::query_as!(
            Session,
            r#"
            INSERT INTO sessions (id, user_id, device_id, device_name, client, version, access_token, created_at, last_activity)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            id,
            user_id,
            device_id,
            device_name,
            client,
            version,
            access_token,
            now,
            now
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(session)
    }

    /// Find session by access token
    pub async fn find_by_access_token(&self, access_token: &str) -> Result<Option<Session>> {
        let session = sqlx::query_as!(
            Session,
            "SELECT * FROM sessions WHERE access_token = ? AND is_active = TRUE",
            access_token
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(session)
    }

    /// Get all active sessions for a user
    pub async fn get_user_sessions(&self, user_id: &str) -> Result<Vec<Session>> {
        let sessions = sqlx::query_as!(
            Session,
            "SELECT * FROM sessions WHERE user_id = ? AND is_active = TRUE ORDER BY last_activity DESC",
            user_id
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(sessions)
    }

    /// Update session activity
    pub async fn update_activity(&self, session_id: &str) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE sessions SET last_activity = ? WHERE id = ?",
            now,
            session_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Deactivate session (logout)
    pub async fn deactivate_session(&self, session_id: &str) -> Result<()> {
        sqlx::query!(
            "UPDATE sessions SET is_active = FALSE WHERE id = ?",
            session_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Deactivate all sessions for a user
    pub async fn deactivate_user_sessions(&self, user_id: &str) -> Result<()> {
        sqlx::query!(
            "UPDATE sessions SET is_active = FALSE WHERE user_id = ?",
            user_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Clean up old inactive sessions
    pub async fn cleanup_old_sessions(&self, days: i64) -> Result<u64> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days);

        let result = sqlx::query!(
            "DELETE FROM sessions WHERE is_active = FALSE AND last_activity < ?",
            cutoff_date
        )
        .execute(&self.pool)
        .await?;

        Ok(result.rows_affected())
    }
}

#[async_trait]
impl Repository<Session, String> for SessionRepository {
    async fn find_by_id(&self, id: &String) -> Result<Option<Session>> {
        let session = sqlx::query_as!(
            Session,
            "SELECT * FROM sessions WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(session)
    }

    async fn create(&self, session: &Session) -> Result<Session> {
        let created_session = sqlx::query_as!(
            Session,
            r#"
            INSERT INTO sessions (id, user_id, device_id, device_name, client, version, access_token, created_at, last_activity, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            session.id,
            session.user_id,
            session.device_id,
            session.device_name,
            session.client,
            session.version,
            session.access_token,
            session.created_at,
            session.last_activity,
            session.is_active
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(created_session)
    }

    async fn update(&self, session: &Session) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE sessions 
            SET device_name = ?, client = ?, version = ?, last_activity = ?, is_active = ?
            WHERE id = ?
            "#,
            session.device_name,
            session.client,
            session.version,
            session.last_activity,
            session.is_active,
            session.id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn delete(&self, id: &String) -> Result<()> {
        sqlx::query!("DELETE FROM sessions WHERE id = ?", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;
    use crate::database::{migrations::run_migrations, repositories::UserRepository, models::CreateUserRequest};

    async fn setup_test_db() -> Pool<Sqlite> {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        run_migrations(&pool).await.unwrap();
        pool
    }

    #[tokio::test]
    async fn test_session_operations() {
        let pool = setup_test_db().await;
        let session_repo = SessionRepository::new(pool.clone());
        let user_repo = UserRepository::new(pool);

        // Create a test user first
        let user_request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password".to_string()),
            is_administrator: false,
        };
        let user = user_repo.create_user(&user_request).await.unwrap();

        // Create session
        let session = session_repo.create_session(
            &user.id,
            "device123",
            "Test Device",
            "TestClient",
            "1.0.0",
            "test_access_token"
        ).await.unwrap();

        assert_eq!(session.user_id, user.id);
        assert_eq!(session.device_id, "device123");
        assert!(session.is_active);

        // Find by access token
        let found_session = session_repo.find_by_access_token("test_access_token").await.unwrap();
        assert!(found_session.is_some());
        assert_eq!(found_session.unwrap().id, session.id);

        // Update activity
        session_repo.update_activity(&session.id).await.unwrap();

        // Get user sessions
        let user_sessions = session_repo.get_user_sessions(&user.id).await.unwrap();
        assert_eq!(user_sessions.len(), 1);

        // Deactivate session
        session_repo.deactivate_session(&session.id).await.unwrap();

        let inactive_session = session_repo.find_by_access_token("test_access_token").await.unwrap();
        assert!(inactive_session.is_none()); // Should not find inactive sessions

        // Clean up old sessions
        let cleaned = session_repo.cleanup_old_sessions(0).await.unwrap();
        assert_eq!(cleaned, 1); // Should clean up the inactive session
    }
}

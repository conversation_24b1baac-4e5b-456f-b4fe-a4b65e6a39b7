//! User repository implementation

use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use sqlx::{Pool, Sqlite};
use uuid::Uuid;

use crate::database::models::{User, CreateUserRequest, UpdateUserRequest};
use super::Repository;

pub struct UserRepository {
    pool: Pool<Sqlite>,
}

impl UserRepository {
    pub fn new(pool: Pool<Sqlite>) -> Self {
        Self { pool }
    }

    /// Find user by name
    pub async fn find_by_name(&self, name: &str) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT * FROM users WHERE name = ?",
            name
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    /// Create a new user from request
    pub async fn create_user(&self, request: &CreateUserRequest) -> Result<User> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        // Hash password if provided
        let password_hash = if let Some(password) = &request.password {
            Some(hash_password(password)?)
        } else {
            None
        };

        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, name, password_hash, is_administrator, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            id,
            request.name,
            password_hash,
            request.is_administrator,
            now,
            now
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    /// Update user information
    pub async fn update_user(&self, id: &str, request: &UpdateUserRequest) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            r#"
            UPDATE users 
            SET name = COALESCE(?, name),
                is_administrator = COALESCE(?, is_administrator),
                is_hidden = COALESCE(?, is_hidden),
                is_disabled = COALESCE(?, is_disabled),
                updated_at = ?
            WHERE id = ?
            "#,
            request.name,
            request.is_administrator,
            request.is_hidden,
            request.is_disabled,
            now,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Get all users with pagination
    pub async fn list_users(&self, limit: i64, offset: i64) -> Result<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            "SELECT * FROM users ORDER BY name LIMIT ? OFFSET ?",
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    /// Update user's last login time
    pub async fn update_last_login(&self, id: &str) -> Result<()> {
        let now = Utc::now();

        sqlx::query!(
            "UPDATE users SET last_login_date = ?, last_activity_date = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    /// Verify user password
    pub async fn verify_password(&self, name: &str, password: &str) -> Result<Option<User>> {
        let user = self.find_by_name(name).await?;
        
        if let Some(user) = user {
            if let Some(hash) = &user.password_hash {
                if verify_password_hash(password, hash)? {
                    return Ok(Some(user));
                }
            }
        }
        
        Ok(None)
    }
}

#[async_trait]
impl Repository<User, String> for UserRepository {
    async fn find_by_id(&self, id: &String) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT * FROM users WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    async fn create(&self, user: &User) -> Result<User> {
        let created_user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            RETURNING *
            "#,
            user.id,
            user.name,
            user.password_hash,
            user.is_administrator,
            user.is_hidden,
            user.is_disabled,
            user.created_at,
            user.updated_at
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(created_user)
    }

    async fn update(&self, user: &User) -> Result<()> {
        sqlx::query!(
            r#"
            UPDATE users 
            SET name = ?, password_hash = ?, is_administrator = ?, is_hidden = ?, is_disabled = ?, updated_at = ?
            WHERE id = ?
            "#,
            user.name,
            user.password_hash,
            user.is_administrator,
            user.is_hidden,
            user.is_disabled,
            user.updated_at,
            user.id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    async fn delete(&self, id: &String) -> Result<()> {
        sqlx::query!("DELETE FROM users WHERE id = ?", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

/// Hash a password using bcrypt
fn hash_password(password: &str) -> Result<String> {
    use bcrypt::{hash, DEFAULT_COST};
    let hashed = hash(password, DEFAULT_COST)?;
    Ok(hashed)
}

/// Verify a password against a hash
pub fn verify_password_hash(password: &str, hash: &str) -> Result<bool> {
    use bcrypt::verify;
    let is_valid = verify(password, hash)?;
    Ok(is_valid)
}

#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::SqlitePool;
    use tempfile::TempDir;
    use crate::database::migrations::run_migrations;

    async fn setup_test_db() -> Pool<Sqlite> {
        let temp_dir = TempDir::new().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        let pool = SqlitePool::connect(&format!("sqlite:{}", db_path.display()))
            .await
            .unwrap();

        run_migrations(&pool).await.unwrap();
        pool
    }

    #[tokio::test]
    async fn test_user_crud() {
        let pool = setup_test_db().await;
        let repo = UserRepository::new(pool);

        // Create user
        let request = CreateUserRequest {
            name: "testuser".to_string(),
            password: Some("password123".to_string()),
            is_administrator: false,
        };

        let user = repo.create_user(&request).await.unwrap();
        assert_eq!(user.name, "testuser");
        assert!(!user.is_administrator);
        assert!(user.password_hash.is_some());

        // Find by ID
        let found_user = repo.find_by_id(&user.id).await.unwrap();
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().name, "testuser");

        // Find by name
        let found_by_name = repo.find_by_name("testuser").await.unwrap();
        assert!(found_by_name.is_some());

        // Verify password
        let verified_user = repo.verify_password("testuser", "password123").await.unwrap();
        assert!(verified_user.is_some());

        let wrong_password = repo.verify_password("testuser", "wrongpassword").await.unwrap();
        assert!(wrong_password.is_none());

        // Update user
        let update_request = UpdateUserRequest {
            name: Some("updateduser".to_string()),
            is_administrator: Some(true),
            is_hidden: None,
            is_disabled: None,
        };

        repo.update_user(&user.id, &update_request).await.unwrap();

        let updated_user = repo.find_by_id(&user.id).await.unwrap().unwrap();
        assert_eq!(updated_user.name, "updateduser");
        assert!(updated_user.is_administrator);

        // Delete user
        repo.delete(&user.id).await.unwrap();
        let deleted_user = repo.find_by_id(&user.id).await.unwrap();
        assert!(deleted_user.is_none());
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password_123";
        let hash = hash_password(password).unwrap();
        
        assert!(verify_password_hash(password, &hash).unwrap());
        assert!(!verify_password_hash("wrong_password", &hash).unwrap());
    }
}

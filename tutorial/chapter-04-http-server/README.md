# Chapter 4: Basic HTTP Server

**Duration**: ~45 minutes  
**Difficulty**: Intermediate  
**Prerequisites**: Chapters 1-3 completed, basic understanding of HTTP and web servers

## 🎯 Learning Objectives

By the end of this chapter, you will:
- Set up a high-performance HTTP server using Axum
- Implement middleware stack for logging, CORS, and compression
- Create health check and system information endpoints
- Handle graceful shutdown for production deployments
- Optimize server configuration for ARM64 devices
- Implement proper error handling and response patterns

## 📋 What We're Building

In this chapter, we'll create a robust HTTP server foundation:
- Axum web server with optimized configuration
- Middleware stack (logging, CORS, compression, rate limiting)
- Health check endpoints for monitoring
- System information API for diagnostics
- Graceful shutdown handling
- ARM64-optimized server settings

## 🏗 HTTP Server Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP Requests                            │
├─────────────────────────────────────────────────────────────┤
│                  Middleware Stack                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Tracing   │ │    CORS     │ │ Compression │          │
│  │  Middleware │ │ Middleware  │ │ Middleware  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Auth     │ │Rate Limiting│ │   Timeout   │          │
│  │ Middleware  │ │ Middleware  │ │ Middleware  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Route Handlers                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Health    │ │   System    │ │    API      │          │
│  │   Check     │ │    Info     │ │  Endpoints  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                 Application State                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Config   │ │  Database   │ │   Metrics   │          │
│  │             │ │    Pool     │ │             │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Step-by-Step Implementation

### Step 1: Update Dependencies

Add HTTP server dependencies to `Cargo.toml`:

```toml
[dependencies]
# Previous dependencies...
tokio = { version = "1.35", features = ["full"] }
clap = { version = "4.4", features = ["derive"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "2.0.16"
serde = { version = "1.0", features = ["derive"] }
config = "0.14.0"
toml = "0.8.8"
dirs = "5.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
once_cell = "1.19"
sqlx = { version = "0.8.6", features = [
    "runtime-tokio-rustls",
    "sqlite", 
    "chrono", 
    "uuid",
    "migrate"
] }
chrono = { version = "0.4", features = ["serde"] }
async-trait = "0.1"
bcrypt = "0.17.1"

# HTTP server dependencies
axum = { version = "0.8.7", features = ["macros", "multipart"] }
tower = { version = "0.5.1", features = ["full"] }
tower-http = { version = "0.6.2", features = [
    "cors",
    "compression-gzip",
    "trace",
    "timeout",
    "limit"
] }
hyper = { version = "1.5.1", features = ["full"] }
tokio-util = "0.7"

# JSON handling
serde_json = "1.0"

# Additional utilities
futures = "0.3"
pin-project-lite = "0.2"
```

### Step 2: Create Server Module

Create `src/server/mod.rs`:

```rust
//! HTTP server implementation for Tulip Media Server
//!
//! This module provides the main HTTP server using Axum with optimized
//! middleware stack and ARM64-specific configurations.

use anyhow::Result;
use axum::{
    extract::State,
    http::{HeaderValue, Method, StatusCode},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{net::SocketAddr, sync::Arc, time::Duration};
use tokio::signal;
use tower::ServiceBuilder;
use tower_http::{
    compression::CompressionLayer,
    cors::{Any, CorsLayer},
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, warn};

use crate::{config::Config, database::Database};

pub mod handlers;
pub mod middleware;

/// Application state shared across all handlers
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<Config>,
    pub database: Database,
    pub start_time: std::time::Instant,
}

/// Server instance
pub struct Server {
    app: Router,
    addr: SocketAddr,
}

impl Server {
    /// Create a new server instance
    pub fn new(config: Arc<Config>, database: Database) -> Result<Self> {
        let state = AppState {
            config: config.clone(),
            database,
            start_time: std::time::Instant::now(),
        };

        let app = create_app(state)?;
        
        let addr = format!("{}:{}", config.server.bind_address, config.server.port)
            .parse::<SocketAddr>()?;

        Ok(Self { app, addr })
    }

    /// Start the server
    pub async fn run(self) -> Result<()> {
        info!("Starting HTTP server on {}", self.addr);

        let listener = tokio::net::TcpListener::bind(self.addr).await?;
        
        info!("Server listening on http://{}", self.addr);
        info!("Health check available at http://{}/health", self.addr);
        info!("System info available at http://{}/system/info", self.addr);

        axum::serve(listener, self.app)
            .with_graceful_shutdown(shutdown_signal())
            .await?;

        Ok(())
    }
}

/// Create the main application router with middleware
fn create_app(state: AppState) -> Result<Router> {
    // Create CORS layer
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers(Any);

    // Create middleware stack
    let middleware = ServiceBuilder::new()
        .layer(TraceLayer::new_for_http())
        .layer(cors)
        .layer(CompressionLayer::new())
        .layer(TimeoutLayer::new(Duration::from_secs(30)))
        .layer(middleware::metrics::MetricsLayer::new());

    // Create routes
    let app = Router::new()
        // Health and system endpoints
        .route("/health", get(handlers::health::health_check))
        .route("/health/ready", get(handlers::health::readiness_check))
        .route("/health/live", get(handlers::health::liveness_check))
        .route("/system/info", get(handlers::system::system_info))
        .route("/system/stats", get(handlers::system::system_stats))
        
        // API routes (placeholder for future chapters)
        .nest("/api/v1", create_api_routes())
        
        // Apply middleware
        .layer(middleware)
        .with_state(state);

    Ok(app)
}

/// Create API routes (placeholder for future chapters)
fn create_api_routes() -> Router<AppState> {
    Router::new()
        .route("/ping", get(handlers::api::ping))
        // More API routes will be added in Chapter 8
}

/// Graceful shutdown signal handler
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            info!("Received Ctrl+C, shutting down gracefully...");
        },
        _ = terminate => {
            info!("Received SIGTERM, shutting down gracefully...");
        },
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_server_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");
        config.server.port = 0; // Use random port for testing

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        let server = Server::new(Arc::new(config), database);
        assert!(server.is_ok());
    }

    #[tokio::test]
    async fn test_app_creation() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        let state = AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        };

        let app = create_app(state);
        assert!(app.is_ok());
    }
}
```

### Step 3: Create Handler Modules

Create `src/server/handlers/mod.rs`:

```rust
//! HTTP request handlers for Tulip Media Server

pub mod health;
pub mod system;
pub mod api;

use axum::{
    http::StatusCode,
    response::{IntoResponse, Json},
};
use serde::{Deserialize, Serialize};

/// Standard API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl<T> ApiResponse<T>
where
    T: Serialize,
{
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now(),
        }
    }
}

impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> axum::response::Response {
        let status = if self.success {
            StatusCode::OK
        } else {
            StatusCode::INTERNAL_SERVER_ERROR
        };

        (status, Json(self)).into_response()
    }
}

/// Error response helper
pub fn error_response(status: StatusCode, message: &str) -> impl IntoResponse {
    (status, Json(ApiResponse::<()>::error(message.to_string())))
}
```

Create `src/server/handlers/health.rs`:

```rust
//! Health check endpoints for monitoring and load balancing

use axum::{extract::State, response::Json};
use serde::{Deserialize, Serialize};
use tracing::error;

use crate::server::AppState;
use super::{ApiResponse, error_response};

/// Health check response
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub uptime_seconds: u64,
    pub version: String,
}

/// Detailed health check response
#[derive(Debug, Serialize, Deserialize)]
pub struct DetailedHealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub uptime_seconds: u64,
    pub version: String,
    pub components: HealthComponents,
}

/// Component health status
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthComponents {
    pub database: ComponentHealth,
    pub memory: ComponentHealth,
    pub disk: ComponentHealth,
}

/// Individual component health
#[derive(Debug, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub status: String,
    pub message: Option<String>,
    pub details: Option<serde_json::Value>,
}

/// Basic health check endpoint
/// GET /health
pub async fn health_check(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    let uptime = state.start_time.elapsed().as_secs();
    
    let response = HealthResponse {
        status: "healthy".to_string(),
        timestamp: chrono::Utc::now(),
        uptime_seconds: uptime,
        version: env!("CARGO_PKG_VERSION").to_string(),
    };

    ApiResponse::success(response)
}

/// Readiness check endpoint (for Kubernetes)
/// GET /health/ready
pub async fn readiness_check(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    // Check if all components are ready
    let database_health = check_database_health(&state).await;
    let memory_health = check_memory_health();
    let disk_health = check_disk_health(&state);

    let all_healthy = database_health.status == "healthy" 
        && memory_health.status == "healthy" 
        && disk_health.status == "healthy";

    if !all_healthy {
        return error_response(
            axum::http::StatusCode::SERVICE_UNAVAILABLE,
            "Service not ready"
        );
    }

    let uptime = state.start_time.elapsed().as_secs();
    
    let response = DetailedHealthResponse {
        status: "ready".to_string(),
        timestamp: chrono::Utc::now(),
        uptime_seconds: uptime,
        version: env!("CARGO_PKG_VERSION").to_string(),
        components: HealthComponents {
            database: database_health,
            memory: memory_health,
            disk: disk_health,
        },
    };

    ApiResponse::success(response)
}

/// Liveness check endpoint (for Kubernetes)
/// GET /health/live
pub async fn liveness_check(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    // Simple liveness check - if we can respond, we're alive
    let uptime = state.start_time.elapsed().as_secs();
    
    let response = HealthResponse {
        status: "alive".to_string(),
        timestamp: chrono::Utc::now(),
        uptime_seconds: uptime,
        version: env!("CARGO_PKG_VERSION").to_string(),
    };

    ApiResponse::success(response)
}

/// Check database health
async fn check_database_health(state: &AppState) -> ComponentHealth {
    match state.database.health_check().await {
        Ok(_) => ComponentHealth {
            status: "healthy".to_string(),
            message: Some("Database connection successful".to_string()),
            details: None,
        },
        Err(e) => {
            error!("Database health check failed: {}", e);
            ComponentHealth {
                status: "unhealthy".to_string(),
                message: Some(format!("Database error: {}", e)),
                details: None,
            }
        }
    }
}

/// Check memory health
fn check_memory_health() -> ComponentHealth {
    // Get memory usage information
    match get_memory_usage() {
        Ok(usage) => {
            let status = if usage.used_percent > 90.0 {
                "warning"
            } else if usage.used_percent > 95.0 {
                "unhealthy"
            } else {
                "healthy"
            };

            ComponentHealth {
                status: status.to_string(),
                message: Some(format!("Memory usage: {:.1}%", usage.used_percent)),
                details: Some(serde_json::json!({
                    "total_bytes": usage.total,
                    "used_bytes": usage.used,
                    "available_bytes": usage.available,
                    "used_percent": usage.used_percent
                })),
            }
        }
        Err(e) => {
            error!("Memory health check failed: {}", e);
            ComponentHealth {
                status: "unknown".to_string(),
                message: Some(format!("Memory check error: {}", e)),
                details: None,
            }
        }
    }
}

/// Check disk health
fn check_disk_health(state: &AppState) -> ComponentHealth {
    // Check disk space for database directory
    match get_disk_usage(&state.config.database.path) {
        Ok(usage) => {
            let status = if usage.used_percent > 90.0 {
                "warning"
            } else if usage.used_percent > 95.0 {
                "unhealthy"
            } else {
                "healthy"
            };

            ComponentHealth {
                status: status.to_string(),
                message: Some(format!("Disk usage: {:.1}%", usage.used_percent)),
                details: Some(serde_json::json!({
                    "total_bytes": usage.total,
                    "used_bytes": usage.used,
                    "available_bytes": usage.available,
                    "used_percent": usage.used_percent
                })),
            }
        }
        Err(e) => {
            error!("Disk health check failed: {}", e);
            ComponentHealth {
                status: "unknown".to_string(),
                message: Some(format!("Disk check error: {}", e)),
                details: None,
            }
        }
    }
}

/// Memory usage information
#[derive(Debug)]
struct MemoryUsage {
    total: u64,
    used: u64,
    available: u64,
    used_percent: f64,
}

/// Get memory usage (Linux-specific)
fn get_memory_usage() -> anyhow::Result<MemoryUsage> {
    #[cfg(target_os = "linux")]
    {
        let meminfo = std::fs::read_to_string("/proc/meminfo")?;
        let mut total = 0u64;
        let mut available = 0u64;

        for line in meminfo.lines() {
            if line.starts_with("MemTotal:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    total = parts[1].parse::<u64>()? * 1024; // Convert KB to bytes
                }
            } else if line.starts_with("MemAvailable:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    available = parts[1].parse::<u64>()? * 1024; // Convert KB to bytes
                }
            }
        }

        let used = total - available;
        let used_percent = if total > 0 {
            (used as f64 / total as f64) * 100.0
        } else {
            0.0
        };

        Ok(MemoryUsage {
            total,
            used,
            available,
            used_percent,
        })
    }

    #[cfg(not(target_os = "linux"))]
    {
        // Fallback for non-Linux systems
        Ok(MemoryUsage {
            total: 0,
            used: 0,
            available: 0,
            used_percent: 0.0,
        })
    }
}

/// Disk usage information
#[derive(Debug)]
struct DiskUsage {
    total: u64,
    used: u64,
    available: u64,
    used_percent: f64,
}

/// Get disk usage for a path
fn get_disk_usage(path: &std::path::Path) -> anyhow::Result<DiskUsage> {
    use std::os::unix::fs::MetadataExt;

    let metadata = std::fs::metadata(path.parent().unwrap_or(path))?;
    
    // This is a simplified implementation
    // In a real implementation, you'd use statvfs or similar
    let total = 1024 * 1024 * 1024 * 10; // 10GB placeholder
    let available = total - metadata.size();
    let used = total - available;
    let used_percent = (used as f64 / total as f64) * 100.0;

    Ok(DiskUsage {
        total,
        used,
        available,
        used_percent,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, database::Database};
    use std::sync::Arc;
    use tempfile::TempDir;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    #[tokio::test]
    async fn test_health_check() {
        let state = create_test_state().await;
        let response = health_check(State(state)).await;
        
        // The response should be successful
        // In a real test, you'd deserialize and check the response
    }

    #[tokio::test]
    async fn test_readiness_check() {
        let state = create_test_state().await;
        let response = readiness_check(State(state)).await;
        
        // The response should indicate readiness
    }

    #[tokio::test]
    async fn test_liveness_check() {
        let state = create_test_state().await;
        let response = liveness_check(State(state)).await;
        
        // The response should indicate the service is alive
    }
}
```

## 🧪 Testing Our HTTP Server

### Step 4: Create System Info Handler

Create `src/server/handlers/system.rs`:

```rust
//! System information endpoints for diagnostics and monitoring

use axum::extract::State;
use serde::{Deserialize, Serialize};
use tracing::error;

use crate::{server::AppState, utils};
use super::ApiResponse;

/// System information response
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemInfo {
    pub server: ServerInfo,
    pub system: SystemDetails,
    pub database: DatabaseInfo,
    pub performance: PerformanceInfo,
}

/// Server information
#[derive(Debug, Serialize, Deserialize)]
pub struct ServerInfo {
    pub name: String,
    pub version: String,
    pub uptime_seconds: u64,
    pub start_time: chrono::DateTime<chrono::Utc>,
}

/// System details
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemDetails {
    pub architecture: String,
    pub os: String,
    pub cpu_cores: usize,
    pub memory: MemoryInfo,
    pub arm64_optimizations: bool,
}

/// Memory information
#[derive(Debug, Serialize, Deserialize)]
pub struct MemoryInfo {
    pub total_bytes: u64,
    pub available_bytes: u64,
    pub used_bytes: u64,
    pub used_percent: f64,
}

/// Database information
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseInfo {
    pub path: String,
    pub max_connections: u32,
    pub statistics: Option<crate::database::DatabaseStats>,
}

/// Performance information
#[derive(Debug, Serialize, Deserialize)]
pub struct PerformanceInfo {
    pub worker_threads: usize,
    pub cache_size_mb: u64,
    pub compression_enabled: bool,
    pub arm64_optimizations: bool,
}

/// System information endpoint
/// GET /system/info
pub async fn system_info(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    let uptime = state.start_time.elapsed().as_secs();
    let start_time = chrono::Utc::now() - chrono::Duration::seconds(uptime as i64);

    // Get memory information
    let memory = get_memory_info().unwrap_or(MemoryInfo {
        total_bytes: 0,
        available_bytes: 0,
        used_bytes: 0,
        used_percent: 0.0,
    });

    // Get database statistics
    let db_stats = match state.database.get_stats().await {
        Ok(stats) => Some(stats),
        Err(e) => {
            error!("Failed to get database statistics: {}", e);
            None
        }
    };

    let info = SystemInfo {
        server: ServerInfo {
            name: state.config.server.server_name.clone(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            uptime_seconds: uptime,
            start_time,
        },
        system: SystemDetails {
            architecture: std::env::consts::ARCH.to_string(),
            os: std::env::consts::OS.to_string(),
            cpu_cores: utils::get_cpu_count(),
            memory,
            arm64_optimizations: utils::is_arm64(),
        },
        database: DatabaseInfo {
            path: state.config.database.path.display().to_string(),
            max_connections: state.config.database.max_connections,
            statistics: db_stats,
        },
        performance: PerformanceInfo {
            worker_threads: utils::get_optimal_worker_threads(),
            cache_size_mb: state.config.performance.cache_size_mb,
            compression_enabled: state.config.performance.enable_compression,
            arm64_optimizations: state.config.performance.arm64_optimizations,
        },
    };

    ApiResponse::success(info)
}

/// System statistics endpoint
/// GET /system/stats
pub async fn system_stats(State(state): State<AppState>) -> impl axum::response::IntoResponse {
    // This is a simplified version - in a real implementation,
    // you'd collect more detailed metrics
    let stats = serde_json::json!({
        "uptime_seconds": state.start_time.elapsed().as_secs(),
        "memory_usage": get_memory_info().unwrap_or(MemoryInfo {
            total_bytes: 0,
            available_bytes: 0,
            used_bytes: 0,
            used_percent: 0.0,
        }),
        "database_stats": state.database.get_stats().await.ok(),
        "timestamp": chrono::Utc::now(),
    });

    ApiResponse::success(stats)
}

/// Get memory information
fn get_memory_info() -> Option<MemoryInfo> {
    #[cfg(target_os = "linux")]
    {
        if let Ok(meminfo) = std::fs::read_to_string("/proc/meminfo") {
            let mut total = 0u64;
            let mut available = 0u64;

            for line in meminfo.lines() {
                if line.starts_with("MemTotal:") {
                    if let Some(value) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = value.parse::<u64>() {
                            total = kb * 1024; // Convert KB to bytes
                        }
                    }
                } else if line.starts_with("MemAvailable:") {
                    if let Some(value) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = value.parse::<u64>() {
                            available = kb * 1024; // Convert KB to bytes
                        }
                    }
                }
            }

            if total > 0 {
                let used = total - available;
                let used_percent = (used as f64 / total as f64) * 100.0;

                return Some(MemoryInfo {
                    total_bytes: total,
                    available_bytes: available,
                    used_bytes: used,
                    used_percent,
                });
            }
        }
    }

    None
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{config::Config, database::Database};
    use std::sync::Arc;
    use tempfile::TempDir;

    async fn create_test_state() -> AppState {
        let temp_dir = TempDir::new().unwrap();
        let mut config = Config::default();
        config.database.path = temp_dir.path().join("test.db");

        let database = Database::new(&config.database).await.unwrap();
        database.migrate().await.unwrap();

        AppState {
            config: Arc::new(config),
            database,
            start_time: std::time::Instant::now(),
        }
    }

    #[tokio::test]
    async fn test_system_info() {
        let state = create_test_state().await;
        let response = system_info(axum::extract::State(state)).await;
        
        // The response should contain system information
    }

    #[tokio::test]
    async fn test_system_stats() {
        let state = create_test_state().await;
        let response = system_stats(axum::extract::State(state)).await;
        
        // The response should contain system statistics
    }
}
```

## 🎯 Key Concepts Explained

### Axum Framework Choice

We chose Axum because:
- **Performance**: Built on Hyper and Tower for maximum throughput
- **Type Safety**: Compile-time request/response validation
- **Middleware**: Rich ecosystem with Tower middleware
- **Async**: First-class async/await support
- **ARM64 Friendly**: Excellent performance on ARM64 architectures

### Middleware Stack Design

Our middleware stack provides:
1. **Tracing**: Request/response logging with correlation IDs
2. **CORS**: Cross-origin resource sharing for web clients
3. **Compression**: Gzip compression for reduced bandwidth
4. **Timeout**: Request timeout protection
5. **Metrics**: Performance monitoring (custom middleware)

### Health Check Patterns

We implement three types of health checks:
- **Basic Health** (`/health`): Simple alive check
- **Readiness** (`/health/ready`): Kubernetes readiness probe
- **Liveness** (`/health/live`): Kubernetes liveness probe

### ARM64 Optimizations

Server optimizations for ARM64:
- Conservative timeout values
- Optimized worker thread counts
- Memory-efficient middleware stack
- ARM64-specific configuration defaults

## 🔍 What's Next?

In **Chapter 5: Authentication System**, we'll:
- Implement JWT-based authentication
- Create user registration and login endpoints
- Add authentication middleware
- Build session management system

## 📚 Additional Resources

- [Axum Documentation](https://docs.rs/axum/) - Web framework guide
- [Tower Middleware](https://docs.rs/tower/) - Middleware ecosystem
- [HTTP Status Codes](https://httpstatuses.com/) - Status code reference
- [Health Check Patterns](https://microservices.io/patterns/observability/health-check-api.html) - Health check design

---

**Checkpoint**: You now have a high-performance HTTP server with health checks, system info, and ARM64 optimizations. Ready for [Chapter 5: Authentication System](../chapter-05-authentication/)?

use std::process::Command;
use tempfile::TempDir;
use tokio::time::{sleep, Duration};

#[tokio::test]
async fn test_server_startup() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    // Start server in background
    let mut child = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "0", // Use random port
        ])
        .current_dir(".")
        .spawn()
        .expect("Failed to start server");

    // Give server time to start
    sleep(Duration::from_secs(2)).await;

    // Kill the server
    child.kill().expect("Failed to kill server");
    child.wait().expect("Failed to wait for server");
}

#[test]
fn test_server_help() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--help"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Tulip Media Server"));
    assert!(stdout.contains("--port"));
}

#[test]
fn test_server_version() {
    let output = Command::new("cargo")
        .args(&["run", "--", "--version"])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("tulip-media"));
}

#[tokio::test]
async fn test_health_endpoints() {
    // This test would require starting the server and making HTTP requests
    // For now, we'll just test that the server can be created
    
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    // Test that server starts without errors
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "18096", // Use different port to avoid conflicts
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    // Server should start and then be killed by timeout
    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Starting HTTP server"));
}

#[test]
fn test_configuration_with_server() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("server_config.toml");
    let data_dir = temp_dir.path().join("data");
    
    // Create server-specific configuration
    let config_content = r#"
[server]
port = 18097
server_name = "Test HTTP Server"
bind_address = "127.0.0.1"

[database]
max_connections = 5

[performance]
cache_size_mb = 64
enable_compression = true
"#;
    
    std::fs::write(&config_path, config_content).unwrap();
    
    let output = Command::new("timeout")
        .args(&[
            "2s",
            "cargo", "run", "--",
            "--config", config_path.to_str().unwrap(),
            "--data-dir", data_dir.to_str().unwrap(),
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Server will bind to: 127.0.0.1:18097"));
    assert!(stdout.contains("Test HTTP Server"));
    assert!(stdout.contains("Starting HTTP server"));
}

#[test]
fn test_graceful_shutdown_signal() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    // Start server and send SIGTERM
    let output = Command::new("timeout")
        .args(&[
            "--signal=TERM",
            "3s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "18098",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Starting HTTP server"));
    
    // On systems that support it, should see graceful shutdown message
    if stdout.contains("Received SIGTERM") {
        assert!(stdout.contains("shutting down gracefully"));
    }
}

#[test]
fn test_invalid_port_error() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("cargo")
        .args(&[
            "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "0", // Invalid port for binding
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    // Should fail with port validation error
    assert!(!output.status.success());
    let stderr = String::from_utf8_lossy(&output.stderr);
    assert!(stderr.contains("Port must be greater than 0"));
}

#[test]
fn test_database_initialization_with_server() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "1s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "18099",
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("Database initialized"));
    assert!(stdout.contains("Database migrations completed"));
    assert!(stdout.contains("Starting HTTP server"));
    
    // Verify database file was created
    let db_path = data_dir.join("tulip.db");
    assert!(db_path.exists());
}

#[test]
fn test_system_info_logging() {
    let temp_dir = TempDir::new().unwrap();
    let data_dir = temp_dir.path().join("data");
    
    let output = Command::new("timeout")
        .args(&[
            "1s",
            "cargo", "run", "--",
            "--data-dir", data_dir.to_str().unwrap(),
            "--port", "18100",
            "--debug", // Enable debug logging
        ])
        .current_dir(".")
        .output()
        .expect("Failed to execute command");

    let stdout = String::from_utf8_lossy(&output.stdout);
    assert!(stdout.contains("System Information:"));
    assert!(stdout.contains("Architecture:"));
    assert!(stdout.contains("CPU cores:"));
    assert!(stdout.contains("Optimal worker threads:"));
    
    // Check for ARM64-specific information if running on ARM64
    if cfg!(target_arch = "aarch64") {
        assert!(stdout.contains("ARM64 optimizations: enabled"));
    }
}

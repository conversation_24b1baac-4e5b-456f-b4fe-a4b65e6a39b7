# Tulip Media Server Configuration
# Optimized for ARM64 devices like Nano Pi M4 V2

[server]
bind_address = "0.0.0.0"
port = 8096
server_name = "Tulip Media Server"
enable_https = false
# cert_path = "/path/to/cert.pem"
# key_path = "/path/to/key.pem"

[database]
# Database will be created in data directory
max_connections = 10
connection_timeout = 30

[media]
# Add your media library paths here
library_paths = [
    "/media/movies",
    "/media/tv", 
    "/media/music"
]
scan_interval = 3600  # 1 hour in seconds
thumbnail_cache_size = 524288000  # 500MB
metadata_cache_size = 104857600   # 100MB

supported_video_formats = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v"]
supported_audio_formats = ["mp3", "flac", "aac", "ogg", "wav", "m4a"]
supported_image_formats = ["jpg", "jpeg", "png", "webp", "bmp"]

[streaming]
enable_transcoding = true
max_concurrent_streams = 4  # Conservative for ARM64
hardware_acceleration = true
video_codecs = ["h264", "h265"]
audio_codecs = ["aac", "mp3"]
max_bitrate = 20000000  # 20 Mbps
segment_duration = 6

[security]
# JWT secret will be auto-generated if not specified
# jwt_secret = "your-secret-key-here"
session_timeout = 86400  # 24 hours
max_login_attempts = 5
enable_api_keys = true

[performance]
# Leave worker_threads unset for auto-detection
# worker_threads = 4
max_blocking_threads = 4
enable_compression = true
cache_size_mb = 256  # Conservative for ARM64
arm64_optimizations = true

# Getting Started with Tulip Media Server

## Quick Start (5 minutes)

### 1. Prerequisites
- Linux system (Ubuntu 20.04+, Debian 11+, or Armbian)
- Rust 1.70+ installed
- At least 2GB RAM
- Media files to serve

### 2. Install and <PERSON>
```bash
# Clone the repository
git clone https://github.com/yourusername/tulip-media.git
cd tulip-media

# Build the project
cargo build --release

# Create basic configuration
cp config-dev.toml config.toml

# Edit config.toml with your media paths
nano config.toml

# Run the server
./target/release/tulip-media --config config.toml
```

### 3. Access the Server
- Open your browser to `http://localhost:8096`
- Use any Jellyfin client app
- Default admin user (no password required initially)

## First-Time Setup

### 1. Configuration File
Edit `config.toml` with your media library paths:

```toml
[media]
library_paths = [
    "/home/<USER>/Movies",
    "/home/<USER>/TV Shows",
    "/home/<USER>/Music"
]
```

### 2. Media Library Structure
Organize your media following these conventions:

```
Movies/
├── Movie Title (2023)/
│   ├── Movie Title (2023).mp4
│   ├── poster.jpg
│   └── fanart.jpg

TV Shows/
├── Show Name/
│   ├── Season 01/
│   │   ├── Show Name - S01E01 - Episode Title.mp4
│   │   └── Show Name - S01E02 - Episode Title.mp4
│   ├── poster.jpg
│   └── fanart.jpg

Music/
├── Artist Name/
│   ├── Album Name/
│   │   ├── 01 - Track Name.mp3
│   │   ├── 02 - Track Name.mp3
│   │   └── cover.jpg
```

### 3. Initial Scan
The server will automatically scan your media libraries on first run. This may take several minutes depending on the size of your collection.

## Using the Server

### Web Interface
- **URL**: `http://your-server-ip:8096`
- **Features**: Browse media, manage libraries, user settings
- **Compatibility**: Works with any modern web browser

### Jellyfin Clients
Tulip Media Server is fully compatible with Jellyfin clients:

#### Mobile Apps
- **Android**: Jellyfin for Android
- **iOS**: Jellyfin for iOS
- **Features**: Offline sync, background playback, casting

#### Desktop Apps
- **Windows**: Jellyfin Media Player
- **macOS**: Jellyfin Media Player
- **Linux**: Jellyfin Media Player

#### Smart TV Apps
- **Android TV**: Jellyfin for Android TV
- **Fire TV**: Jellyfin for Fire TV
- **Roku**: Jellyfin Roku Channel

### API Access
All Jellyfin API endpoints are supported:

```bash
# Get server info
curl http://localhost:8096/System/Info

# List users
curl http://localhost:8096/Users

# Get media items
curl http://localhost:8096/Items
```

## Basic Operations

### Adding Media
1. Place media files in configured library paths
2. The server automatically detects new files
3. Metadata is extracted and stored
4. Thumbnails are generated automatically

### User Management
1. Access `/Users` endpoint
2. Create new users with appropriate permissions
3. Set passwords and access restrictions
4. Configure parental controls if needed

### Library Management
1. Add new library paths in configuration
2. Restart server or trigger rescan
3. Monitor scan progress in logs
4. Verify media appears in web interface

## Performance Optimization

### For Nano Pi M4 V2
```toml
[performance]
worker_threads = 4  # 4 CPU cores
max_blocking_threads = 8
arm64_optimizations = true
cache_size_mb = 1024  # 1GB cache

[streaming]
max_concurrent_streams = 2  # Conservative for ARM64
hardware_acceleration = true
```

### System Tuning
```bash
# Set CPU governor to performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# Optimize I/O scheduler
echo bfq | sudo tee /sys/block/sda/queue/scheduler

# Increase network buffers
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Troubleshooting

### Common Issues

#### Server Won't Start
```bash
# Check logs
RUST_LOG=debug ./target/release/tulip-media

# Verify configuration
./target/release/tulip-media --config config.toml --check

# Check port availability
sudo netstat -tlnp | grep 8096
```

#### Media Not Appearing
```bash
# Check library paths
ls -la /path/to/your/media

# Verify file permissions
sudo chown -R $USER:$USER /path/to/your/media

# Trigger manual scan
curl -X POST http://localhost:8096/Library/VirtualFolders/LibraryScan
```

#### Poor Streaming Performance
```bash
# Check CPU usage
htop

# Monitor network
iftop

# Verify hardware acceleration
ffmpeg -hwaccel rkmpp -i input.mp4 -c:v h264_rkmpp output.mp4
```

### Log Analysis
```bash
# Enable debug logging
export RUST_LOG=tulip_media=debug,tower_http=debug

# Filter specific errors
grep "ERROR" /var/log/tulip-media.log
grep "transcoding" /var/log/tulip-media.log
grep "session" /var/log/tulip-media.log
```

## Advanced Features

### Hardware Acceleration
Enable RK3399 VPU support:

```bash
# Install media codecs
sudo apt install libmali-rk-dev librockchip-mpp-dev

# Test hardware acceleration
ffmpeg -hwaccel rkmpp -i input.mp4 -c:v h264_rkmpp -b:v 2M output.mp4
```

### Custom Transcoding Profiles
Create custom profiles for different devices:

```toml
[streaming.profiles]
mobile = { max_width = 854, max_height = 480, max_bitrate = 1000000 }
tablet = { max_width = 1280, max_height = 720, max_bitrate = 3000000 }
tv = { max_width = 1920, max_height = 1080, max_bitrate = 8000000 }
```

### External Authentication
Configure LDAP or OAuth:

```toml
[auth]
provider = "ldap"
ldap_url = "ldap://ldap.example.com"
ldap_base_dn = "dc=example,dc=com"
```

## Security Best Practices

### Network Security
```bash
# Configure firewall
sudo ufw allow 8096/tcp
sudo ufw enable

# Use VPN for remote access
# Consider reverse proxy with HTTPS
```

### Authentication
```toml
[security]
jwt_secret = "your-very-long-random-secret-key"
session_timeout = 86400
max_login_attempts = 3
enable_api_keys = true
```

### File Permissions
```bash
# Create dedicated user
sudo useradd -r -s /bin/false tulip

# Set proper permissions
sudo chown -R tulip:tulip /opt/tulip-media
sudo chmod 755 /opt/tulip-media
```

## Monitoring and Maintenance

### Health Monitoring
```bash
# Check server status
curl http://localhost:8096/System/Info

# Monitor resource usage
htop
iotop
df -h

# Check database health
sqlite3 data/tulip.db "PRAGMA integrity_check;"
```

### Regular Maintenance
```bash
# Update dependencies
cargo update

# Clean cache
rm -rf data/cache/*

# Backup configuration
cp config.toml config.toml.backup

# Check for updates
git pull origin main
```

### Performance Monitoring
```bash
# Monitor transcoding
watch -n 1 'ps aux | grep ffmpeg'

# Check network usage
iftop -i eth0

# Monitor disk I/O
iotop -o
```

## Getting Help

### Documentation
- **API Reference**: [docs/api.md](docs/api.md)
- **Configuration**: [docs/configuration.md](docs/configuration.md)
- **Performance**: [docs/performance.md](docs/performance.md)

### Community Support
- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and share tips
- **Discord**: Real-time chat and support

### Reporting Issues
When reporting issues, include:
- Tulip Media Server version
- Operating system and architecture
- Configuration file (sanitized)
- Relevant log entries
- Steps to reproduce

## Next Steps

### Explore Advanced Features
- [Custom Transcoding Profiles](docs/transcoding.md)
- [Plugin Development](docs/plugins.md)
- [Performance Tuning](docs/performance.md)
- [API Integration](docs/api.md)

### Contribute to Development
- Report bugs and feature requests
- Submit pull requests
- Improve documentation
- Test on different hardware

### Join the Community
- Star the repository
- Share your setup and tips
- Help other users
- Provide feedback

Welcome to Tulip Media Server! We hope you enjoy using it and find it useful for your media streaming needs.

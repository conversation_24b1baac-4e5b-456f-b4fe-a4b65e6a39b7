# Rust build artifacts
target/
# Note: Cargo.lock is included for reproducible builds

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
docs/

# Test data (will be mounted separately)
test-media/
data/
data-*/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/

# Environment files
.env
.env.local

# Cache directories
.cache/
node_modules/

# Tulip Media Server - Development Configuration

[server]
bind_address = "0.0.0.0"
port = 8096
server_name = "Tulip Media Server (Development)"
enable_https = false

[database]
max_connections = 5
connection_timeout = 30

[media]
library_paths = [
    "/workspace/test-media/movies",
    "/workspace/test-media/tv",
    "/workspace/test-media/music"
]
scan_interval = 300  # 5 minutes for development
thumbnail_cache_size = 104857600   # 100MB
metadata_cache_size = 52428800    # 50MB

supported_video_formats = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "webm", "m4v"]
supported_audio_formats = ["mp3", "flac", "aac", "ogg", "wav", "m4a"]
supported_image_formats = ["jpg", "jpeg", "png", "webp", "bmp"]

[streaming]
enable_transcoding = true
max_concurrent_streams = 2  # Conservative for development
hardware_acceleration = false  # Disable for development
video_codecs = ["h264"]
audio_codecs = ["aac", "mp3"]
max_bitrate = 10000000  # 10 Mbps
segment_duration = 6

[security]
session_timeout = 86400  # 24 hours
max_login_attempts = 10  # Relaxed for development
enable_api_keys = true

[performance]
max_blocking_threads = 2
enable_compression = true
cache_size_mb = 128
arm64_optimizations = false  # Disable for x86_64 development

# Tulip Media Server - Installation Guide

## Overview

Tulip Media Server is a high-performance, Jellyfin-compatible media server optimized for ARM64 devices like the Nano Pi M4 V2. It provides efficient media streaming, transcoding, and management with minimal resource usage.

## System Requirements

### Hardware
- **CPU**: ARM64 (ARMv8) processor (recommended: Cortex-A72 or better)
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: 
  - System: 8GB+ for OS and application
  - Media: As needed for your media library
  - Cache: 1-5GB for transcoding and thumbnails
- **Network**: Gigabit Ethernet recommended for streaming

### Software
- **OS**: Linux (Ubuntu 20.04+, Debian 11+, Armbian)
- **Kernel**: 5.4+ (for hardware acceleration support)
- **Dependencies**: See [Dependencies](#dependencies) section

## Installation Methods

### Method 1: Docker (Recommended for Development)

#### Prerequisites
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Quick Start
```bash
# Clone the repository
git clone https://github.com/yourusername/tulip-media.git
cd tulip-media

# Create configuration
cp config-dev.toml config.toml
# Edit config.toml with your media paths

# Start the server
docker-compose up -d

# Check logs
docker-compose logs -f
```

#### Docker Configuration
The `docker-compose.yml` includes:
- Multi-architecture support (ARM64/AMD64)
- Volume mounts for media and data
- Network configuration
- Resource limits optimized for ARM64

### Method 2: Native Installation

#### Prerequisites
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install build dependencies
sudo apt install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    libsqlite3-dev \
    libclang-dev \
    clang \
    cmake \
    git \
    curl

# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Add ARM64 target
rustup target add aarch64-unknown-linux-gnu

# Install ARM64 toolchain (for cross-compilation)
sudo apt install -y gcc-aarch64-linux-gnu
```

#### Build and Install
```bash
# Clone repository
git clone https://github.com/yourusername/tulip-media.git
cd tulip-media

# Build in release mode
cargo build --release --target aarch64-unknown-linux-gnu

# Install binary
sudo cp target/aarch64-unknown-linux-gnu/release/tulip-media /usr/local/bin/
sudo chmod +x /usr/local/bin/tulip-media

# Create systemd service
sudo tee /etc/systemd/system/tulip-media.service > /dev/null <<EOF
[Unit]
Description=Tulip Media Server
After=network.target

[Service]
Type=simple
User=tulip
Group=tulip
WorkingDirectory=/opt/tulip-media
ExecStart=/usr/local/bin/tulip-media --config /etc/tulip-media/config.toml
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Create user and directories
sudo useradd -r -s /bin/false tulip
sudo mkdir -p /opt/tulip-media/{data,logs}
sudo mkdir -p /etc/tulip-media
sudo chown -R tulip:tulip /opt/tulip-media

# Copy configuration
sudo cp config.toml /etc/tulip-media/
sudo chown tulip:tulip /etc/tulip-media/config.toml

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable tulip-media
sudo systemctl start tulip-media
```

### Method 3: Cross-Compilation from x86_64

#### Setup Cross-Compilation Environment
```bash
# Install cross-compilation tools
sudo apt install -y \
    gcc-aarch64-linux-gnu \
    g++-aarch64-linux-gnu \
    qemu-user-static

# Configure Cargo for cross-compilation
mkdir -p ~/.cargo
cat >> ~/.cargo/config <<EOF
[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"
EOF

# Build for ARM64
cargo build --release --target aarch64-unknown-linux-gnu

# Transfer binary to target device
scp target/aarch64-unknown-linux-gnu/release/tulip-media user@nano-pi:/tmp/
```

## Configuration

### Basic Configuration
```toml
# config.toml
[server]
bind_address = "0.0.0.0"
port = 8096
server_name = "Tulip Media Server"

[database]
path = "./data/tulip.db"
max_connections = 10

[media]
library_paths = [
    "/media/movies",
    "/media/tv",
    "/media/music"
]
scan_interval = 3600

[streaming]
enable_transcoding = true
max_concurrent_streams = 4
hardware_acceleration = true

[performance]
worker_threads = 4
arm64_optimizations = true
cache_size_mb = 512
```

### Advanced Configuration
```toml
[streaming]
video_codecs = ["h264", "h265"]
audio_codecs = ["aac", "mp3"]
max_bitrate = 20_000_000
segment_duration = 6

[security]
jwt_secret = "your-secret-key-here"
session_timeout = 86400
max_login_attempts = 5

[discovery]
enable_ssdp = true
enable_mdns = true
```

## Hardware Acceleration Setup

### RK3399 VPU (Nano Pi M4 V2)
```bash
# Install media codecs
sudo apt install -y \
    libmali-rk-dev \
    librockchip-mpp-dev

# Configure FFmpeg for hardware acceleration
export FFMPEG_OPTS="--enable-rkmpp --enable-v4l2-request"
```

### Verify Hardware Support
```bash
# Check video devices
ls -la /dev/video*

# Test hardware acceleration
ffmpeg -hwaccel rkmpp -i input.mp4 -c:v h264_rkmpp -b:v 2M output.mp4
```

## Performance Tuning

### System Optimizations
```bash
# CPU governor for performance
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# I/O scheduler
echo bfq | sudo tee /sys/block/sda/queue/scheduler

# Network tuning
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### Application Tuning
```toml
[performance]
worker_threads = 4  # Match CPU cores
max_blocking_threads = 8
enable_compression = true
cache_size_mb = 1024  # Adjust based on available RAM
arm64_optimizations = true

[streaming]
max_concurrent_streams = 2  # Conservative for ARM64
hardware_acceleration = true
```

## Troubleshooting

### Common Issues

#### Build Errors
```bash
# Clear cargo cache
cargo clean
cargo update

# Check Rust version
rustc --version  # Should be 1.70+
```

#### Runtime Errors
```bash
# Check logs
sudo journalctl -u tulip-media -f

# Check permissions
sudo chown -R tulip:tulip /opt/tulip-media

# Verify configuration
tulip-media --config /etc/tulip-media/config.toml --check
```

#### Performance Issues
```bash
# Monitor resource usage
htop
iotop
nethogs

# Check transcoding
ffmpeg -f lavfi -i testsrc -t 10 -c:v h264_rkmpp test.mp4
```

### Log Analysis
```bash
# Enable debug logging
export RUST_LOG=tulip_media=debug,tower_http=debug

# Filter logs
grep "transcoding" /var/log/tulip-media.log
grep "session" /var/log/tulip-media.log
```

## Security Considerations

### Network Security
- Use firewall rules to restrict access
- Consider VPN for remote access
- Enable HTTPS with valid certificates

### Authentication
- Change default admin password
- Use strong JWT secrets
- Implement rate limiting

### File Permissions
- Restrict media directory access
- Use dedicated user account
- Regular security updates

## Monitoring and Maintenance

### Health Checks
```bash
# API health endpoint
curl http://localhost:8096/System/Info

# Service status
sudo systemctl status tulip-media

# Database integrity
sqlite3 /opt/tulip-media/data/tulip.db "PRAGMA integrity_check;"
```

### Backup
```bash
# Backup configuration
sudo cp -r /etc/tulip-media /backup/

# Backup database
sqlite3 /opt/tulip-media/data/tulip.db ".backup /backup/tulip.db"

# Backup media metadata
sudo cp -r /opt/tulip-media/data /backup/
```

### Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
cargo build --release
sudo systemctl restart tulip-media
```

## Support and Community

- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
- **Documentation**: [docs/](docs/) directory
- **Performance Tips**: [PERFORMANCE.md](PERFORMANCE.md)

## License

This project is licensed under the MIT License - see [LICENSE](LICENSE) file for details.

# Tulip Media Server - Development Makefile

.PHONY: help setup dev test arm64 debug clean logs build shell check fmt clippy

# Default target
help:
	@echo "🌷 Tulip Media Server - Development Commands"
	@echo ""
	@echo "Setup:"
	@echo "  setup     - Initialize development environment"
	@echo ""
	@echo "Development:"
	@echo "  dev       - Start development server with hot reload"
	@echo "  shell     - Open shell in development container"
	@echo "  logs      - View development server logs"
	@echo ""
	@echo "Testing:"
	@echo "  test      - Start production-like test environment"
	@echo "  arm64     - Test ARM64 build (requires Docker buildx)"
	@echo "  debug     - Start with database browser and file server"
	@echo ""
	@echo "Code Quality:"
	@echo "  check     - Run cargo check in container"
	@echo "  fmt       - Format code"
	@echo "  clippy    - Run clippy linter"
	@echo "  build     - Build release binary"
	@echo ""
	@echo "Cleanup:"
	@echo "  clean     - Clean up containers and volumes"
	@echo "  reset     - Reset everything (clean + remove data)"

# Setup development environment
setup:
	@echo "🌷 Setting up Tulip Media Server development environment..."
	@chmod +x scripts/setup-dev.sh
	@./scripts/setup-dev.sh

# Start development server with hot reload
dev:
	@echo "🚀 Starting development server..."
	@docker compose up --build tulip-dev

# Start production-like test environment
test:
	@echo "🧪 Starting test environment..."
	@docker compose --profile testing up --build tulip-test

# Test ARM64 build
arm64:
	@echo "🔧 Testing ARM64 build..."
	@docker buildx create --use --name tulip-builder 2>/dev/null || true
	@docker compose --profile arm64 up --build tulip-arm64

# Start debug environment with additional services
debug:
	@echo "🐛 Starting debug environment..."
	@docker compose --profile debug up --build tulip-dev sqlite-browser media-server

# Open shell in development container
shell:
	@echo "🐚 Opening shell in development container..."
	@docker compose run --rm tulip-dev bash

# View development logs
logs:
	@docker compose logs -f tulip-dev

# Run cargo check
check:
	@echo "✅ Running cargo check..."
	@docker compose run --rm tulip-dev cargo check

# Format code
fmt:
	@echo "🎨 Formatting code..."
	@docker compose run --rm tulip-dev cargo fmt

# Run clippy
clippy:
	@echo "📎 Running clippy..."
	@docker compose run --rm tulip-dev cargo clippy -- -D warnings

# Build release binary
build:
	@echo "🔨 Building release binary..."
	@docker compose run --rm tulip-dev cargo build --release

# Build ARM64 binary
build-arm64:
	@echo "🔨 Building ARM64 release binary..."
	@docker compose run --rm tulip-dev cargo build --release --target aarch64-unknown-linux-gnu --features ffmpeg

# Run tests
test-unit:
	@echo "🧪 Running unit tests..."
	@docker compose run --rm tulip-dev cargo test

# Clean up containers and volumes
clean:
	@echo "🧹 Cleaning up..."
	@docker compose down -v
	@docker system prune -f
	@docker volume prune -f

# Reset everything
reset: clean
	@echo "🔄 Resetting everything..."
	@rm -rf data data-test data-arm64 target
	@docker buildx rm tulip-builder 2>/dev/null || true

# Install development dependencies on host (optional)
install-host-deps:
	@echo "📦 Installing host dependencies..."
	@if command -v apt-get >/dev/null 2>&1; then \
		sudo apt-get update && sudo apt-get install -y \
			ffmpeg libavutil-dev libavcodec-dev libavformat-dev \
			libsqlite3-dev pkg-config libssl-dev build-essential; \
	elif command -v brew >/dev/null 2>&1; then \
		brew install ffmpeg sqlite pkg-config openssl; \
	else \
		echo "❌ Unsupported package manager. Please install dependencies manually."; \
	fi

# Generate API documentation
docs:
	@echo "📚 Generating documentation..."
	@docker compose run --rm tulip-dev cargo doc --open

# Performance profiling (requires perf tools)
profile:
	@echo "📊 Running performance profile..."
	@docker compose run --rm --cap-add SYS_ADMIN tulip-dev \
		cargo build --release && \
		perf record --call-graph=dwarf ./target/release/tulip-media --help

# Security audit
audit:
	@echo "🔒 Running security audit..."
	@docker compose run --rm tulip-dev cargo audit

# Update dependencies
update:
	@echo "⬆️ Updating dependencies..."
	@docker compose run --rm tulip-dev cargo update

# Show container status
status:
	@echo "📊 Container status:"
	@docker compose ps

# Show resource usage
stats:
	@echo "📈 Resource usage:"
	@docker stats --no-stream

# Backup data
backup:
	@echo "💾 Creating backup..."
	@tar -czf tulip-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz data/ test-media/ config*.toml

# Quick development cycle
quick: fmt clippy check
	@echo "✨ Quick development cycle complete!"

[package]
name = "tulip-media"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web framework and async runtime
tokio = { version = "1.35", features = ["full"] }
tokio-util = { version = "0.7", features = ["io"] }
axum = { version = "0.8.4", features = ["macros", "multipart", "ws"] }
tower = { version = "0.5.2", features = ["full"] }
tower-http = { version = "0.6.6", features = ["fs", "cors", "trace", "compression-gzip"] }
hyper = { version = "1.0", features = ["full"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_urlencoded = "0.7"

# Database
sqlx = { version = "0.8.6", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# Media processing (optional for initial build)
ffmpeg-next = { version = "7.1.0", optional = true }
image = { version = "0.25.6", features = ["jpeg", "png", "webp"] }

# Cryptography and security
sha2 = "0.10"
hex = "0.4"
uuid = { version = "1.6", features = ["v4", "serde"] }
jsonwebtoken = "9.2"
bcrypt = "0.17.1"

# File system and utilities
notify = "8.2.0"
walkdir = "2.4"
mime_guess = "2.0"
regex = "1.10"
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "2.0.16"

# Network and discovery
socket2 = "0.6.0"
local-ip-address = "0.6.5"

# Configuration
config = "0.15.14"
toml = "0.9.5"
clap = { version = "4.4", features = ["derive"] }

# Performance optimizations for ARM64
rayon = "1.8"  # Parallel processing
parking_lot = "0.12"  # Fast mutexes
dashmap = "6.1.0"  # Concurrent hashmap
num_cpus = "1.16"  # CPU detection
libc = "0.2"
prometheus = "0.14"
once_cell = "1.21"

[profile.release]
# Optimize for ARM64 performance
lto = true
codegen-units = 1
panic = "abort"
strip = true
opt-level = 3

[profile.release.package."*"]
opt-level = 3

[target.'cfg(target_arch = "aarch64")'.dependencies]
# ARM64 specific optimizations
neon-sys = "0.10.1"  # ARM NEON SIMD support

[dev-dependencies]
tempfile = "3.8"

[features]
default = []
ffmpeg = ["ffmpeg-next"]  # Enable FFmpeg support
hardware-accel = ["ffmpeg"]  # Enable hardware acceleration features
debug-logging = []   # Enable detailed debug logging

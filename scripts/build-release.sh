#!/bin/bash

# Tulip Media Server - Release Build Script
# Optimized for ARM64 (Nano Pi M4 V2)

set -e

echo "🌷 Building Tulip Media Server for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TARGET_ARCH="aarch64-unknown-linux-gnu"
BUILD_TYPE="release"
FEATURES="ffmpeg,hardware-accel"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on the target architecture
if [[ $(uname -m) == "aarch64" ]]; then
    log_info "Building natively on ARM64"
    TARGET_ARCH=""
    CROSS_COMPILE=""
else
    log_info "Cross-compiling for ARM64"
    CROSS_COMPILE="--target $TARGET_ARCH"
fi

# Check dependencies
log_info "Checking build dependencies..."

if ! command -v cargo &> /dev/null; then
    log_error "Rust/Cargo not found. Please install Rust."
    exit 1
fi

if ! command -v ffmpeg &> /dev/null; then
    log_warning "FFmpeg not found. Some features may not work."
fi

# Add ARM64 target if cross-compiling
if [[ -n "$TARGET_ARCH" ]]; then
    log_info "Adding ARM64 target..."
    rustup target add $TARGET_ARCH
fi

# Set environment variables for cross-compilation
if [[ -n "$TARGET_ARCH" ]]; then
    export PKG_CONFIG_ALLOW_CROSS=1
    export PKG_CONFIG_ALLOW_SYSTEM_LIBS=1
    export PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1
    
    # Set up cross-compilation toolchain
    export CC_aarch64_unknown_linux_gnu=aarch64-linux-gnu-gcc
    export CXX_aarch64_unknown_linux_gnu=aarch64-linux-gnu-g++
    export AR_aarch64_unknown_linux_gnu=aarch64-linux-gnu-ar
    export CARGO_TARGET_AARCH64_UNKNOWN_LINUX_GNU_LINKER=aarch64-linux-gnu-gcc
fi

# Clean previous builds
log_info "Cleaning previous builds..."
cargo clean

# Build the project
log_info "Building Tulip Media Server..."
log_info "Features: $FEATURES"
log_info "Target: ${TARGET_ARCH:-native}"

if cargo build --release $CROSS_COMPILE --features "$FEATURES"; then
    log_success "Build completed successfully!"
else
    log_error "Build failed!"
    exit 1
fi

# Determine binary path
if [[ -n "$TARGET_ARCH" ]]; then
    BINARY_PATH="target/$TARGET_ARCH/release/tulip-media"
else
    BINARY_PATH="target/release/tulip-media"
fi

# Verify binary exists
if [[ ! -f "$BINARY_PATH" ]]; then
    log_error "Binary not found at $BINARY_PATH"
    exit 1
fi

# Get binary info
BINARY_SIZE=$(du -h "$BINARY_PATH" | cut -f1)
log_success "Binary created: $BINARY_PATH ($BINARY_SIZE)"

# Strip binary for smaller size
log_info "Stripping binary for smaller size..."
if [[ -n "$TARGET_ARCH" ]]; then
    aarch64-linux-gnu-strip "$BINARY_PATH" 2>/dev/null || log_warning "Could not strip binary"
else
    strip "$BINARY_PATH" 2>/dev/null || log_warning "Could not strip binary"
fi

STRIPPED_SIZE=$(du -h "$BINARY_PATH" | cut -f1)
log_success "Binary stripped: $STRIPPED_SIZE"

# Run tests if building natively
if [[ -z "$TARGET_ARCH" ]]; then
    log_info "Running tests..."
    if cargo test --features "$FEATURES"; then
        log_success "All tests passed!"
    else
        log_warning "Some tests failed, but build is complete"
    fi
fi

# Create deployment package
log_info "Creating deployment package..."
PACKAGE_DIR="tulip-media-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$PACKAGE_DIR"

# Copy binary
cp "$BINARY_PATH" "$PACKAGE_DIR/"

# Copy configuration files
cp config.toml "$PACKAGE_DIR/config.toml.example"
cp README.md "$PACKAGE_DIR/"
cp LICENSE "$PACKAGE_DIR/" 2>/dev/null || echo "# MIT License" > "$PACKAGE_DIR/LICENSE"

# Create installation script
cat > "$PACKAGE_DIR/install.sh" << 'EOF'
#!/bin/bash
# Tulip Media Server Installation Script

set -e

echo "Installing Tulip Media Server..."

# Create user and directories
sudo useradd -r -s /bin/false -m -d /opt/tulip-media tulip-media 2>/dev/null || true
sudo mkdir -p /opt/tulip-media/{bin,config,data,logs}

# Copy binary
sudo cp tulip-media /opt/tulip-media/bin/
sudo chmod +x /opt/tulip-media/bin/tulip-media

# Copy configuration
sudo cp config.toml.example /opt/tulip-media/config/config.toml

# Set permissions
sudo chown -R tulip-media:tulip-media /opt/tulip-media

# Create systemd service
sudo tee /etc/systemd/system/tulip-media.service > /dev/null << 'SYSTEMD_EOF'
[Unit]
Description=Tulip Media Server
After=network.target

[Service]
Type=simple
User=tulip-media
Group=tulip-media
WorkingDirectory=/opt/tulip-media
ExecStart=/opt/tulip-media/bin/tulip-media --config /opt/tulip-media/config/config.toml --data-dir /opt/tulip-media/data
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
SYSTEMD_EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable tulip-media
sudo systemctl start tulip-media

echo "Tulip Media Server installed successfully!"
echo "Service status: $(sudo systemctl is-active tulip-media)"
echo "Access the server at: http://$(hostname -I | awk '{print $1}'):8096"
echo ""
echo "To configure the server:"
echo "  sudo nano /opt/tulip-media/config/config.toml"
echo "  sudo systemctl restart tulip-media"
echo ""
echo "To view logs:"
echo "  sudo journalctl -u tulip-media -f"
EOF

chmod +x "$PACKAGE_DIR/install.sh"

# Create archive
ARCHIVE_NAME="$PACKAGE_DIR.tar.gz"
tar -czf "$ARCHIVE_NAME" "$PACKAGE_DIR"
rm -rf "$PACKAGE_DIR"

log_success "Deployment package created: $ARCHIVE_NAME"

# Final summary
echo ""
echo "🎉 Build Summary:"
echo "  Binary: $BINARY_PATH ($STRIPPED_SIZE)"
echo "  Package: $ARCHIVE_NAME"
echo "  Features: $FEATURES"
echo "  Target: ${TARGET_ARCH:-native}"
echo ""
echo "To deploy on Nano Pi M4 V2:"
echo "  1. Copy $ARCHIVE_NAME to your device"
echo "  2. Extract: tar -xzf $ARCHIVE_NAME"
echo "  3. Run: cd $PACKAGE_DIR && sudo ./install.sh"
echo ""
echo "🌷 Tulip Media Server build complete!"
EOF

use anyhow::Result;
use chrono::Utc;
use notify::{Config, Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    sync::Arc,
    time::Duration,
};
use tokio::{
    sync::{mpsc, RwLock},
    time::sleep,
};
use tracing::{debug, error, info, warn};
use walkdir::WalkDir;

use crate::{
    config::Config as AppConfig,
    database::{models::*, Database},
    utils::{is_audio_file, is_image_file, is_video_file},
};

pub struct MediaScanner {
    config: Arc<AppConfig>,
    database: Database,
    watchers: Arc<RwLock<HashMap<PathBuf, RecommendedWatcher>>>,
    scan_queue: Arc<RwLock<Vec<PathBuf>>>,
}

#[derive(Debug, Clone)]
pub struct ScanResult {
    pub total_files: usize,
    pub new_files: usize,
    pub updated_files: usize,
    pub errors: usize,
    pub duration: Duration,
}

impl MediaScanner {
    pub fn new(config: Arc<AppConfig>, database: Database) -> Self {
        Self {
            config,
            database,
            watchers: Arc::new(RwLock::new(HashMap::new())),
            scan_queue: Arc::new(RwLock::new(Vec::new())),
        }
    }

    pub async fn start_scanning(&self) -> Result<()> {
        info!("Starting media scanner...");

        // Initialize libraries in database
        self.initialize_libraries().await?;

        // Start file watchers for each library path
        self.start_file_watchers().await?;

        // Perform initial scan
        self.perform_full_scan().await?;

        // Start periodic scanning
        self.start_periodic_scan().await;

        Ok(())
    }

    async fn initialize_libraries(&self) -> Result<()> {
        for (index, path) in self.config.media.library_paths.iter().enumerate() {
            let library_type = self.detect_library_type(path);
            let library_name = format!("Library {}", index + 1);

            // Check if library already exists
            let existing =
                sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM libraries WHERE path = ?")
                    .bind(path.to_string_lossy().as_ref())
                    .fetch_one(self.database.pool())
                    .await?;

            if existing == 0 {
                let library = Library::new(
                    library_name,
                    path.to_string_lossy().to_string(),
                    library_type,
                );

                sqlx::query(
                    r#"
                    INSERT INTO libraries (id, name, path, library_type, created_at, updated_at, last_scan)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    "#,
                )
                .bind(&library.id)
                .bind(&library.name)
                .bind(&library.path)
                .bind(&library.library_type)
                .bind(library.created_at.to_rfc3339())
                .bind(library.updated_at.to_rfc3339())
                .bind(library.last_scan.map(|d| d.to_rfc3339()))
                .execute(self.database.pool())
                .await?;

                info!("Created library: {} at {}", library.name, library.path);
            }
        }

        Ok(())
    }

    fn detect_library_type(&self, path: &Path) -> String {
        let path_str = path.to_string_lossy().to_lowercase();

        if path_str.contains("movie") {
            "Movies".to_string()
        } else if path_str.contains("tv") || path_str.contains("show") {
            "TV Shows".to_string()
        } else if path_str.contains("music") || path_str.contains("audio") {
            "Music".to_string()
        } else {
            "Mixed".to_string()
        }
    }

    async fn start_file_watchers(&self) -> Result<()> {
        let (tx, mut rx) = mpsc::channel(1000);

        for path in &self.config.media.library_paths {
            if !path.exists() {
                warn!("Library path does not exist: {:?}", path);
                continue;
            }

            let mut watcher = RecommendedWatcher::new(
                {
                    let tx = tx.clone();
                    move |res: Result<Event, notify::Error>| match res {
                        Ok(event) => {
                            if let Err(e) = tx.blocking_send(event) {
                                error!("Failed to send file event: {}", e);
                            }
                        }
                        Err(e) => error!("File watcher error: {}", e),
                    }
                },
                Config::default(),
            )?;

            watcher.watch(path, RecursiveMode::Recursive)?;

            let mut watchers = self.watchers.write().await;
            watchers.insert(path.clone(), watcher);

            info!("Started file watcher for: {:?}", path);
        }

        // Handle file events
        let scan_queue = self.scan_queue.clone();
        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                match event.kind {
                    EventKind::Create(_) | EventKind::Modify(_) => {
                        for path in event.paths {
                            if is_media_file(&path) {
                                debug!("Queuing file for scan: {:?}", path);
                                let mut queue = scan_queue.write().await;
                                if !queue.contains(&path) {
                                    queue.push(path);
                                }
                            }
                        }
                    }
                    EventKind::Remove(_) => {
                        for path in event.paths {
                            debug!("File removed: {:?}", path);
                            // TODO: Remove from database
                        }
                    }
                    _ => {}
                }
            }
        });

        Ok(())
    }

    async fn perform_full_scan(&self) -> Result<ScanResult> {
        let start_time = std::time::Instant::now();
        let mut total_files = 0;
        let mut new_files = 0;
        let mut updated_files = 0;
        let mut errors = 0;

        info!("Starting full media library scan...");

        for path in &self.config.media.library_paths {
            if !path.exists() {
                warn!("Skipping non-existent path: {:?}", path);
                continue;
            }

            info!("Scanning library: {:?}", path);

            for entry in WalkDir::new(path)
                .follow_links(false)
                .into_iter()
                .filter_map(|e| e.ok())
            {
                let file_path = entry.path();

                if !is_media_file(file_path) {
                    continue;
                }

                total_files += 1;

                match self.scan_file(file_path).await {
                    Ok(is_new) => {
                        if is_new {
                            new_files += 1;
                        } else {
                            updated_files += 1;
                        }
                    }
                    Err(e) => {
                        error!("Failed to scan file {:?}: {}", file_path, e);
                        errors += 1;
                    }
                }

                // Yield control periodically
                if total_files % 100 == 0 {
                    tokio::task::yield_now().await;
                }
            }
        }

        // Update library scan timestamps
        self.update_library_scan_timestamps().await?;

        let duration = start_time.elapsed();
        let result = ScanResult {
            total_files,
            new_files,
            updated_files,
            errors,
            duration,
        };

        info!(
            "Scan complete: {} total, {} new, {} updated, {} errors in {:?}",
            result.total_files,
            result.new_files,
            result.updated_files,
            result.errors,
            result.duration
        );

        Ok(result)
    }

    async fn scan_file(&self, path: &Path) -> Result<bool> {
        let path_str = path.to_string_lossy();

        // Check if file already exists in database
        let existing_item =
            sqlx::query_as::<_, MediaItem>("SELECT * FROM media_items WHERE path = ?")
                .bind(&*path_str)
                .fetch_optional(self.database.pool())
                .await?;

        // Get file metadata
        let metadata = std::fs::metadata(path)?;
        let file_size = metadata.len() as i64;
        let modified = metadata.modified()?;
        let modified_dt = chrono::DateTime::<Utc>::from(modified);

        // Find appropriate library
        let library_id = self.find_library_for_path(path).await?;

        let is_new = if let Some(mut item) = existing_item {
            // Check if file has been modified
            if item.date_modified < modified_dt {
                // Update existing item
                item.file_size = file_size;
                item.date_modified = modified_dt;
                item.updated_at = Utc::now();

                self.update_media_item(&item).await?;
                false
            } else {
                false // No changes needed
            }
        } else {
            // Create new media item
            let item_type = self.determine_item_type(path);
            let media_type = self.determine_media_type(path);
            let name = self.extract_name_from_path(path);

            let item = MediaItem::new(
                library_id,
                None, // parent_id will be set later for episodes
                name,
                path_str.to_string(),
                item_type,
                media_type,
                file_size,
            );

            self.insert_media_item(&item).await?;
            true
        };

        Ok(is_new)
    }

    async fn find_library_for_path(&self, path: &Path) -> Result<String> {
        let path_str = path.to_string_lossy();

        let library = sqlx::query_as::<_, Library>(
            "SELECT * FROM libraries WHERE ? LIKE path || '%' ORDER BY LENGTH(path) DESC LIMIT 1",
        )
        .bind(&*path_str)
        .fetch_optional(self.database.pool())
        .await?;

        match library {
            Some(lib) => Ok(lib.id),
            None => {
                // Fallback: create a default library
                let default_lib =
                    Library::new("Default".to_string(), "/".to_string(), "Mixed".to_string());

                sqlx::query(
                    r#"
                    INSERT INTO libraries (id, name, path, library_type, created_at, updated_at, last_scan)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    "#,
                )
                .bind(&default_lib.id)
                .bind(&default_lib.name)
                .bind(&default_lib.path)
                .bind(&default_lib.library_type)
                .bind(default_lib.created_at.to_rfc3339())
                .bind(default_lib.updated_at.to_rfc3339())
                .bind(default_lib.last_scan.map(|d| d.to_rfc3339()))
                .execute(self.database.pool())
                .await?;

                Ok(default_lib.id)
            }
        }
    }

    fn determine_item_type(&self, path: &Path) -> String {
        if is_video_file(path) {
            if self.is_tv_episode(path) {
                "Episode".to_string()
            } else {
                "Movie".to_string()
            }
        } else if is_audio_file(path) {
            "Audio".to_string()
        } else if is_image_file(path) {
            "Photo".to_string()
        } else {
            "Unknown".to_string()
        }
    }

    fn determine_media_type(&self, path: &Path) -> String {
        if is_video_file(path) {
            "Video".to_string()
        } else if is_audio_file(path) {
            "Audio".to_string()
        } else if is_image_file(path) {
            "Image".to_string()
        } else {
            "Unknown".to_string()
        }
    }

    fn is_tv_episode(&self, path: &Path) -> bool {
        let path_str = path.to_string_lossy().to_lowercase();

        // Look for TV show patterns
        path_str.contains("season")
            || path_str.contains("episode")
            || path_str.contains("s0")
            || path_str.contains("e0")
            || regex::Regex::new(r"s\d+e\d+").unwrap().is_match(&path_str)
    }

    fn extract_name_from_path(&self, path: &Path) -> String {
        path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("Unknown")
            .to_string()
    }

    async fn insert_media_item(&self, item: &MediaItem) -> Result<()> {
        sqlx::query(
            r#"
            INSERT INTO media_items (
                id, library_id, parent_id, name, sort_name, path, item_type, media_type,
                file_size, duration, bitrate, container, video_codec, audio_codec,
                width, height, aspect_ratio, framerate, created_at, updated_at,
                date_added, date_modified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&item.id)
        .bind(&item.library_id)
        .bind(&item.parent_id)
        .bind(&item.name)
        .bind(&item.sort_name)
        .bind(&item.path)
        .bind(&item.item_type)
        .bind(&item.media_type)
        .bind(item.file_size)
        .bind(item.duration)
        .bind(item.bitrate)
        .bind(&item.container)
        .bind(&item.video_codec)
        .bind(&item.audio_codec)
        .bind(item.width)
        .bind(item.height)
        .bind(&item.aspect_ratio)
        .bind(item.framerate)
        .bind(item.created_at.to_rfc3339())
        .bind(item.updated_at.to_rfc3339())
        .bind(item.date_added.to_rfc3339())
        .bind(item.date_modified.to_rfc3339())
        .execute(self.database.pool())
        .await?;

        Ok(())
    }

    async fn update_media_item(&self, item: &MediaItem) -> Result<()> {
        sqlx::query(
            r#"
            UPDATE media_items SET
                file_size = ?, updated_at = ?, date_modified = ?
            WHERE id = ?
            "#,
        )
        .bind(item.file_size)
        .bind(item.updated_at.to_rfc3339())
        .bind(item.date_modified.to_rfc3339())
        .bind(&item.id)
        .execute(self.database.pool())
        .await?;

        Ok(())
    }

    async fn update_library_scan_timestamps(&self) -> Result<()> {
        let now = Utc::now();

        sqlx::query("UPDATE libraries SET last_scan = ?")
            .bind(now.to_rfc3339())
            .execute(self.database.pool())
            .await?;

        Ok(())
    }

    async fn start_periodic_scan(&self) {
        let scanner = self.clone();
        let interval = Duration::from_secs(self.config.media.scan_interval);

        tokio::spawn(async move {
            loop {
                sleep(interval).await;

                info!("Starting periodic scan...");
                if let Err(e) = scanner.perform_full_scan().await {
                    error!("Periodic scan failed: {}", e);
                }
            }
        });
    }

    #[allow(dead_code)]
    async fn process_scan_queue(&self) -> Result<()> {
        let mut queue = self.scan_queue.write().await;
        let files_to_scan: Vec<PathBuf> = queue.drain(..).collect();
        drop(queue);

        for path in files_to_scan {
            if let Err(e) = self.scan_file(&path).await {
                error!("Failed to scan queued file {:?}: {}", path, e);
            }
        }

        Ok(())
    }
}

impl Clone for MediaScanner {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            database: self.database.clone(),
            watchers: self.watchers.clone(),
            scan_queue: self.scan_queue.clone(),
        }
    }
}

fn is_media_file(path: &Path) -> bool {
    is_video_file(path) || is_audio_file(path) || is_image_file(path)
}

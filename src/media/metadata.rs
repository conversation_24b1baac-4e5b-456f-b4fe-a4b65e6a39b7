use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::process::Command;
use tracing::debug;

use crate::database::models::{MediaItem, MediaMetadata};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MediaInfo {
    pub duration: Option<f64>,
    pub bitrate: Option<i64>,
    pub container: Option<String>,
    pub video_streams: Vec<VideoStream>,
    pub audio_streams: Vec<AudioStream>,
    pub subtitle_streams: Vec<SubtitleStream>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoStream {
    pub index: i32,
    pub codec: String,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub aspect_ratio: Option<String>,
    pub framerate: Option<f64>,
    pub bitrate: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioStream {
    pub index: i32,
    pub codec: String,
    pub channels: Option<i32>,
    pub sample_rate: Option<i32>,
    pub bitrate: Option<i64>,
    pub language: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SubtitleStream {
    pub index: i32,
    pub codec: String,
    pub language: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FFProbeOutput {
    format: Option<FFProbeFormat>,
    streams: Option<Vec<FFProbeStream>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FFProbeFormat {
    duration: Option<String>,
    bit_rate: Option<String>,
    format_name: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FFProbeStream {
    index: i32,
    codec_type: String,
    codec_name: String,
    width: Option<i32>,
    height: Option<i32>,
    r_frame_rate: Option<String>,
    bit_rate: Option<String>,
    channels: Option<i32>,
    sample_rate: Option<String>,
    tags: Option<FFProbeTags>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
struct FFProbeTags {
    language: Option<String>,
}

pub struct MetadataExtractor;

impl MetadataExtractor {
    pub fn new() -> Self {
        Self
    }

    pub async fn extract_media_info(&self, path: &Path) -> Result<MediaInfo> {
        debug!("Extracting media info for: {:?}", path);

        let output = Command::new("ffprobe")
            .args([
                "-v",
                "quiet",
                "-print_format",
                "json",
                "-show_format",
                "-show_streams",
                path.to_str().unwrap_or(""),
            ])
            .output()?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow::anyhow!("FFprobe failed: {}", error));
        }

        let probe_output: FFProbeOutput = serde_json::from_slice(&output.stdout)?;

        let mut media_info = MediaInfo {
            duration: None,
            bitrate: None,
            container: None,
            video_streams: Vec::new(),
            audio_streams: Vec::new(),
            subtitle_streams: Vec::new(),
        };

        // Extract format information
        if let Some(format) = probe_output.format {
            if let Some(duration_str) = format.duration {
                media_info.duration = duration_str.parse::<f64>().ok();
            }

            if let Some(bitrate_str) = format.bit_rate {
                media_info.bitrate = bitrate_str.parse::<i64>().ok();
            }

            media_info.container = format.format_name;
        }

        // Extract stream information
        if let Some(streams) = probe_output.streams {
            for stream in streams {
                match stream.codec_type.as_str() {
                    "video" => {
                        let framerate = stream
                            .r_frame_rate
                            .as_ref()
                            .and_then(|r| self.parse_framerate(r));

                        let aspect_ratio = if let (Some(w), Some(h)) = (stream.width, stream.height)
                        {
                            Some(format!("{}:{}", w, h))
                        } else {
                            None
                        };

                        media_info.video_streams.push(VideoStream {
                            index: stream.index,
                            codec: stream.codec_name,
                            width: stream.width,
                            height: stream.height,
                            aspect_ratio,
                            framerate,
                            bitrate: stream.bit_rate.and_then(|b| b.parse().ok()),
                        });
                    }
                    "audio" => {
                        let language = stream.tags.as_ref().and_then(|t| t.language.clone());

                        media_info.audio_streams.push(AudioStream {
                            index: stream.index,
                            codec: stream.codec_name,
                            channels: stream.channels,
                            sample_rate: stream.sample_rate.and_then(|s| s.parse().ok()),
                            bitrate: stream.bit_rate.and_then(|b| b.parse().ok()),
                            language,
                        });
                    }
                    "subtitle" => {
                        let language = stream.tags.as_ref().and_then(|t| t.language.clone());

                        media_info.subtitle_streams.push(SubtitleStream {
                            index: stream.index,
                            codec: stream.codec_name,
                            language,
                        });
                    }
                    _ => {}
                }
            }
        }

        Ok(media_info)
    }

    pub async fn update_media_item_with_info(&self, item: &mut MediaItem, info: &MediaInfo) {
        // Update duration (convert from seconds to milliseconds)
        if let Some(duration_secs) = info.duration {
            item.duration = Some((duration_secs * 1000.0) as i64);
        }

        // Update bitrate
        item.bitrate = info.bitrate;

        // Update container format
        item.container = info.container.clone();

        // Update video information from first video stream
        if let Some(video_stream) = info.video_streams.first() {
            item.video_codec = Some(video_stream.codec.clone());
            item.width = video_stream.width;
            item.height = video_stream.height;
            item.aspect_ratio = video_stream.aspect_ratio.clone();
            item.framerate = video_stream.framerate;
        }

        // Update audio information from first audio stream
        if let Some(audio_stream) = info.audio_streams.first() {
            item.audio_codec = Some(audio_stream.codec.clone());
        }
    }

    pub async fn extract_metadata_from_filename(&self, path: &Path) -> MediaMetadata {
        let filename = path
            .file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("Unknown");

        let mut metadata = MediaMetadata {
            item_id: String::new(), // Will be set by caller
            title: Some(filename.to_string()),
            original_title: None,
            overview: None,
            tagline: None,
            release_date: None,
            runtime: None,
            rating: None,
            vote_average: None,
            vote_count: None,
            imdb_id: None,
            tmdb_id: None,
            tvdb_id: None,
            genres: None,
            studios: None,
            tags: None,
            people: None,
        };

        // Try to extract year from filename
        if let Some(year) = self.extract_year_from_filename(filename) {
            metadata.release_date = Some(
                chrono::NaiveDate::from_ymd_opt(year, 1, 1)
                    .unwrap()
                    .and_hms_opt(0, 0, 0)
                    .unwrap()
                    .and_utc(),
            );
        }

        // Clean up title by removing year and common suffixes
        let clean_title = self.clean_title(filename);
        metadata.title = Some(clean_title);

        metadata
    }

    fn parse_framerate(&self, framerate_str: &str) -> Option<f64> {
        if let Some((num_str, den_str)) = framerate_str.split_once('/') {
            if let (Ok(num), Ok(den)) = (num_str.parse::<f64>(), den_str.parse::<f64>()) {
                if den != 0.0 {
                    return Some(num / den);
                }
            }
        }

        framerate_str.parse().ok()
    }

    fn extract_year_from_filename(&self, filename: &str) -> Option<i32> {
        // Look for 4-digit year in parentheses or brackets
        let year_regex = regex::Regex::new(r"[\(\[](\d{4})[\)\]]").ok()?;

        if let Some(captures) = year_regex.captures(filename) {
            if let Some(year_match) = captures.get(1) {
                return year_match.as_str().parse().ok();
            }
        }

        // Look for standalone 4-digit year
        let standalone_year_regex = regex::Regex::new(r"\b(19|20)\d{2}\b").ok()?;

        if let Some(year_match) = standalone_year_regex.find(filename) {
            return year_match.as_str().parse().ok();
        }

        None
    }

    fn clean_title(&self, filename: &str) -> String {
        let mut title = filename.to_string();

        // Remove year in parentheses or brackets
        let year_regex = regex::Regex::new(r"[\(\[](\d{4})[\)\]]").unwrap();
        title = year_regex.replace_all(&title, "").to_string();

        // Remove common video quality indicators
        let quality_regex = regex::Regex::new(
            r"(?i)\b(720p|1080p|4k|2160p|bluray|dvdrip|webrip|hdtv|x264|x265|h264|h265)\b",
        )
        .unwrap();
        title = quality_regex.replace_all(&title, "").to_string();

        // Remove dots, underscores, and extra spaces
        title = title.replace('.', " ");
        title = title.replace('_', " ");

        // Normalize whitespace
        let whitespace_regex = regex::Regex::new(r"\s+").unwrap();
        title = whitespace_regex.replace_all(&title, " ").to_string();

        title.trim().to_string()
    }

    pub async fn extract_tv_show_info(&self, path: &Path) -> Option<TvShowInfo> {
        let path_str = path.to_string_lossy();

        // Try to extract season and episode numbers
        let episode_regex = regex::Regex::new(r"(?i)s(\d+)e(\d+)").ok()?;

        if let Some(captures) = episode_regex.captures(&path_str) {
            let season = captures.get(1)?.as_str().parse().ok()?;
            let episode = captures.get(2)?.as_str().parse().ok()?;

            // Extract show name from path
            let show_name = self.extract_show_name_from_path(path)?;

            return Some(TvShowInfo {
                show_name,
                season_number: season,
                episode_number: episode,
            });
        }

        None
    }

    fn extract_show_name_from_path(&self, path: &Path) -> Option<String> {
        // Look for show name in parent directories
        let mut current = path.parent()?;

        while let Some(parent) = current.parent() {
            let dir_name = current.file_name()?.to_str()?;

            // If this directory contains "Season", the parent is likely the show name
            if dir_name.to_lowercase().contains("season") {
                return parent.file_name()?.to_str().map(|s| s.to_string());
            }

            current = parent;
        }

        None
    }
}

#[derive(Debug, Clone)]
pub struct TvShowInfo {
    pub show_name: String,
    pub season_number: i32,
    pub episode_number: i32,
}

impl Default for MetadataExtractor {
    fn default() -> Self {
        Self::new()
    }
}

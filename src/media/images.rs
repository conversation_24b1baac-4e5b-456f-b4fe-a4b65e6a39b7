use anyhow::Result;
use image::{imageops::FilterType, DynamicImage, GenericImageView, ImageFormat};
use std::{
    io::Cursor,
    path::{Path, PathBuf},
    sync::Arc,
};
use tokio::fs;
use tracing::{debug, info, warn};
use uuid::Uuid;

use crate::{
    config::Config,
    database::{models::*, Database},
    utils::is_image_file,
};

pub struct ImageProcessor {
    config: Arc<Config>,
    database: Database,
    cache_dir: PathBuf,
}

#[derive(Debug, Clone)]
pub struct ThumbnailRequest {
    pub item_id: String,
    pub image_type: ImageType,
    pub width: Option<u32>,
    pub height: Option<u32>,
    pub quality: Option<u8>,
    pub format: Option<ImageFormat>,
}

#[derive(Debug, Clone)]
pub enum ImageType {
    Primary,
    Backdrop,
    Logo,
    Thumb,
    Banner,
    Art,
    Disc,
    Screenshot,
}

impl ImageType {
    pub fn as_str(&self) -> &'static str {
        match self {
            ImageType::Primary => "Primary",
            ImageType::Backdrop => "Backdrop",
            ImageType::Logo => "Logo",
            ImageType::Thumb => "Thumb",
            ImageType::Banner => "Banner",
            ImageType::Art => "Art",
            ImageType::Disc => "Disc",
            ImageType::Screenshot => "Screenshot",
        }
    }

    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "primary" => Some(ImageType::Primary),
            "backdrop" => Some(ImageType::Backdrop),
            "logo" => Some(ImageType::Logo),
            "thumb" => Some(ImageType::Thumb),
            "banner" => Some(ImageType::Banner),
            "art" => Some(ImageType::Art),
            "disc" => Some(ImageType::Disc),
            "screenshot" => Some(ImageType::Screenshot),
            _ => None,
        }
    }
}

impl ImageProcessor {
    pub async fn new(config: Arc<Config>, database: Database) -> Result<Self> {
        let cache_dir = PathBuf::from("./data/cache/images");
        fs::create_dir_all(&cache_dir).await?;

        Ok(Self {
            config,
            database,
            cache_dir,
        })
    }

    pub async fn generate_thumbnail(&self, request: ThumbnailRequest) -> Result<Vec<u8>> {
        debug!("Generating thumbnail for item: {}", request.item_id);

        // Check cache first
        if let Ok(cached_data) = self.get_cached_thumbnail(&request).await {
            debug!("Returning cached thumbnail for item: {}", request.item_id);
            return Ok(cached_data);
        }

        // Get media item
        let media_item = self.get_media_item(&request.item_id).await?;

        // Find appropriate image source
        let image_path = self
            .find_image_source(&media_item, &request.image_type)
            .await?;

        // Generate thumbnail
        let thumbnail_data = self.create_thumbnail(&image_path, &request).await?;

        // Cache the result
        self.cache_thumbnail(&request, &thumbnail_data).await?;

        Ok(thumbnail_data)
    }

    async fn get_media_item(&self, item_id: &str) -> Result<MediaItem> {
        let item = sqlx::query_as::<_, MediaItem>("SELECT * FROM media_items WHERE id = ?")
            .bind(item_id)
            .fetch_optional(self.database.pool())
            .await?;

        item.ok_or_else(|| anyhow::anyhow!("Media item not found"))
    }

    async fn find_image_source(
        &self,
        media_item: &MediaItem,
        image_type: &ImageType,
    ) -> Result<PathBuf> {
        // First, check if we have a specific image in the database
        if let Ok(Some(media_image)) = self.get_media_image(&media_item.id, image_type).await {
            return Ok(PathBuf::from(media_image.path));
        }

        // For video files, try to extract frame
        if media_item.media_type == "Video" {
            return self.extract_video_frame(&media_item.path).await;
        }

        // For image files, use the file itself
        if media_item.media_type == "Image" {
            return Ok(PathBuf::from(&media_item.path));
        }

        // Look for common image files in the same directory
        self.find_nearby_image(&media_item.path, image_type).await
    }

    async fn get_media_image(
        &self,
        item_id: &str,
        image_type: &ImageType,
    ) -> Result<Option<MediaImage>> {
        let image = sqlx::query_as::<_, MediaImage>(
            "SELECT * FROM media_images WHERE item_id = ? AND image_type = ?",
        )
        .bind(item_id)
        .bind(image_type.as_str())
        .fetch_optional(self.database.pool())
        .await?;

        Ok(image)
    }

    async fn extract_video_frame(&self, video_path: &str) -> Result<PathBuf> {
        let output_path = self.cache_dir.join(format!("frame_{}.jpg", Uuid::new_v4()));

        // Use FFmpeg to extract a frame from the video
        let output = tokio::process::Command::new("ffmpeg")
            .args([
                "-i",
                video_path,
                "-ss",
                "00:00:10", // Seek to 10 seconds
                "-vframes",
                "1",
                "-q:v",
                "2",  // High quality
                "-y", // Overwrite output
                output_path.to_str().unwrap(),
            ])
            .output()
            .await?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow::anyhow!("FFmpeg frame extraction failed: {}", error));
        }

        Ok(output_path)
    }

    async fn find_nearby_image(&self, media_path: &str, image_type: &ImageType) -> Result<PathBuf> {
        let media_path = Path::new(media_path);
        let parent_dir = media_path
            .parent()
            .ok_or_else(|| anyhow::anyhow!("No parent directory"))?;

        // Common image filenames to look for
        let image_names = match image_type {
            ImageType::Primary => vec![
                "poster.jpg",
                "poster.png",
                "cover.jpg",
                "cover.png",
                "folder.jpg",
            ],
            ImageType::Backdrop => vec!["fanart.jpg", "fanart.png", "backdrop.jpg", "backdrop.png"],
            ImageType::Logo => vec!["logo.png", "logo.jpg"],
            ImageType::Banner => vec!["banner.jpg", "banner.png"],
            _ => vec!["poster.jpg", "poster.png", "cover.jpg", "cover.png"],
        };

        for name in image_names {
            let image_path = parent_dir.join(name);
            if image_path.exists() && is_image_file(&image_path) {
                return Ok(image_path);
            }
        }

        // If no specific image found, look for any image file
        let mut entries = fs::read_dir(parent_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if is_image_file(&path) {
                return Ok(path);
            }
        }

        Err(anyhow::anyhow!("No suitable image found"))
    }

    async fn create_thumbnail(
        &self,
        image_path: &Path,
        request: &ThumbnailRequest,
    ) -> Result<Vec<u8>> {
        debug!("Creating thumbnail from: {:?}", image_path);

        // Load the image
        let image_data = fs::read(image_path).await?;
        let img = image::load_from_memory(&image_data)?;

        // Determine target dimensions
        let (target_width, target_height) = self.calculate_thumbnail_dimensions(&img, request);

        // Resize the image with ARM64 optimizations
        let resized = if self.config.performance.arm64_optimizations {
            // Use faster filtering for ARM64
            img.resize(target_width, target_height, FilterType::Triangle)
        } else {
            img.resize(target_width, target_height, FilterType::Lanczos3)
        };

        // Encode to desired format
        let format = request.format.unwrap_or(ImageFormat::Jpeg);
        let quality = request.quality.unwrap_or(85);

        let mut output = Vec::new();
        let mut cursor = Cursor::new(&mut output);

        match format {
            ImageFormat::Jpeg => {
                let encoder =
                    image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, quality);
                resized.write_with_encoder(encoder)?;
            }
            ImageFormat::Png => {
                resized.write_to(&mut cursor, format)?;
            }
            ImageFormat::WebP => {
                // WebP encoding with quality
                resized.write_to(&mut cursor, format)?;
            }
            _ => {
                resized.write_to(&mut cursor, ImageFormat::Jpeg)?;
            }
        }

        Ok(output)
    }

    fn calculate_thumbnail_dimensions(
        &self,
        img: &DynamicImage,
        request: &ThumbnailRequest,
    ) -> (u32, u32) {
        let (original_width, original_height) = img.dimensions();

        let target_width = request.width.unwrap_or(300);
        let target_height = request.height.unwrap_or(300);

        // Maintain aspect ratio
        let width_ratio = target_width as f32 / original_width as f32;
        let height_ratio = target_height as f32 / original_height as f32;
        let ratio = width_ratio.min(height_ratio);

        let new_width = (original_width as f32 * ratio) as u32;
        let new_height = (original_height as f32 * ratio) as u32;

        (new_width, new_height)
    }

    async fn get_cached_thumbnail(&self, request: &ThumbnailRequest) -> Result<Vec<u8>> {
        let cache_key = self.generate_cache_key(request);
        let cache_path = self.cache_dir.join(format!("{}.cache", cache_key));

        if cache_path.exists() {
            Ok(fs::read(&cache_path).await?)
        } else {
            Err(anyhow::anyhow!("Not in cache"))
        }
    }

    async fn cache_thumbnail(&self, request: &ThumbnailRequest, data: &[u8]) -> Result<()> {
        let cache_key = self.generate_cache_key(request);
        let cache_path = self.cache_dir.join(format!("{}.cache", cache_key));

        fs::write(&cache_path, data).await?;

        // Store metadata in database
        let image = MediaImage {
            id: Uuid::new_v4().to_string(),
            item_id: request.item_id.clone(),
            image_type: request.image_type.as_str().to_string(),
            path: cache_path.to_string_lossy().to_string(),
            width: request.width.map(|w| w as i32),
            height: request.height.map(|h| h as i32),
            size: data.len() as i64,
            created_at: chrono::Utc::now(),
        };

        // Insert or update image record
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO media_images (id, item_id, image_type, path, width, height, size, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&image.id)
        .bind(&image.item_id)
        .bind(&image.image_type)
        .bind(&image.path)
        .bind(image.width)
        .bind(image.height)
        .bind(image.size)
        .bind(image.created_at.to_rfc3339())
        .execute(self.database.pool())
        .await?;

        Ok(())
    }

    pub(crate) fn generate_cache_key(&self, request: &ThumbnailRequest) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        request.item_id.hash(&mut hasher);
        request.image_type.as_str().hash(&mut hasher);
        request.width.hash(&mut hasher);
        request.height.hash(&mut hasher);
        request.quality.hash(&mut hasher);

        if let Some(format) = &request.format {
            format!("{:?}", format).hash(&mut hasher);
        }

        format!("{:x}", hasher.finish())
    }

    pub async fn cleanup_cache(&self) -> Result<()> {
        info!("Cleaning up image cache...");

        let max_cache_size = self.config.media.thumbnail_cache_size;
        let mut total_size = 0u64;
        let mut cache_files = Vec::new();

        // Collect all cache files with their metadata
        let mut entries = fs::read_dir(&self.cache_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if let Ok(metadata) = entry.metadata().await {
                cache_files.push((
                    path,
                    metadata.len(),
                    metadata
                        .modified()
                        .unwrap_or(std::time::SystemTime::UNIX_EPOCH),
                ));
                total_size += metadata.len();
            }
        }

        // If cache is too large, remove oldest files
        if total_size > max_cache_size {
            // Sort by modification time (oldest first)
            cache_files.sort_by_key(|(_, _, modified)| *modified);

            let mut removed_size = 0u64;
            for (path, size, _) in cache_files {
                if total_size - removed_size <= max_cache_size {
                    break;
                }

                if let Err(e) = fs::remove_file(&path).await {
                    warn!("Failed to remove cache file {:?}: {}", path, e);
                } else {
                    removed_size += size;
                    debug!("Removed cache file: {:?}", path);
                }
            }

            info!("Cleaned up {} bytes from image cache", removed_size);
        }

        Ok(())
    }

    pub async fn scan_for_images(&self, media_item: &MediaItem) -> Result<()> {
        debug!("Scanning for images for item: {}", media_item.id);

        let media_path = Path::new(&media_item.path);
        let parent_dir = match media_path.parent() {
            Some(dir) => dir,
            None => return Ok(()),
        };

        // Scan directory for image files
        let mut entries = fs::read_dir(parent_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();

            if !is_image_file(&path) {
                continue;
            }

            let filename = path
                .file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_lowercase();

            // Determine image type based on filename
            let image_type = if filename.contains("poster")
                || filename.contains("cover")
                || filename == "folder.jpg"
            {
                ImageType::Primary
            } else if filename.contains("fanart") || filename.contains("backdrop") {
                ImageType::Backdrop
            } else if filename.contains("logo") {
                ImageType::Logo
            } else if filename.contains("banner") {
                ImageType::Banner
            } else {
                continue; // Skip unknown image types
            };

            // Get image dimensions
            let (width, height, size) = match self.get_image_info(&path).await {
                Ok(info) => info,
                Err(e) => {
                    warn!("Failed to get image info for {:?}: {}", path, e);
                    continue;
                }
            };

            // Store image record
            let image = MediaImage {
                id: Uuid::new_v4().to_string(),
                item_id: media_item.id.clone(),
                image_type: image_type.as_str().to_string(),
                path: path.to_string_lossy().to_string(),
                width: Some(width),
                height: Some(height),
                size,
                created_at: chrono::Utc::now(),
            };

            sqlx::query(
                r#"
                INSERT OR REPLACE INTO media_images (id, item_id, image_type, path, width, height, size, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                "#,
            )
            .bind(&image.id)
            .bind(&image.item_id)
            .bind(&image.image_type)
            .bind(&image.path)
            .bind(image.width)
            .bind(image.height)
            .bind(image.size)
            .bind(image.created_at.to_rfc3339())
            .execute(self.database.pool())
            .await?;

            debug!("Registered image: {:?} -> {}", path, image_type.as_str());
        }

        Ok(())
    }

    async fn get_image_info(&self, path: &Path) -> Result<(i32, i32, i64)> {
        let metadata = fs::metadata(path).await?;
        let size = metadata.len() as i64;

        // Get image dimensions
        let image_data = fs::read(path).await?;
        let img = image::load_from_memory(&image_data)?;
        let (width, height) = img.dimensions();

        Ok((width as i32, height as i32, size))
    }
}

use anyhow::Result;
use std::sync::Arc;
use tracing::info;

use crate::{config::Config, database::Database};

pub mod images;
pub mod library;
pub mod metadata;
pub mod scanner;

use library::LibraryManager;

pub struct MediaManager {
    _config: Arc<Config>, // Reserved for future configuration options
    _database: Database,  // Reserved for future database operations
    library_manager: LibraryManager,
}

impl MediaManager {
    pub async fn new(config: Arc<Config>, database: Database) -> Result<Self> {
        let library_manager = LibraryManager::new(config.clone(), database.clone());

        Ok(Self {
            _config: config,
            _database: database,
            library_manager,
        })
    }

    pub async fn start_scanning(&self) -> Result<()> {
        info!("Starting media library scanning...");
        self.library_manager.initialize().await?;
        Ok(())
    }

    pub fn library_manager(&self) -> &LibraryManager {
        &self.library_manager
    }
}

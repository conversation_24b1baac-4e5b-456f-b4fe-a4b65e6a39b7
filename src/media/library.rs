use anyhow::Result;
use std::collections::HashMap;
use std::path::Path;
use std::sync::Arc;
use tracing::{debug, info};

use crate::{
    config::Config,
    database::{models::*, Database},
    media::{metadata::MetadataExtractor, scanner::MediaScanner},
};

pub struct LibraryManager {
    _config: Arc<Config>, // Reserved for future configuration options
    database: Database,
    scanner: MediaScanner,
    metadata_extractor: MetadataExtractor,
}

impl LibraryManager {
    pub fn new(config: Arc<Config>, database: Database) -> Self {
        let scanner = MediaScanner::new(config.clone(), database.clone());
        let metadata_extractor = MetadataExtractor::new();

        Self {
            _config: config,
            database,
            scanner,
            metadata_extractor,
        }
    }

    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing library manager...");

        // Start media scanning
        self.scanner.start_scanning().await?;

        info!("Library manager initialized successfully");
        Ok(())
    }

    pub async fn get_libraries(&self) -> Result<Vec<Library>> {
        let libraries = sqlx::query_as::<_, Library>("SELECT * FROM libraries ORDER BY name")
            .fetch_all(self.database.pool())
            .await?;

        Ok(libraries)
    }

    pub async fn get_library_items(
        &self,
        library_id: &str,
        parent_id: Option<&str>,
    ) -> Result<Vec<MediaItem>> {
        let items = if let Some(parent) = parent_id {
            sqlx::query_as::<_, MediaItem>(
                "SELECT * FROM media_items WHERE library_id = ? AND parent_id = ? ORDER BY sort_name"
            )
            .bind(library_id)
            .bind(parent)
            .fetch_all(self.database.pool())
            .await?
        } else {
            sqlx::query_as::<_, MediaItem>(
                "SELECT * FROM media_items WHERE library_id = ? AND parent_id IS NULL ORDER BY sort_name"
            )
            .bind(library_id)
            .fetch_all(self.database.pool())
            .await?
        };

        Ok(items)
    }

    pub async fn get_media_item(&self, item_id: &str) -> Result<Option<MediaItem>> {
        let item = sqlx::query_as::<_, MediaItem>("SELECT * FROM media_items WHERE id = ?")
            .bind(item_id)
            .fetch_optional(self.database.pool())
            .await?;

        Ok(item)
    }

    pub async fn get_media_metadata(&self, item_id: &str) -> Result<Option<MediaMetadata>> {
        let metadata =
            sqlx::query_as::<_, MediaMetadata>("SELECT * FROM media_metadata WHERE item_id = ?")
                .bind(item_id)
                .fetch_optional(self.database.pool())
                .await?;

        Ok(metadata)
    }

    pub async fn get_recent_items(&self, limit: i64) -> Result<Vec<MediaItem>> {
        let items = sqlx::query_as::<_, MediaItem>(
            "SELECT * FROM media_items ORDER BY date_added DESC LIMIT ?",
        )
        .bind(limit)
        .fetch_all(self.database.pool())
        .await?;

        Ok(items)
    }

    pub async fn search_items(&self, query: &str, limit: i64) -> Result<Vec<MediaItem>> {
        let search_pattern = format!("%{}%", query);

        let items = sqlx::query_as::<_, MediaItem>(
            r#"
            SELECT mi.* FROM media_items mi
            LEFT JOIN media_metadata mm ON mi.id = mm.item_id
            WHERE mi.name LIKE ? 
               OR mi.sort_name LIKE ?
               OR mm.title LIKE ?
               OR mm.original_title LIKE ?
            ORDER BY mi.sort_name
            LIMIT ?
            "#,
        )
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(&search_pattern)
        .bind(limit)
        .fetch_all(self.database.pool())
        .await?;

        Ok(items)
    }

    pub async fn get_tv_shows(&self) -> Result<Vec<TvShowSummary>> {
        let shows = sqlx::query_as::<_, TvShowSummary>(
            r#"
            SELECT 
                mi.id,
                mi.name,
                COUNT(DISTINCT seasons.id) as season_count,
                COUNT(episodes.id) as episode_count
            FROM media_items mi
            LEFT JOIN media_items seasons ON seasons.parent_id = mi.id AND seasons.item_type = 'Season'
            LEFT JOIN media_items episodes ON episodes.parent_id = seasons.id AND episodes.item_type = 'Episode'
            WHERE mi.item_type = 'Series'
            GROUP BY mi.id, mi.name
            ORDER BY mi.sort_name
            "#
        )
        .fetch_all(self.database.pool())
        .await?;

        Ok(shows)
    }

    pub async fn get_tv_seasons(&self, series_id: &str) -> Result<Vec<MediaItem>> {
        let seasons = sqlx::query_as::<_, MediaItem>(
            "SELECT * FROM media_items WHERE parent_id = ? AND item_type = 'Season' ORDER BY sort_name"
        )
        .bind(series_id)
        .fetch_all(self.database.pool())
        .await?;

        Ok(seasons)
    }

    pub async fn get_tv_episodes(&self, season_id: &str) -> Result<Vec<MediaItem>> {
        let episodes = sqlx::query_as::<_, MediaItem>(
            "SELECT * FROM media_items WHERE parent_id = ? AND item_type = 'Episode' ORDER BY sort_name"
        )
        .bind(season_id)
        .fetch_all(self.database.pool())
        .await?;

        Ok(episodes)
    }

    pub async fn organize_tv_content(&self) -> Result<()> {
        info!("Organizing TV content into series/seasons/episodes...");

        // Get all potential TV episodes
        let episodes =
            sqlx::query_as::<_, MediaItem>("SELECT * FROM media_items WHERE item_type = 'Episode'")
                .fetch_all(self.database.pool())
                .await?;

        let mut series_map: HashMap<String, String> = HashMap::new();
        let mut season_map: HashMap<(String, i32), String> = HashMap::new();

        for episode in episodes {
            if let Some(tv_info) = self
                .metadata_extractor
                .extract_tv_show_info(Path::new(&episode.path))
                .await
            {
                // Get or create series
                let series_id = if let Some(existing_id) = series_map.get(&tv_info.show_name) {
                    existing_id.clone()
                } else {
                    let series_id = self
                        .get_or_create_series(&tv_info.show_name, &episode.library_id)
                        .await?;
                    series_map.insert(tv_info.show_name.clone(), series_id.clone());
                    series_id
                };

                // Get or create season
                let season_key = (series_id.clone(), tv_info.season_number);
                let season_id = if let Some(existing_id) = season_map.get(&season_key) {
                    existing_id.clone()
                } else {
                    let season_id = self
                        .get_or_create_season(&series_id, tv_info.season_number)
                        .await?;
                    season_map.insert(season_key, season_id.clone());
                    season_id
                };

                // Update episode parent
                sqlx::query("UPDATE media_items SET parent_id = ? WHERE id = ?")
                    .bind(&season_id)
                    .bind(&episode.id)
                    .execute(self.database.pool())
                    .await?;

                debug!(
                    "Organized episode: {} -> Season {} -> {}",
                    tv_info.show_name, tv_info.season_number, episode.name
                );
            }
        }

        info!("TV content organization complete");
        Ok(())
    }

    async fn get_or_create_series(&self, series_name: &str, library_id: &str) -> Result<String> {
        // Check if series already exists
        if let Some(existing) = sqlx::query_as::<_, MediaItem>(
            "SELECT * FROM media_items WHERE name = ? AND item_type = 'Series' AND library_id = ?",
        )
        .bind(series_name)
        .bind(library_id)
        .fetch_optional(self.database.pool())
        .await?
        {
            return Ok(existing.id);
        }

        // Create new series
        let series = MediaItem::new(
            library_id.to_string(),
            None,
            series_name.to_string(),
            format!("/virtual/series/{}", series_name),
            "Series".to_string(),
            "Video".to_string(),
            0,
        );

        sqlx::query(
            r#"
            INSERT INTO media_items (
                id, library_id, parent_id, name, sort_name, path, item_type, media_type,
                file_size, duration, bitrate, container, video_codec, audio_codec,
                width, height, aspect_ratio, framerate, created_at, updated_at,
                date_added, date_modified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&series.id)
        .bind(&series.library_id)
        .bind(&series.parent_id)
        .bind(&series.name)
        .bind(&series.sort_name)
        .bind(&series.path)
        .bind(&series.item_type)
        .bind(&series.media_type)
        .bind(series.file_size)
        .bind(series.duration)
        .bind(series.bitrate)
        .bind(&series.container)
        .bind(&series.video_codec)
        .bind(&series.audio_codec)
        .bind(series.width)
        .bind(series.height)
        .bind(&series.aspect_ratio)
        .bind(series.framerate)
        .bind(series.created_at.to_rfc3339())
        .bind(series.updated_at.to_rfc3339())
        .bind(series.date_added.to_rfc3339())
        .bind(series.date_modified.to_rfc3339())
        .execute(self.database.pool())
        .await?;

        Ok(series.id)
    }

    async fn get_or_create_season(&self, series_id: &str, season_number: i32) -> Result<String> {
        let season_name = format!("Season {}", season_number);

        // Check if season already exists
        if let Some(existing) = sqlx::query_as::<_, MediaItem>(
            "SELECT * FROM media_items WHERE parent_id = ? AND name = ? AND item_type = 'Season'",
        )
        .bind(series_id)
        .bind(&season_name)
        .fetch_optional(self.database.pool())
        .await?
        {
            return Ok(existing.id);
        }

        // Get series info for library_id
        let series = sqlx::query_as::<_, MediaItem>("SELECT * FROM media_items WHERE id = ?")
            .bind(series_id)
            .fetch_one(self.database.pool())
            .await?;

        // Create new season
        let season = MediaItem::new(
            series.library_id,
            Some(series_id.to_string()),
            season_name,
            format!("/virtual/season/{}/{}", series_id, season_number),
            "Season".to_string(),
            "Video".to_string(),
            0,
        );

        sqlx::query(
            r#"
            INSERT INTO media_items (
                id, library_id, parent_id, name, sort_name, path, item_type, media_type,
                file_size, duration, bitrate, container, video_codec, audio_codec,
                width, height, aspect_ratio, framerate, created_at, updated_at,
                date_added, date_modified
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&season.id)
        .bind(&season.library_id)
        .bind(&season.parent_id)
        .bind(&season.name)
        .bind(&season.sort_name)
        .bind(&season.path)
        .bind(&season.item_type)
        .bind(&season.media_type)
        .bind(season.file_size)
        .bind(season.duration)
        .bind(season.bitrate)
        .bind(&season.container)
        .bind(&season.video_codec)
        .bind(&season.audio_codec)
        .bind(season.width)
        .bind(season.height)
        .bind(&season.aspect_ratio)
        .bind(season.framerate)
        .bind(season.created_at.to_rfc3339())
        .bind(season.updated_at.to_rfc3339())
        .bind(season.date_added.to_rfc3339())
        .bind(season.date_modified.to_rfc3339())
        .execute(self.database.pool())
        .await?;

        Ok(season.id)
    }
}

#[derive(Debug, sqlx::FromRow)]
pub struct TvShowSummary {
    pub id: String,
    pub name: String,
    pub season_count: i64,
    pub episode_count: i64,
}

use once_cell::sync::Lazy;
use prometheus::{Encoder, IntCounter, IntGauge, Opts, Registry, TextEncoder};

// Global registry and metrics
pub static REGISTRY: Lazy<Registry> = Lazy::new(|| Registry::new());

pub static ACTIVE_SESSIONS: Lazy<IntGauge> = Lazy::new(|| {
    let g = IntGauge::with_opts(Opts::new(
        "active_transcoding_sessions",
        "Number of active transcoding sessions",
    ))
    .unwrap();
    REGISTRY.register(Box::new(g.clone())).ok();
    g
});

pub static TRANSCODING_FAILURES: Lazy<IntCounter> = Lazy::new(|| {
    let c = IntCounter::with_opts(Opts::new(
        "transcoding_failures_total",
        "Total transcoding failures",
    ))
    .unwrap();
    REGISTRY.register(Box::new(c.clone())).ok();
    c
});

pub fn gather_metrics() -> String {
    let encoder = TextEncoder::new();
    let metric_families = REGISTRY.gather();
    let mut buffer = Vec::new();
    encoder.encode(&metric_families, &mut buffer).unwrap();
    String::from_utf8(buffer).unwrap()
}

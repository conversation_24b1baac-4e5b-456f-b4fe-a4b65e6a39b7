use crate::cli::Cli;
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub media: MediaConfig,
    pub streaming: StreamingConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
}

#[derive(Debu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub bind_address: String,
    pub port: u16,
    pub base_url: Option<String>,
    pub server_name: String,
    pub enable_https: bool,
    pub cert_path: Option<PathBuf>,
    pub key_path: Option<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub path: PathBuf,
    pub max_connections: u32,
    pub connection_timeout: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MediaConfig {
    pub library_paths: Vec<PathBuf>,
    pub scan_interval: u64,
    pub thumbnail_cache_size: u64,
    pub metadata_cache_size: u64,
    pub supported_video_formats: Vec<String>,
    pub supported_audio_formats: Vec<String>,
    pub supported_image_formats: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamingConfig {
    pub enable_transcoding: bool,
    pub max_concurrent_streams: u32,
    pub hardware_acceleration: bool,
    pub video_codecs: Vec<String>,
    pub audio_codecs: Vec<String>,
    pub max_bitrate: u64,
    pub segment_duration: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub jwt_secret: String,
    pub session_timeout: u64,
    pub max_login_attempts: u32,
    pub enable_api_keys: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub worker_threads: Option<usize>,
    pub max_blocking_threads: usize,
    pub enable_compression: bool,
    pub cache_size_mb: u64,
    pub arm64_optimizations: bool,
    /// When true, spawn transcoding under `setsid nice -n <nice_level> ffmpeg ...` to lower CPU priority
    pub nice_on_transcode: bool,
    /// Nice level to use when `nice_on_transcode` is true. Typical values: 0 (default) to 19 (lowest priority)
    pub nice_level: i32,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                bind_address: "0.0.0.0".to_string(),
                port: 8096,
                base_url: None,
                server_name: "Tulip Media Server".to_string(),
                enable_https: false,
                cert_path: None,
                key_path: None,
            },
            database: DatabaseConfig {
                path: PathBuf::from("./data/tulip.db"),
                max_connections: 10,
                connection_timeout: 30,
            },
            media: MediaConfig {
                library_paths: vec![],
                scan_interval: 3600,                     // 1 hour
                thumbnail_cache_size: 500 * 1024 * 1024, // 500MB
                metadata_cache_size: 100 * 1024 * 1024,  // 100MB
                supported_video_formats: vec![
                    "mp4".to_string(),
                    "mkv".to_string(),
                    "avi".to_string(),
                    "mov".to_string(),
                    "wmv".to_string(),
                    "flv".to_string(),
                    "webm".to_string(),
                    "m4v".to_string(),
                ],
                supported_audio_formats: vec![
                    "mp3".to_string(),
                    "flac".to_string(),
                    "aac".to_string(),
                    "ogg".to_string(),
                    "wav".to_string(),
                    "m4a".to_string(),
                ],
                supported_image_formats: vec![
                    "jpg".to_string(),
                    "jpeg".to_string(),
                    "png".to_string(),
                    "webp".to_string(),
                    "bmp".to_string(),
                ],
            },
            streaming: StreamingConfig {
                enable_transcoding: true,
                max_concurrent_streams: 2, // Target for Nano Pi: limit to 1-2 clients
                hardware_acceleration: true,
                video_codecs: vec!["h264".to_string(), "h265".to_string()],
                audio_codecs: vec!["aac".to_string(), "mp3".to_string()],
                max_bitrate: 10_000_000, // 10 Mbps conservative default for low-power devices
                segment_duration: 6,
            },
            security: SecurityConfig {
                jwt_secret: uuid::Uuid::new_v4().to_string(),
                session_timeout: 86400, // 24 hours
                max_login_attempts: 5,
                enable_api_keys: true,
            },
            performance: PerformanceConfig {
                worker_threads: Some(2), // Conservative default for Nano Pi
                max_blocking_threads: 2,
                enable_compression: true,
                cache_size_mb: 128, // Reduce cache memory usage
                arm64_optimizations: cfg!(target_arch = "aarch64"),
                nice_on_transcode: true,
                nice_level: 10,
            },
        }
    }
}

impl Config {
    pub fn load(config_path: &std::path::Path, cli: Cli) -> Result<Self> {
        let mut config = if config_path.exists() {
            let content = std::fs::read_to_string(config_path)
                .with_context(|| format!("Failed to read config file: {:?}", config_path))?;
            toml::from_str(&content)
                .with_context(|| format!("Failed to parse config file: {:?}", config_path))?
        } else {
            Self::default()
        };

        // Override with CLI arguments
        config.server.bind_address = cli.bind_address;
        config.server.port = cli.port;

        if !cli.media_dirs.is_empty() {
            config.media.library_paths = cli.media_dirs;
        }

        // Ensure data directory exists
        std::fs::create_dir_all(&cli.data_dir)
            .with_context(|| format!("Failed to create data directory: {:?}", cli.data_dir))?;

        config.database.path = cli.data_dir.join("tulip.db");

        // Validate configuration
        config.validate()?;

        Ok(config)
    }

    fn validate(&self) -> Result<()> {
        if self.media.library_paths.is_empty() {
            anyhow::bail!("At least one media library path must be specified");
        }

        for path in &self.media.library_paths {
            if !path.exists() {
                anyhow::bail!("Media library path does not exist: {:?}", path);
            }
        }

        if self.server.port == 0 {
            anyhow::bail!("Server port must be greater than 0");
        }

        Ok(())
    }
}

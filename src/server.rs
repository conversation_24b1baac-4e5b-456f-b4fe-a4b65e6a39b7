use anyhow::Result;
use axum::{
    http::Method,
    response::Json,
    routing::{get, post},
    Router,
};
use serde_json::{json, Value};
use std::{net::SocketAddr, sync::Arc};
use tokio::net::TcpListener;

use tower_http::{
    compression::CompressionLayer,
    cors::{Any, CorsLayer},
    trace::TraceLayer,
};
use tracing::{info, warn};

use crate::{
    api::{media, system, users},
    auth,
    config::Config,
    database::Database,
    discovery::DiscoveryService,
    media::MediaManager,
    streaming::StreamingEngine,
    metrics,
};
use axum::http::header;

#[derive(Clone)]
pub struct AppState {
    pub config: Arc<Config>,
    pub database: Database,
    pub media_manager: Arc<MediaManager>,
    pub streaming_engine: Arc<StreamingEngine>,
}

pub struct Server {
    config: Arc<Config>,
    state: AppState,
    discovery_service: DiscoveryService,
}

impl Server {
    pub async fn new(config: Config) -> Result<Self> {
        let config = Arc::new(config);

        // Initialize database
        let database = Database::new(&config.database).await?;
        database.migrate().await?;

        // Initialize media manager
        let media_manager = Arc::new(MediaManager::new(config.clone(), database.clone()).await?);

        // Initialize streaming engine
        let streaming_engine = Arc::new(StreamingEngine::new(config.clone())?);

        // Initialize discovery service
        let discovery_service = DiscoveryService::new(config.clone())?;

        let state = AppState {
            config: config.clone(),
            database,
            media_manager,
            streaming_engine,
        };

        Ok(Self {
            config,
            state,
            discovery_service,
        })
    }

    pub async fn run(self) -> Result<()> {
        // Extract values before moving self
        let discovery_service = self.discovery_service;
        let media_manager = self.state.media_manager.clone();
        let bind_address = self.config.server.bind_address.clone();
        let port = self.config.server.port;

        // Start discovery service
        let discovery_handle = tokio::spawn(async move {
            if let Err(e) = discovery_service.start().await {
                warn!("Discovery service error: {}", e);
            }
        });

        // Start media scanning
        let scan_handle = tokio::spawn(async move {
            if let Err(e) = media_manager.start_scanning().await {
                warn!("Media scanning failed: {}", e);
            }
        });

        // Build router
        let app = build_router(self.state, self.config.performance.enable_compression);

        // Start server
        let addr = SocketAddr::new(bind_address.parse()?, port);

        info!("Starting server on {}", addr);
        info!("Jellyfin API compatibility enabled");
        info!("Web interface available at http://{}/web", addr);

        let listener = TcpListener::bind(addr).await?;

        // Graceful shutdown handling
        let shutdown_signal = async {
            tokio::signal::ctrl_c()
                .await
                .expect("Failed to install CTRL+C signal handler");
            info!("Shutdown signal received");
        };

        axum::serve(listener, app)
            .with_graceful_shutdown(shutdown_signal)
            .await?;

        // Wait for background tasks to complete
        discovery_handle.abort();
        scan_handle.abort();

        info!("Server shutdown complete");
        Ok(())
    }
}

fn build_router(state: AppState, enable_compression: bool) -> Router {
    let cors = CorsLayer::new()
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers(Any)
        .allow_origin(Any);

    let mut router = Router::new()
        // System endpoints (Jellyfin compatible)
        .route("/System/Info", get(system::get_system_info))
        .route("/System/Info/Public", get(system::get_public_system_info))
        .route("/System/Ping", get(system::ping))
        // Authentication endpoints
        .route(
            "/Users/<USER>",
            post(auth::authenticate_by_name),
        )
        .route(
            "/Users/<USER>",
            post(auth::authenticate_quick_connect),
        )
        .route("/Sessions/Logout", post(auth::logout))
        // Sessions
        .route("/Sessions", get(auth::list_sessions))
        .route("/Sessions/Playing/Start", post(auth::playing_start))
        .route("/Sessions/Playing/Progress", post(auth::playing_progress))
        .route("/Sessions/Playing/Stopped", post(auth::playing_stopped))
        .route("/Sessions/:session_id/Command", post(auth::session_command))
        // User management
        .route("/Users", get(users::get_users))
        .route("/Users/<USER>", get(users::get_user))
        .route("/Users/<USER>/Items/Resume", get(users::get_resume_items))
        .route("/Users/<USER>/Items/NextUp", get(users::get_next_up))
        .route("/Users/<USER>", get(users::get_current_user))
        // Media library endpoints
        .route("/Items", get(media::get_items))
        .route("/Items/:item_id", get(media::get_item))
        .route(
            "/Items/:item_id/Images/:image_type",
            get(media::get_item_image),
        )
        .route(
            "/Items/:item_id/PlaybackInfo",
            get(media::get_playback_info),
        )
        // Streaming endpoints
        .route(
            "/Videos/:item_id/stream",
            get(crate::streaming::stream_video),
        )
        .route(
            "/Videos/:item_id/master.m3u8",
            get(crate::streaming::get_hls_playlist),
        )
        .route(
            "/Videos/:item_id/hls/:segment",
            get(crate::streaming::get_hls_segment),
        )
        .route(
            "/Videos/:item_id/variant_:profile.m3u8",
            get(crate::streaming::hls::get_variant_playlist),
        )
        .route(
            "/Videos/:item_id/:profile/:segment",
            get(crate::streaming::hls::get_segment),
        )
        // Static web interface
        .route("/web/*path", get(serve_web_interface))
    .route("/metrics", get(metrics_handler))
        .route("/", get(redirect_to_web))
        .layer(TraceLayer::new_for_http())
        .layer(cors)
        .with_state(state);

    if enable_compression {
        router = router.layer(CompressionLayer::new());
    }

    router
}

async fn metrics_handler() -> impl axum::response::IntoResponse {
    let body = metrics::gather_metrics();
    (
        axum::http::StatusCode::OK,
        [(header::CONTENT_TYPE, "text/plain; version=0.0.4")],
        body,
    )
}

// Handler for serving the web interface
async fn serve_web_interface() -> Json<Value> {
    Json(json!({
        "message": "Web interface not yet implemented. Use a Jellyfin client app to connect.",
        "api_docs": "/api-docs/swagger",
        "status": "ready"
    }))
}

// Redirect root to web interface
async fn redirect_to_web() -> Json<Value> {
    Json(json!({
        "ProductName": "Tulip Media Server",
        "Version": env!("CARGO_PKG_VERSION"),
        "Id": uuid::Uuid::new_v4(),
        "StartupWizardCompleted": true,
        "OperatingSystem": "Linux",
        "Architecture": "ARM64",
        "ServerName": "Tulip Media Server"
    }))
}

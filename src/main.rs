#![recursion_limit = "256"]

use anyhow::Result;
use clap::Parser;
use tracing::{info, warn};

use tulip_media::cli::Cli;
use tulip_media::config::Config;
use tulip_media::server::Server;

use tulip_media as _; // ensure crate is linked

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    let log_level = if cli.debug { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(format!("tulip_media={},tower_http=debug", log_level))
        .init();

    info!("Starting Tulip Media Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Optimized for ARM64 architecture (Nano Pi M4 V2)");

    // Load configuration
    let config_path = cli.config.clone();
    let config = Config::load(&config_path, cli)?;

    // Validate ARM64 architecture
    if cfg!(not(target_arch = "aarch64")) {
        warn!("This server is optimized for ARM64 architecture. Performance may be suboptimal on other architectures.");
    }

    // Initialize and start server
    let server = Server::new(config).await?;
    server.run().await?;

    Ok(())
}

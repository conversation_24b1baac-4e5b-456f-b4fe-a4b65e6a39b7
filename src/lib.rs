#![recursion_limit = "256"]
//! Tulip Media Server - A high-performance Jellyfin-compatible media server
//!
//! This library provides all the core functionality for the Tulip Media Server,
//! optimized for ARM64 devices like the Nano Pi M4 V2.

pub mod api;
pub mod auth;
pub mod cli;
pub mod config;
pub mod database;
pub mod discovery;
pub mod media;
pub mod metrics;
pub mod server;
pub mod streaming;
pub mod test_utils; // Added test utilities
pub mod utils;

// Re-export commonly used types for convenience
pub use config::Config;
pub use database::Database;
pub use server::{AppState, Server};

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Initialize the media server with the given configuration
pub async fn init(config: Config) -> anyhow::Result<Server> {
    Server::new(config).await
}

/// Get the default configuration for the media server
pub fn default_config() -> Config {
    Config::default()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
        assert!(!NAME.is_empty());
    }

    #[test]
    fn test_default_config() {
        let config = default_config();
        assert_eq!(config.server.port, 8096);
        assert_eq!(config.server.server_name, "Tulip Media Server");
    }
}

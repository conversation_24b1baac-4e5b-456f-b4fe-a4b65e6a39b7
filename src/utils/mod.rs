pub mod auth;
pub mod media;
pub mod performance;

use std::path::Path;

/// Get file extension from path
pub fn get_file_extension(path: &Path) -> Option<String> {
    path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| ext.to_lowercase())
}

/// Check if file is a video file
pub fn is_video_file(path: &Path) -> bool {
    if let Some(ext) = get_file_extension(path) {
        matches!(
            ext.as_str(),
            "mp4" | "mkv" | "avi" | "mov" | "wmv" | "flv" | "webm" | "m4v" | "ts" | "m2ts"
        )
    } else {
        false
    }
}

/// Check if file is an audio file
pub fn is_audio_file(path: &Path) -> bool {
    if let Some(ext) = get_file_extension(path) {
        matches!(
            ext.as_str(),
            "mp3" | "flac" | "aac" | "ogg" | "wav" | "m4a" | "wma" | "opus"
        )
    } else {
        false
    }
}

/// Check if file is an image file
pub fn is_image_file(path: &Path) -> bool {
    if let Some(ext) = get_file_extension(path) {
        matches!(
            ext.as_str(),
            "jpg" | "jpeg" | "png" | "webp" | "bmp" | "gif"
        )
    } else {
        false
    }
}

/// Format file size in human readable format
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// Format duration in human readable format
pub fn format_duration(duration_ms: u64) -> String {
    let total_seconds = duration_ms / 1000;
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;

    if hours > 0 {
        format!("{:02}:{:02}:{:02}", hours, minutes, seconds)
    } else {
        format!("{:02}:{:02}", minutes, seconds)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_file_extension() {
        assert_eq!(
            get_file_extension(&PathBuf::from("test.mp4")),
            Some("mp4".to_string())
        );
        assert_eq!(
            get_file_extension(&PathBuf::from("test.MP4")),
            Some("mp4".to_string())
        );
        assert_eq!(get_file_extension(&PathBuf::from("test")), None);
    }

    #[test]
    fn test_is_video_file() {
        assert!(is_video_file(&PathBuf::from("movie.mp4")));
        assert!(is_video_file(&PathBuf::from("movie.MKV")));
        assert!(!is_video_file(&PathBuf::from("song.mp3")));
        assert!(!is_video_file(&PathBuf::from("document.txt")));
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
        assert_eq!(format_file_size(1073741824), "1.0 GB");
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(30000), "00:30");
        assert_eq!(format_duration(90000), "01:30");
        assert_eq!(format_duration(3661000), "01:01:01");
    }
}

use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tracing::info;

/// ARM64 performance optimization utilities
pub struct PerformanceOptimizer {
    cpu_count: usize,
    memory_limit: usize,
    _cache_size: usize, // Reserved for future cache management
}

impl PerformanceOptimizer {
    pub fn new(cache_size_mb: u64) -> Self {
        let cpu_count = num_cpus::get();
        let memory_limit = get_available_memory();
        let cache_size = (cache_size_mb * 1024 * 1024) as usize;

        info!("Performance optimizer initialized:");
        info!("  CPU cores: {}", cpu_count);
        info!("  Memory limit: {} MB", memory_limit / 1024 / 1024);
        info!("  Cache size: {} MB", cache_size_mb);

        Self {
            cpu_count,
            memory_limit,
            _cache_size: cache_size,
        }
    }

    /// Get optimal thread count for ARM64
    pub fn get_optimal_thread_count(&self) -> usize {
        // For ARM64, use conservative thread count to avoid thermal throttling
        std::cmp::min(self.cpu_count, 4)
    }

    /// Get optimal chunk size for parallel processing
    pub fn get_optimal_chunk_size(&self, total_items: usize) -> usize {
        let thread_count = self.get_optimal_thread_count();
        std::cmp::max(total_items / thread_count, 1)
    }

    /// Check if we should use SIMD optimizations
    pub fn should_use_simd(&self) -> bool {
        cfg!(target_arch = "aarch64") && cfg!(target_feature = "neon")
    }

    /// Get memory pressure level (0.0 = no pressure, 1.0 = high pressure)
    pub fn get_memory_pressure(&self) -> f32 {
        let used_memory = get_used_memory();
        used_memory as f32 / self.memory_limit as f32
    }

    /// Suggest whether to enable compression based on CPU/memory trade-off
    pub fn should_enable_compression(&self) -> bool {
        // Enable compression if memory pressure is high and CPU usage is low
        let memory_pressure = self.get_memory_pressure();
        let cpu_usage = get_cpu_usage();

        memory_pressure > 0.7 && cpu_usage < 0.5
    }

    /// Get optimal buffer size for I/O operations
    pub fn get_optimal_buffer_size(&self) -> usize {
        // ARM64 typically benefits from 64KB buffers
        if cfg!(target_arch = "aarch64") {
            64 * 1024
        } else {
            32 * 1024
        }
    }
}

/// Simple CPU usage tracker
static CPU_USAGE: AtomicUsize = AtomicUsize::new(0);
static LAST_CPU_CHECK: std::sync::Mutex<Option<Instant>> = std::sync::Mutex::new(None);

fn get_cpu_usage() -> f32 {
    // Simple CPU usage estimation
    // In a real implementation, you'd read from /proc/stat
    let mut last_check = LAST_CPU_CHECK.lock().unwrap();
    let now = Instant::now();

    if let Some(last) = *last_check {
        if now.duration_since(last) < Duration::from_secs(1) {
            return CPU_USAGE.load(Ordering::Relaxed) as f32 / 100.0;
        }
    }

    *last_check = Some(now);

    // Placeholder: return moderate CPU usage
    0.3
}

fn get_available_memory() -> usize {
    // Try to read from /proc/meminfo
    if let Ok(meminfo) = std::fs::read_to_string("/proc/meminfo") {
        for line in meminfo.lines() {
            if line.starts_with("MemTotal:") {
                if let Some(kb_str) = line.split_whitespace().nth(1) {
                    if let Ok(kb) = kb_str.parse::<usize>() {
                        return kb * 1024; // Convert to bytes
                    }
                }
            }
        }
    }

    // Fallback: assume 4GB for Nano Pi M4 V2
    4 * 1024 * 1024 * 1024
}

fn get_used_memory() -> usize {
    // Try to read from /proc/meminfo
    if let Ok(meminfo) = std::fs::read_to_string("/proc/meminfo") {
        let mut mem_total = 0;
        let mut mem_available = 0;

        for line in meminfo.lines() {
            if line.starts_with("MemTotal:") {
                if let Some(kb_str) = line.split_whitespace().nth(1) {
                    if let Ok(kb) = kb_str.parse::<usize>() {
                        mem_total = kb * 1024;
                    }
                }
            } else if line.starts_with("MemAvailable:") {
                if let Some(kb_str) = line.split_whitespace().nth(1) {
                    if let Ok(kb) = kb_str.parse::<usize>() {
                        mem_available = kb * 1024;
                    }
                }
            }
        }

        if mem_total > 0 && mem_available > 0 {
            return mem_total - mem_available;
        }
    }

    // Fallback
    1024 * 1024 * 1024 // 1GB
}

/// ARM64 SIMD optimizations for image processing
#[cfg(target_arch = "aarch64")]
pub mod simd {
    use std::arch::aarch64::*;

    /// Fast RGB to grayscale conversion using NEON
    pub unsafe fn rgb_to_grayscale_neon(rgb: &[u8], grayscale: &mut [u8]) {
        assert_eq!(rgb.len(), grayscale.len() * 3);

        let len = grayscale.len();
        let chunks = len / 16; // Process 16 pixels at a time

        // Weights for RGB to grayscale: R=0.299, G=0.587, B=0.114
        let weights_r = vdupq_n_u8((0.299 * 256.0) as u8);
        let weights_g = vdupq_n_u8((0.587 * 256.0) as u8);
        let weights_b = vdupq_n_u8((0.114 * 256.0) as u8);

        for i in 0..chunks {
            let rgb_offset = i * 48; // 16 pixels * 3 channels
            let gray_offset = i * 16;

            // Load RGB data
            let r = vld1q_u8(rgb.as_ptr().add(rgb_offset));
            let g = vld1q_u8(rgb.as_ptr().add(rgb_offset + 16));
            let b = vld1q_u8(rgb.as_ptr().add(rgb_offset + 32));

            // Multiply by weights
            let r_weighted = vmulq_u8(r, weights_r);
            let g_weighted = vmulq_u8(g, weights_g);
            let b_weighted = vmulq_u8(b, weights_b);

            // Add components
            let gray = vaddq_u8(vaddq_u8(r_weighted, g_weighted), b_weighted);

            // Store result
            vst1q_u8(grayscale.as_mut_ptr().add(gray_offset), gray);
        }

        // Handle remaining pixels
        for i in (chunks * 16)..len {
            let rgb_offset = i * 3;
            let r = rgb[rgb_offset] as f32;
            let g = rgb[rgb_offset + 1] as f32;
            let b = rgb[rgb_offset + 2] as f32;

            grayscale[i] = (r * 0.299 + g * 0.587 + b * 0.114) as u8;
        }
    }

    /// Fast image scaling using NEON
    pub unsafe fn scale_image_neon(
        src: &[u8],
        src_width: usize,
        src_height: usize,
        dst: &mut [u8],
        dst_width: usize,
        dst_height: usize,
    ) {
        let x_ratio = (src_width << 16) / dst_width;
        let y_ratio = (src_height << 16) / dst_height;

        for y in 0..dst_height {
            let src_y = (y * y_ratio) >> 16;
            let dst_row = y * dst_width;
            let src_row = src_y * src_width;

            for x in 0..dst_width {
                let src_x = (x * x_ratio) >> 16;
                dst[dst_row + x] = src[src_row + src_x];
            }
        }
    }
}

#[cfg(not(target_arch = "aarch64"))]
pub mod simd {
    /// Fallback implementations for non-ARM64 architectures
    pub fn rgb_to_grayscale_neon(rgb: &[u8], grayscale: &mut [u8]) {
        assert_eq!(rgb.len(), grayscale.len() * 3);

        for i in 0..grayscale.len() {
            let rgb_offset = i * 3;
            let r = rgb[rgb_offset] as f32;
            let g = rgb[rgb_offset + 1] as f32;
            let b = rgb[rgb_offset + 2] as f32;

            grayscale[i] = (r * 0.299 + g * 0.587 + b * 0.114) as u8;
        }
    }

    pub fn scale_image_neon(
        src: &[u8],
        src_width: usize,
        src_height: usize,
        dst: &mut [u8],
        dst_width: usize,
        dst_height: usize,
    ) {
        let x_ratio = (src_width << 16) / dst_width;
        let y_ratio = (src_height << 16) / dst_height;

        for y in 0..dst_height {
            let src_y = (y * y_ratio) >> 16;
            let dst_row = y * dst_width;
            let src_row = src_y * src_width;

            for x in 0..dst_width {
                let src_x = (x * x_ratio) >> 16;
                dst[dst_row + x] = src[src_row + src_x];
            }
        }
    }
}

/// Memory pool for reducing allocations
pub struct MemoryPool<T> {
    pool: std::sync::Mutex<Vec<Vec<T>>>,
    max_size: usize,
}

impl<T: Clone + Default> MemoryPool<T> {
    pub fn new(max_size: usize) -> Self {
        Self {
            pool: std::sync::Mutex::new(Vec::new()),
            max_size,
        }
    }

    pub fn get(&self, size: usize) -> Vec<T> {
        let mut pool = self.pool.lock().unwrap();

        // Try to find a suitable buffer
        for i in 0..pool.len() {
            if pool[i].len() >= size {
                let mut buffer = pool.swap_remove(i);
                buffer.truncate(size);
                return buffer;
            }
        }

        // Create new buffer
        vec![T::default(); size]
    }

    pub fn return_buffer(&self, mut buffer: Vec<T>) {
        let mut pool = self.pool.lock().unwrap();

        if pool.len() < self.max_size {
            buffer.clear();
            pool.push(buffer);
        }
    }
}

/// Performance monitoring
pub struct PerformanceMonitor {
    start_time: Instant,
    operation_count: AtomicUsize,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
            operation_count: AtomicUsize::new(0),
        }
    }

    pub fn record_operation(&self) {
        self.operation_count.fetch_add(1, Ordering::Relaxed);
    }

    pub fn get_operations_per_second(&self) -> f64 {
        let elapsed = self.start_time.elapsed().as_secs_f64();
        let count = self.operation_count.load(Ordering::Relaxed) as f64;

        if elapsed > 0.0 {
            count / elapsed
        } else {
            0.0
        }
    }

    pub fn reset(&self) {
        self.operation_count.store(0, Ordering::Relaxed);
    }
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

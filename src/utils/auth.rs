use anyhow::Result;
use bcrypt::{hash, verify, DEFAULT_COST};
use hex;
use sha2::{Digest, Sha256};
use uuid::Uuid;

/// Generate a secure access token with very low collision probability
pub fn generate_access_token() -> String {
    // Combine multiple UUIDs to reduce collision probability
    let uuid1 = Uuid::new_v4();
    let uuid2 = Uuid::new_v4();

    // Create a hash of the combined UUIDs
    let combined = format!("{}{}", uuid1, uuid2);
    sha256_hash(&combined)[..32].to_string()
}

/// Hash a password using bcrypt
pub fn hash_password(password: &str) -> Result<String> {
    Ok(hash(password, DEFAULT_COST)?)
}

/// Verify a password against a hash
pub fn verify_password(password: &str, hash: &str) -> bool {
    verify(password, hash).unwrap_or(false)
}

/// Generate a SHA256 hash of input
pub fn sha256_hash(input: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(input.as_bytes());
    hex::encode(hasher.finalize())
}

/// Generate a device ID from device information
pub fn generate_device_id(device_name: &str, client: &str) -> String {
    let input = format!("{}-{}", device_name, client);
    sha256_hash(&input)[..16].to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_access_token() {
        let token = generate_access_token();
        assert_eq!(token.len(), 32); // UUID without dashes
        assert!(!token.contains('-'));
    }

    #[test]
    fn test_password_hashing() {
        let password = "test_password";
        let hash = hash_password(password).unwrap();

        assert!(verify_password(password, &hash));
        assert!(!verify_password("wrong_password", &hash));
    }

    #[test]
    fn test_sha256_hash() {
        let input = "test";
        let hash = sha256_hash(input);
        assert_eq!(hash.len(), 64); // SHA256 produces 64 character hex string

        // Same input should produce same hash
        assert_eq!(sha256_hash(input), hash);
    }

    #[test]
    fn test_generate_device_id() {
        let device_id = generate_device_id("iPhone", "Jellyfin Mobile");
        assert_eq!(device_id.len(), 16);

        // Same input should produce same device ID
        assert_eq!(generate_device_id("iPhone", "Jellyfin Mobile"), device_id);
    }
}

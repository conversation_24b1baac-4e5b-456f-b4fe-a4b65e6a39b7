use anyhow::Result;
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde::Deserialize;
use std::sync::Arc;
use tracing::{error, info};

use crate::{config::Config, server::AppState};

pub mod hls;
pub mod playback;
pub mod transcoding;

use playback::PlaybackEngine;
use transcoding::TranscodingEngine;

pub struct StreamingEngine {
    _config: Arc<Config>, // Reserved for future configuration options
    transcoding_engine: TranscodingEngine,
    playback_engine: PlaybackEngine,
}

impl StreamingEngine {
    pub fn new(config: Arc<Config>) -> Result<Self> {
        let transcoding_engine = TranscodingEngine::new(config.clone())?;
        let playback_engine = PlaybackEngine::new(config.clone());

        Ok(Self {
            _config: config,
            transcoding_engine,
            playback_engine,
        })
    }

    pub fn playback_engine(&self) -> &PlaybackEngine {
        &self.playback_engine
    }

    pub async fn start_transcoding(
        &self,
        media_item: &crate::database::models::MediaItem,
        request: transcoding::TranscodingRequest,
    ) -> Result<String> {
        self.transcoding_engine
            .start_transcoding(media_item, request)
            .await
    }

    pub async fn get_session(&self, session_id: &str) -> Option<transcoding::TranscodingSession> {
        self.transcoding_engine.get_session(session_id).await
    }

    pub async fn get_playlist(&self, session_id: &str) -> Result<String> {
        self.transcoding_engine.get_playlist(session_id).await
    }

    pub async fn get_segment(&self, session_id: &str, segment_name: &str) -> Result<Vec<u8>> {
        self.transcoding_engine
            .get_segment(session_id, segment_name)
            .await
    }

    pub async fn cleanup_expired_sessions(&self) {
        self.transcoding_engine.cleanup_expired_sessions().await;
        // Also cleanup playback sessions
        self.playback_engine
            .cleanup_inactive_sessions(
                std::time::Duration::from_secs(3600), // 1 hour
            )
            .await;
    }
}

#[derive(Debug, Deserialize)]
pub struct DirectStreamParams {
    #[serde(rename = "MediaSourceId")]
    pub media_source_id: Option<String>,
    #[serde(rename = "DeviceId")]
    pub device_id: Option<String>,
    #[serde(rename = "Container")]
    pub container: Option<String>,
}

pub async fn stream_video(
    State(state): State<AppState>,
    Path(item_id): Path<String>,
    Query(_params): Query<DirectStreamParams>,
) -> Result<Response, StatusCode> {
    info!("Direct streaming request for item: {}", item_id);

    // Get media item
    let media_item = match state
        .media_manager
        .library_manager()
        .get_media_item(&item_id)
        .await
    {
        Ok(Some(item)) => item,
        Ok(None) => return Err(StatusCode::NOT_FOUND),
        Err(e) => {
            error!("Database error: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // For direct streaming, serve the file directly
    match tokio::fs::File::open(&media_item.path).await {
        Ok(file) => {
            let stream = tokio_util::io::ReaderStream::new(file);
            let body = axum::body::Body::from_stream(stream);

            Ok((
                StatusCode::OK,
                [("Content-Type", "video/mp4"), ("Accept-Ranges", "bytes")],
                body,
            )
                .into_response())
        }
        Err(e) => {
            error!("Failed to open media file: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

pub async fn get_hls_playlist(
    State(state): State<AppState>,
    Path(item_id): Path<String>,
    Query(params): Query<hls::StreamingParams>,
) -> Result<Response, StatusCode> {
    hls::get_master_playlist(State(state), Path(item_id), Query(params)).await
}

pub async fn get_hls_segment(
    State(state): State<AppState>,
    Path((item_id, segment)): Path<(String, String)>,
) -> Result<Response, StatusCode> {
    // Parse segment name to extract profile and segment info
    if let Some((profile_name, segment_name)) = segment.split_once('_') {
        hls::get_segment(
            State(state),
            Path((item_id, profile_name.to_string(), segment_name.to_string())),
        )
        .await
    } else {
        Err(StatusCode::BAD_REQUEST)
    }
}

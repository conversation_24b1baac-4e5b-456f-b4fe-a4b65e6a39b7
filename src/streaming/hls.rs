use anyhow::Result;
use axum::{
    extract::{Path, Query, State},
    http::{header, StatusCode},
    response::{IntoResponse, Response},
};
use serde::Deserialize;
use tracing::{debug, error, warn};

use crate::{
    database::models::MediaItem,
    server::AppState,
    streaming::transcoding::{TranscodingProfile, TranscodingRequest},
};

#[derive(Debug, Deserialize)]
pub struct StreamingParams {
    #[serde(rename = "StartTimeTicks")]
    pub start_time_ticks: Option<u64>,
    #[serde(rename = "MaxStreamingBitrate")]
    pub max_streaming_bitrate: Option<u64>,
    #[serde(rename = "VideoCodec")]
    pub video_codec: Option<String>,
    #[serde(rename = "AudioCodec")]
    pub audio_codec: Option<String>,
    #[serde(rename = "MaxWidth")]
    pub max_width: Option<u32>,
    #[serde(rename = "MaxHeight")]
    pub max_height: Option<u32>,
    #[serde(rename = "MaxFramerate")]
    pub max_framerate: Option<f64>,
    #[serde(rename = "DeviceId")]
    pub device_id: Option<String>,
    #[serde(rename = "MediaSourceId")]
    pub media_source_id: Option<String>,
    #[serde(rename = "Container")]
    pub container: Option<String>,
}

pub async fn get_master_playlist(
    State(state): State<AppState>,
    Path(item_id): Path<String>,
    Query(params): Query<StreamingParams>,
) -> Result<Response, StatusCode> {
    debug!("Generating master playlist for item: {}", item_id);

    // Get media item from database
    let _media_item = match state
        .media_manager
        .library_manager()
        .get_media_item(&item_id)
        .await
    {
        Ok(Some(item)) => item,
        Ok(None) => {
            warn!("Media item not found: {}", item_id);
            return Err(StatusCode::NOT_FOUND);
        }
        Err(e) => {
            error!("Database error: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // Generate transcoding profiles based on request parameters
    let profiles = generate_transcoding_profiles(&params, &state);

    // Create master playlist content
    let playlist_content = create_master_playlist(&item_id, &profiles);

    Ok((
        StatusCode::OK,
        [(header::CONTENT_TYPE, "application/vnd.apple.mpegurl")],
        playlist_content,
    )
        .into_response())
}

pub async fn get_variant_playlist(
    State(state): State<AppState>,
    Path((item_id, profile_name)): Path<(String, String)>,
    Query(params): Query<StreamingParams>,
) -> Result<Response, StatusCode> {
    debug!(
        "Getting variant playlist for item: {}, profile: {}",
        item_id, profile_name
    );

    // Get media item
    let media_item = match state
        .media_manager
        .library_manager()
        .get_media_item(&item_id)
        .await
    {
        Ok(Some(item)) => item,
        Ok(None) => return Err(StatusCode::NOT_FOUND),
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };

    // Get or create transcoding session
    let session_id = match get_or_create_transcoding_session(
        &state,
        &media_item,
        &profile_name,
        &params,
    )
    .await
    {
        Ok(id) => id,
        Err(e) => {
            error!("Failed to create transcoding session: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // Get playlist from transcoding engine
    match state.streaming_engine.get_playlist(&session_id).await {
        Ok(playlist) => Ok((
            StatusCode::OK,
            [(header::CONTENT_TYPE, "application/vnd.apple.mpegurl")],
            playlist,
        )
            .into_response()),
        Err(e) => {
            if e.to_string().contains("not ready") {
                // Playlist is still being generated
                Ok((
                    StatusCode::ACCEPTED,
                    [(header::CONTENT_TYPE, "application/vnd.apple.mpegurl")],
                    "#EXTM3U\n#EXT-X-VERSION:3\n#EXT-X-TARGETDURATION:10\n",
                )
                    .into_response())
            } else {
                error!("Failed to get playlist: {}", e);
                Err(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
}

pub async fn get_segment(
    State(state): State<AppState>,
    Path((item_id, profile_name, segment_name)): Path<(String, String, String)>,
) -> Result<Response, StatusCode> {
    debug!(
        "Getting segment: {} for item: {}, profile: {}",
        segment_name, item_id, profile_name
    );

    // Find transcoding session
    let session_id = format!("{}_{}", item_id, profile_name);

    match state
        .streaming_engine
        .get_segment(&session_id, &segment_name)
        .await
    {
        Ok(segment_data) => Ok((
            StatusCode::OK,
            [
                (header::CONTENT_TYPE, "video/mp2t"),
                (header::CACHE_CONTROL, "public, max-age=31536000"),
            ],
            segment_data,
        )
            .into_response()),
        Err(e) => {
            error!("Failed to get segment: {}", e);
            Err(StatusCode::NOT_FOUND)
        }
    }
}

fn generate_transcoding_profiles(
    params: &StreamingParams,
    state: &AppState,
) -> Vec<TranscodingProfile> {
    let mut profiles = Vec::new();

    // Get default profiles
    let default_profiles = crate::streaming::transcoding::TranscodingEngine::get_default_profiles();

    // Filter profiles based on device capabilities and request parameters
    let max_bitrate = params
        .max_streaming_bitrate
        .unwrap_or(state.config.streaming.max_bitrate);

    for profile in default_profiles {
        if profile.max_bitrate <= max_bitrate {
            let mut custom_profile = profile.clone();

            // Apply custom parameters
            if let Some(max_width) = params.max_width {
                custom_profile.max_width = Some(std::cmp::min(
                    custom_profile.max_width.unwrap_or(max_width),
                    max_width,
                ));
            }

            if let Some(max_height) = params.max_height {
                custom_profile.max_height = Some(std::cmp::min(
                    custom_profile.max_height.unwrap_or(max_height),
                    max_height,
                ));
            }

            if let Some(max_framerate) = params.max_framerate {
                custom_profile.max_framerate = Some(
                    custom_profile
                        .max_framerate
                        .map(|f| f.min(max_framerate))
                        .unwrap_or(max_framerate),
                );
            }

            // Override codecs if specified
            if let Some(ref video_codec) = params.video_codec {
                custom_profile.video_codec = video_codec.clone();
            }

            if let Some(ref audio_codec) = params.audio_codec {
                custom_profile.audio_codec = audio_codec.clone();
            }

            profiles.push(custom_profile);
        }
    }

    // If no profiles match, create a basic one
    if profiles.is_empty() {
        profiles.push(TranscodingProfile {
            name: "Basic".to_string(),
            container: "mp4".to_string(),
            video_codec: "libx264".to_string(),
            audio_codec: "aac".to_string(),
            max_bitrate: std::cmp::min(max_bitrate, 2_000_000),
            max_width: params.max_width.or(Some(854)),
            max_height: params.max_height.or(Some(480)),
            max_framerate: params.max_framerate.or(Some(30.0)),
        });
    }

    profiles
}

fn create_master_playlist(item_id: &str, profiles: &[TranscodingProfile]) -> String {
    let mut playlist = String::from("#EXTM3U\n#EXT-X-VERSION:6\n\n");

    for profile in profiles {
        // Calculate bandwidth (bitrate + audio overhead)
        let bandwidth = profile.max_bitrate + 128_000; // Add 128k for audio

        // Add stream info
        playlist.push_str(&format!(
            "#EXT-X-STREAM-INF:BANDWIDTH={},RESOLUTION={}x{},CODECS=\"{},{}\",NAME=\"{}\"\n",
            bandwidth,
            profile.max_width.unwrap_or(1920),
            profile.max_height.unwrap_or(1080),
            get_codec_string(&profile.video_codec),
            get_codec_string(&profile.audio_codec),
            profile.name
        ));

        // Add playlist URL
        playlist.push_str(&format!(
            "variant_{}_{}.m3u8\n\n",
            item_id,
            profile.name.to_lowercase()
        ));
    }

    playlist
}

fn get_codec_string(codec: &str) -> &str {
    match codec {
        "libx264" => "avc1.640028",
        "libx265" => "hev1.1.6.L93.B0",
        "aac" => "mp4a.40.2",
        "mp3" => "mp4a.40.34",
        _ => codec,
    }
}

async fn get_or_create_transcoding_session(
    state: &AppState,
    media_item: &MediaItem,
    profile_name: &str,
    params: &StreamingParams,
) -> Result<String> {
    let session_id = format!("{}_{}", media_item.id, profile_name);

    // Check if session already exists
    if state
        .streaming_engine
        .get_session(&session_id)
        .await
        .is_some()
    {
        return Ok(session_id);
    }

    // Create transcoding profile
    let profile = create_profile_from_params(profile_name, params, state);

    // Calculate start time from ticks (100ns units)
    let start_time = params
        .start_time_ticks
        .map(|ticks| ticks as f64 / 10_000_000.0); // Convert to seconds

    let request = TranscodingRequest {
        item_id: media_item.id.clone(),
        profile,
        start_time,
        duration: None, // Stream entire file
        segment_duration: state.config.streaming.segment_duration,
    };

    // Start transcoding
    state
        .streaming_engine
        .start_transcoding(media_item, request)
        .await
}

fn create_profile_from_params(
    profile_name: &str,
    params: &StreamingParams,
    _state: &AppState,
) -> TranscodingProfile {
    let default_profiles = crate::streaming::transcoding::TranscodingEngine::get_default_profiles();

    // Find matching default profile or create custom one
    let base_profile = default_profiles
        .iter()
        .find(|p| p.name.to_lowercase() == profile_name.to_lowercase())
        .cloned()
        .unwrap_or_else(|| TranscodingProfile {
            name: profile_name.to_string(),
            container: "mp4".to_string(),
            video_codec: "libx264".to_string(),
            audio_codec: "aac".to_string(),
            max_bitrate: 4_000_000,
            max_width: Some(1280),
            max_height: Some(720),
            max_framerate: Some(30.0),
        });

    let mut profile = base_profile;

    // Apply parameter overrides
    if let Some(max_bitrate) = params.max_streaming_bitrate {
        profile.max_bitrate = std::cmp::min(profile.max_bitrate, max_bitrate);
    }

    if let Some(max_width) = params.max_width {
        profile.max_width = Some(max_width);
    }

    if let Some(max_height) = params.max_height {
        profile.max_height = Some(max_height);
    }

    if let Some(max_framerate) = params.max_framerate {
        profile.max_framerate = Some(max_framerate);
    }

    if let Some(ref video_codec) = params.video_codec {
        profile.video_codec = video_codec.clone();
    }

    if let Some(ref audio_codec) = params.audio_codec {
        profile.audio_codec = audio_codec.clone();
    }

    if let Some(ref container) = params.container {
        profile.container = container.clone();
    }

    profile
}

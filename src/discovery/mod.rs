use anyhow::Result;
use serde_json::json;
use std::{net::SocketAddr, sync::Arc, time::Duration};
use tokio::{net::UdpSocket, time::interval};
use tracing::{debug, error, info, warn};

use crate::config::Config;

pub struct DiscoveryService {
    config: Arc<Config>,
    _socket: Option<UdpSocket>, // Reserved for future use
}

impl DiscoveryService {
    pub fn new(config: Arc<Config>) -> Result<Self> {
        Ok(Self {
            config,
            _socket: None,
        })
    }

    pub async fn start(&self) -> Result<()> {
        info!("Starting Jellyfin-compatible discovery service on UDP port 7359...");

        // Bind to discovery port
        let socket = UdpSocket::bind("0.0.0.0:7359").await?;
        info!("Discovery service listening on UDP port 7359");

        // Start discovery response handler
        let config = self.config.clone();
        let socket_arc = Arc::new(socket);
        let socket_clone = socket_arc.clone();

        tokio::spawn(async move {
            let mut buf = [0; 1024];

            loop {
                match socket_clone.recv_from(&mut buf).await {
                    Ok((len, addr)) => {
                        let message = String::from_utf8_lossy(&buf[..len]);
                        debug!("Received discovery request from {}: {}", addr, message);

                        if let Err(e) =
                            handle_discovery_request(&socket_clone, &config, &message, addr).await
                        {
                            error!("Failed to handle discovery request: {}", e);
                        }
                    }
                    Err(e) => {
                        error!("Discovery socket error: {}", e);
                        tokio::time::sleep(Duration::from_secs(1)).await;
                    }
                }
            }
        });

        // Start periodic announcements
        let config_clone = self.config.clone();
        let socket_announce = socket_arc.clone();

        tokio::spawn(async move {
            let mut announce_interval = interval(Duration::from_secs(30));

            loop {
                announce_interval.tick().await;

                if let Err(e) = send_announcement(&socket_announce, &config_clone).await {
                    warn!("Failed to send discovery announcement: {}", e);
                }
            }
        });

        Ok(())
    }
}

async fn handle_discovery_request(
    socket: &Arc<UdpSocket>,
    config: &Config,
    message: &str,
    addr: SocketAddr,
) -> Result<()> {
    // Handle Jellyfin discovery requests
    if message.trim() == "who is JellyfinServer?" {
        let response = create_jellyfin_discovery_response(config)?;
        socket.send_to(response.as_bytes(), addr).await?;
        debug!("Sent Jellyfin discovery response to {}", addr);
    }
    // Handle DLNA/UPnP discovery requests
    else if message.contains("M-SEARCH") && message.contains("upnp:rootdevice") {
        let response = create_upnp_discovery_response(config)?;
        socket.send_to(response.as_bytes(), addr).await?;
        debug!("Sent UPnP discovery response to {}", addr);
    }

    Ok(())
}

fn create_jellyfin_discovery_response(config: &Config) -> Result<String> {
    let server_info = json!({
        "Address": get_local_ip(),
        "Id": uuid::Uuid::new_v4().to_string(),
        "Name": config.server.server_name,
        "Version": env!("CARGO_PKG_VERSION"),
        "ProductName": "Tulip Media Server",
        "OperatingSystem": "Linux",
        "Architecture": "ARM64"
    });

    Ok(server_info.to_string())
}

fn create_upnp_discovery_response(config: &Config) -> Result<String> {
    let local_ip = get_local_ip();
    let server_url = format!("http://{}:{}", local_ip, config.server.port);

    let response = format!(
        "HTTP/1.1 200 OK\r\n\
         CACHE-CONTROL: max-age=1800\r\n\
         DATE: {}\r\n\
         EXT:\r\n\
         LOCATION: {}/description.xml\r\n\
         OPT: \"http://schemas.upnp.org/upnp/1/0/\"; ns=01\r\n\
         01-NLS: 1\r\n\
         SERVER: Linux/5.0 UPnP/1.0 Tulip Media Server/{}\r\n\
         ST: upnp:rootdevice\r\n\
         USN: uuid:{}::upnp:rootdevice\r\n\
         BOOTID.UPNP.ORG: 1\r\n\
         CONFIGID.UPNP.ORG: 1\r\n\r\n",
        chrono::Utc::now().format("%a, %d %b %Y %H:%M:%S GMT"),
        server_url,
        env!("CARGO_PKG_VERSION"),
        uuid::Uuid::new_v4()
    );

    Ok(response)
}

async fn send_announcement(socket: &Arc<UdpSocket>, config: &Config) -> Result<()> {
    let broadcast_addr = "***************:7359";

    // Send Jellyfin announcement
    let jellyfin_announcement = json!({
        "Type": "ServerAnnouncement",
        "Server": {
            "Id": uuid::Uuid::new_v4().to_string(),
            "Name": config.server.server_name,
            "Version": env!("CARGO_PKG_VERSION"),
            "ProductName": "Tulip Media Server",
            "Address": get_local_ip(),
            "Port": config.server.port,
            "OperatingSystem": "Linux",
            "Architecture": "ARM64"
        }
    });

    socket
        .send_to(jellyfin_announcement.to_string().as_bytes(), broadcast_addr)
        .await?;

    debug!("Sent discovery announcement");
    Ok(())
}

fn get_local_ip() -> String {
    // Try to get the local IP address
    match local_ip_address::local_ip() {
        Ok(ip) => ip.to_string(),
        Err(_) => {
            // Fallback to localhost
            "127.0.0.1".to_string()
        }
    }
}

// Clean, minimal test fixture helpers (single copy) to restore crate compilation.
use crate::database::models::{Library, MediaItem, PlaybackSession, User};
use anyhow::Result;
use chrono::Utc;
use sqlx::{Pool, Sqlite};
use uuid::Uuid;

pub async fn create_test_library(
    pool: &Pool<Sqlite>,
    name: &str,
    path: &str,
    library_type: &str,
) -> Result<Library> {
    let library = Library::new(name.to_string(), path.to_string(), library_type.to_string());

    sqlx::query(
        r#"INSERT INTO libraries (id, name, path, library_type, created_at, updated_at, last_scan)
        VALUES (?, ?, ?, ?, ?, ?, ?)"#,
    )
    .bind(&library.id)
    .bind(&library.name)
    .bind(&library.path)
    .bind(&library.library_type)
    .bind(library.created_at.to_rfc3339())
    .bind(library.updated_at.to_rfc3339())
    .bind(library.last_scan.map(|d| d.to_rfc3339()))
    .execute(pool)
    .await?;

    Ok(library)
}

pub async fn create_test_user(
    pool: &Pool<Sqlite>,
    name: &str,
    password: Option<&str>,
    is_admin: bool,
) -> Result<User> {
    let user = User::new(name.to_string(), password.map(|p| p.to_string()), is_admin);

    sqlx::query(
        "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
    )
    .bind(&user.id)
    .bind(&user.name)
    .bind(&user.password_hash)
    .bind(user.is_administrator)
    .bind(user.is_hidden)
    .bind(user.is_disabled)
    .bind(user.created_at.to_rfc3339())
    .bind(user.updated_at.to_rfc3339())
    .execute(pool)
    .await?;

    Ok(user)
}

pub async fn create_test_media_item(
    pool: &Pool<Sqlite>,
    library: &Library,
    parent_id: Option<String>,
    name: &str,
    path: &str,
    item_type: &str,
    media_type: &str,
) -> Result<MediaItem> {
    // Ensure library exists; create it if missing
    let library_exists = sqlx::query("SELECT 1 FROM libraries WHERE id = ?")
        .bind(&library.id)
        .fetch_optional(pool)
        .await?
        .is_some();

    if !library_exists {
        // Insert the library so FK constraint is satisfied
        sqlx::query(
            r#"INSERT INTO libraries (id, name, path, library_type, created_at, updated_at, last_scan)
            VALUES (?, ?, ?, ?, ?, ?, ?)"#,
        )
        .bind(&library.id)
        .bind(&library.name)
        .bind(&library.path)
        .bind(&library.library_type)
        .bind(library.created_at.to_rfc3339())
        .bind(library.updated_at.to_rfc3339())
        .bind(library.last_scan.map(|d| d.to_rfc3339()))
        .execute(pool)
        .await?;
    }

    if let Some(parent) = &parent_id {
        let parent_exists = sqlx::query("SELECT 1 FROM media_items WHERE id = ?")
            .bind(parent)
            .fetch_optional(pool)
            .await?
            .is_some();

        if !parent_exists {
            anyhow::bail!(
                "Parent media item {} does not exist in the database",
                parent
            );
        }
    }

    let item = MediaItem::new(
        library.id.clone(),
        parent_id,
        name.to_string(),
        path.to_string(),
        item_type.to_string(),
        media_type.to_string(),
        1024 * 1024 * 100,
    );

    sqlx::query(
        r#"INSERT INTO media_items (
            id, library_id, parent_id, name, sort_name, path, item_type, media_type,
            file_size, duration, bitrate, container, video_codec, audio_codec,
            width, height, aspect_ratio, framerate,
            created_at, updated_at, date_added, date_modified
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"#,
    )
    .bind(&item.id)
    .bind(&item.library_id)
    .bind(&item.parent_id)
    .bind(&item.name)
    .bind(&item.sort_name)
    .bind(&item.path)
    .bind(&item.item_type)
    .bind(&item.media_type)
    .bind(item.file_size)
    .bind(item.duration)
    .bind(item.bitrate)
    .bind(&item.container)
    .bind(&item.video_codec)
    .bind(&item.audio_codec)
    .bind(item.width)
    .bind(item.height)
    .bind(&item.aspect_ratio)
    .bind(item.framerate)
    .bind(item.created_at.to_rfc3339())
    .bind(item.updated_at.to_rfc3339())
    .bind(item.date_added.to_rfc3339())
    .bind(item.date_modified.to_rfc3339())
    .execute(pool)
    .await?;

    Ok(item)
}

pub async fn create_test_media_item_with_dependencies(
    pool: &Pool<Sqlite>,
    library_name: &str,
    library_path: &str,
    library_type: &str,
    media_name: &str,
    media_path: &str,
    item_type: &str,
    media_type: &str,
) -> Result<MediaItem> {
    let library = create_test_library(pool, library_name, library_path, library_type).await?;
    let item = create_test_media_item(
        pool, &library, None, media_name, media_path, item_type, media_type,
    )
    .await?;

    Ok(item)
}

pub async fn create_test_playback_session(
    pool: &Pool<Sqlite>,
    user: &User,
    item: &MediaItem,
    position: i64,
) -> Result<PlaybackSession> {
    let session = PlaybackSession {
        id: Uuid::new_v4().to_string(),
        user_id: user.id.clone(),
        item_id: item.id.clone(),
        session_id: Uuid::new_v4().to_string(),
        device_id: "test-device".to_string(),
        position_ticks: position,
        is_paused: false,
        volume_level: Some(80),
        is_muted: false,
        subtitle_stream_index: Some(0),
        audio_stream_index: Some(0),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };

    sqlx::query(
        r#"INSERT INTO playback_sessions (
            id, user_id, item_id, session_id, device_id, position_ticks,
            is_paused, volume_level, is_muted, subtitle_stream_index,
            audio_stream_index, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"#,
    )
    .bind(&session.id)
    .bind(&session.user_id)
    .bind(&session.item_id)
    .bind(&session.session_id)
    .bind(&session.device_id)
    .bind(&session.position_ticks)
    .bind(&session.is_paused)
    .bind(&session.volume_level)
    .bind(&session.is_muted)
    .bind(&session.subtitle_stream_index)
    .bind(&session.audio_stream_index)
    .bind(&session.created_at.to_rfc3339())
    .bind(&session.updated_at.to_rfc3339())
    .execute(pool)
    .await?;

    Ok(session)
}

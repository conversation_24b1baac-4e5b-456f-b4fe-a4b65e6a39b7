use crate::{
    config::Config, database::Database, media::MediaManager, server::AppState,
    streaming::StreamingEngine,
};
use anyhow::Result;
use sqlx::{Pool, Sqlite};
use std::{future::Future, sync::Arc};
use tokio::sync::Semaphore;
use uuid::Uuid;

/// Test utilities that help manage database connections and concurrency
#[derive(Clone)]
pub struct TestUtils {
    // Limit concurrent operations for Nano Pi resources
    connection_semaphore: Arc<Semaphore>,
}

impl TestUtils {
    pub fn new() -> Self {
        Self {
            connection_semaphore: Arc::new(Semaphore::new(5)), // Max 5 concurrent operations
        }
    }

    /// Run a database operation with controlled concurrency
    pub async fn run_db_operation<F, T>(&self, operation: F) -> Result<T>
    where
        F: Future<Output = Result<T>>,
    {
        let _permit = self.connection_semaphore.acquire().await?;
        operation.await
    }
}

/// Creates a test app state with an in-memory database
pub async fn create_test_app_state() -> AppState {
    let mut config = Config::default();
    config.database.path = std::path::PathBuf::from(format!(
        "file:test_{}?mode=memory&cache=shared",
        Uuid::new_v4()
    ));
    config.database.max_connections = 10;

    let database = Database::new(&config.database).await.unwrap();

    // Run migrations and validate tables
    setup_test_db(&database.pool()).await.unwrap();
    validate_test_db(&database.pool()).await.unwrap();

    let config_arc = Arc::new(config);

    let media_manager = MediaManager::new(config_arc.clone(), database.clone())
        .await
        .unwrap();

    let streaming_engine = StreamingEngine::new(config_arc.clone()).unwrap();

    AppState {
        config: config_arc,
        database,
        media_manager: Arc::new(media_manager),
        streaming_engine: Arc::new(streaming_engine),
    }
}

/// Validate that all required tables exist and have proper structure
async fn validate_test_db(pool: &Pool<Sqlite>) -> Result<()> {
    let required_tables = [
        "users",
        "sessions",
        "libraries",
        "media_items",
        "playback_sessions",
    ];

    for table in required_tables.iter() {
        let exists = sqlx::query("SELECT name FROM sqlite_master WHERE type='table' AND name = ?")
            .bind(table)
            .fetch_optional(pool)
            .await?
            .is_some();

        if !exists {
            anyhow::bail!("Required table {} is missing", table);
        }
    }

    Ok(())
}

/// Setup the test database tables and indexes
pub async fn setup_test_db(pool: &Pool<Sqlite>) -> Result<()> {
    // Enable foreign key support
    sqlx::query("PRAGMA foreign_keys = ON;")
        .execute(pool)
        .await?;

    // Create tables in proper order to respect foreign key constraints
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            password_hash TEXT,
            is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
            is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
            is_disabled BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_login_date TEXT,
            last_activity_date TEXT
        );"#,
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            device_name TEXT NOT NULL,
            client TEXT NOT NULL,
            version TEXT NOT NULL,
            access_token TEXT NOT NULL UNIQUE,
            created_at TEXT NOT NULL,
            last_activity TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        );"#,
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS libraries (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            library_type TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_scan TEXT
        );"#,
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_items (
            id TEXT PRIMARY KEY,
            library_id TEXT NOT NULL,
            parent_id TEXT,
            name TEXT NOT NULL,
            sort_name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            item_type TEXT NOT NULL,
            media_type TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            duration INTEGER,
            bitrate INTEGER,
            container TEXT,
            video_codec TEXT,
            audio_codec TEXT,
            width INTEGER,
            height INTEGER,
            aspect_ratio TEXT,
            framerate REAL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            date_added TEXT NOT NULL,
            date_modified TEXT NOT NULL,
            FOREIGN KEY (library_id) REFERENCES libraries(id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES media_items(id) ON DELETE CASCADE
        );"#,
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS playback_sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            item_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            position_ticks INTEGER NOT NULL,
            is_paused BOOLEAN NOT NULL DEFAULT FALSE,
            volume_level INTEGER,
            is_muted BOOLEAN NOT NULL DEFAULT FALSE,
            subtitle_stream_index INTEGER,
            audio_stream_index INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (item_id) REFERENCES media_items(id) ON DELETE CASCADE
        );"#,
    )
    .execute(pool)
    .await?;

    // Create indexes for better performance
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);")
        .execute(pool)
        .await?;

    sqlx::query(
        "CREATE INDEX IF NOT EXISTS idx_media_items_library_id ON media_items(library_id);",
    )
    .execute(pool)
    .await?;

    sqlx::query(
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_user_id ON playback_sessions(user_id);",
    )
    .execute(pool)
    .await?;

    sqlx::query("CREATE INDEX IF NOT EXISTS idx_media_items_parent_id ON media_items(parent_id);")
        .execute(pool)
        .await?;

    sqlx::query(
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_item_id ON playback_sessions(item_id);",
    )
    .execute(pool)
    .await?;

    Ok(())
}

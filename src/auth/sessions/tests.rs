#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{TestUtils, create_test_app_state};
    use anyhow::Result;

    #[tokio::test]
    async fn test_session_management() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();

        // Test listing empty sessions
        let result = test_utils.run_db_operation(async {
            list_sessions(State(state.clone())).await
        }).await?;

        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string())?;
        assert_eq!(data["Items"].as_array().unwrap().len(), 0);

        Ok(())
    }

    #[tokio::test]
    async fn test_concurrent_session_access() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();
        let mut headers = HeaderMap::new();
        headers.insert("X-Emby-Session", "test-session".parse().unwrap());

        // Insert a test session
        test_utils.run_db_operation(async {
            sqlx::query(
                r#"
                INSERT INTO sessions 
                (id, user_id, device_id, device_name, client, version, access_token, created_at, last_activity, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                "#
            )
            .bind("test-session")
            .bind("test-user")
            .bind("test-device")
            .bind("Test Device")
            .bind("Test Client")
            .bind("1.0.0")
            .bind("test-token")
            .bind(chrono::Utc::now().to_rfc3339())
            .bind(chrono::Utc::now().to_rfc3339())
            .bind(true)
            .execute(state.database.pool())
            .await?;
            Ok::<_, anyhow::Error>(())
        }).await?;

        // Test concurrent session commands
        let mut handles = vec![];
        for i in 0..3 {
            let state_clone = state.clone();
            let test_utils = test_utils.clone();
            let headers = headers.clone();
            
            let handle = tokio::spawn(async move {
                tokio::time::sleep(std::time::Duration::from_millis(10)).await;
                test_utils.run_db_operation(async {
                    let result = session_command(
                        State(state_clone),
                        Path("test-session".to_string()),
                        headers,
                        Json(json!({
                            "Command": if i % 2 == 0 { "play" } else { "stop" }
                        }))
                    ).await;
                    assert!(result.is_ok());
                    Ok(())
                }).await.unwrap();
            });
            
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }

        Ok(())
    }
}

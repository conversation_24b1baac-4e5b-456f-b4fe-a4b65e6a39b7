use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::J<PERSON>,
};
use serde_json::{json, Value};
// tracing::info is intentionally unused in the auth module for now
// use tracing::info;

use crate::server::AppState;

pub mod jwt;
pub mod middleware;
pub mod playback;
pub mod sessions;
pub mod types;

#[cfg(test)]
mod tests;

pub use types::AuthenticateByNameRequest;

pub async fn authenticate_by_name(
    _state: State<AppState>,
    Json(request): <PERSON><PERSON><AuthenticateByNameRequest>,
) -> Result<Json<Value>, StatusCode> {
    // Return a mock response for now
    Ok(Json(json!({
        "User": {
            "Id": "mock-user-id",
            "Name": request.username,
        },
        "SessionInfo": {
            "Id": "mock-session-id",
            "UserId": "mock-user-id",
        },
        "AccessToken": "mock-token",
        "ServerId": "mock-server-id"
    })))
}

pub async fn authenticate_quick_connect(
    _state: State<AppState>,
    J<PERSON>(_request): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    Err(StatusCode::NOT_IMPLEMENTED)
}

pub async fn logout(_state: State<AppState>, _headers: HeaderMap) -> Result<(), StatusCode> {
    // Mock implementation
    Ok(())
}

pub async fn list_sessions(_state: State<AppState>) -> Result<Json<Value>, StatusCode> {
    Ok(Json(json!({
        "Items": [],
    })))
}

pub async fn playing_start(
    _state: State<AppState>,
    _headers: HeaderMap,
    Json(_request): Json<Value>,
) -> Result<(), StatusCode> {
    Ok(())
}

pub async fn playing_progress(
    _state: State<AppState>,
    _headers: HeaderMap,
    Json(_request): Json<Value>,
) -> Result<(), StatusCode> {
    Ok(())
}

pub async fn playing_stopped(
    _state: State<AppState>,
    _headers: HeaderMap,
    Json(_request): Json<Value>,
) -> Result<(), StatusCode> {
    Ok(())
}

pub async fn session_command(
    _state: State<AppState>,
    Path(_session_id): Path<String>,
    _headers: HeaderMap,
    Json(_request): Json<Value>,
) -> Result<(), StatusCode> {
    Ok(())
}

#[cfg(test)]
mod tests {
    // `super` imports are unused; keep tests explicit with necessary imports below.
    use crate::{
        auth::{authenticate_by_name, AuthenticateByNameRequest},
        database::models::User,
        test_utils::test_utils::{create_test_app_state, TestUtils},
    };
    use anyhow::Result;
    use axum::{extract::State, Json};
    use std::time::Duration;
    use tokio::time::sleep;

    #[tokio::test]
    async fn test_concurrent_authentication() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();
        let mut users = Vec::new();

        // Create users first, using a single transaction
        test_utils.run_db_operation(async {
            let mut tx = state.database.pool().begin().await?;
            // Only create 3 users for realistic testing on Nano Pi
            for i in 0..3 {
                let username = format!("concurrent_user_{}_{}", i, uuid::Uuid::new_v4());
                let password = format!("password_{}", i);
                let user = User::new(username.clone(), Some(password.clone()), false);

                sqlx::query(
                    "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(&user.id)
                .bind(&user.name)
                .bind(&user.password_hash)
                .bind(user.is_administrator)
                .bind(user.is_hidden)
                .bind(user.is_disabled)
                .bind(user.created_at.to_rfc3339())
                .bind(user.updated_at.to_rfc3339())
                .execute(&mut *tx)
                .await?;

                users.push((username, password));
                }

            tx.commit().await?;
            Ok::<_, anyhow::Error>(())
        }).await.unwrap();

        // Test concurrent authentication
        let mut handles = vec![];
        for (username, password) in users {
            let state_clone = state.clone();
            let test_utils = test_utils.clone();

            let handle = tokio::spawn(async move {
                sleep(Duration::from_millis(10)).await; // Small delay to simulate real world
                test_utils
                    .run_db_operation(async {
                        let result = authenticate_by_name(
                            State(state_clone),
                            Json(AuthenticateByNameRequest {
                                username,
                                password,
                                password_alt: None,
                            }),
                        )
                        .await;
                        assert!(result.is_ok());
                        Ok(())
                    })
                    .await
                    .unwrap();
            });

            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }

        Ok(())
    }

    #[tokio::test]
    async fn test_performance_under_load() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();

        // Create a test user
        let username = format!("perf_test_user_{}", uuid::Uuid::new_v4());
        let password = "testpass".to_string();

        test_utils.run_db_operation(async {
            let user = User::new(username.clone(), Some(password.clone()), false);
            sqlx::query(
                "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
            )
            .bind(&user.id)
            .bind(&user.name)
            .bind(&user.password_hash)
            .bind(user.is_administrator)
            .bind(user.is_hidden)
            .bind(user.is_disabled)
            .bind(user.created_at.to_rfc3339())
            .bind(user.updated_at.to_rfc3339())
            .execute(state.database.pool())
            .await?;
            Ok(())
        }).await.unwrap();

        // Test performance under load
        let mut handles = vec![];
        for _ in 0..5 {
            // Only 5 concurrent requests for Nano Pi
            let state_clone = state.clone();
            let username = username.clone();
            let password = password.clone();
            let test_utils = test_utils.clone();

            let handle = tokio::spawn(async move {
                sleep(Duration::from_millis(10)).await;
                test_utils
                    .run_db_operation(async {
                        let result = authenticate_by_name(
                            State(state_clone),
                            Json(AuthenticateByNameRequest {
                                username: username.clone(),
                                password: password.clone(),
                                password_alt: None,
                            }),
                        )
                        .await;
                        assert!(result.is_ok());
                        Ok(())
                    })
                    .await
                    .unwrap();
            });

            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }

        Ok(())
    }
}

//! Session management and playback state handling
use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::J<PERSON>,
};
use serde_json::{json, Value};
use tracing::info;

use crate::{database::models::Session, server::AppState};

pub async fn list_sessions(State(state): State<AppState>) -> Result<Json<Value>, StatusCode> {
    let sessions = sqlx::query_as::<_, Session>("SELECT * FROM sessions WHERE is_active = true")
        .fetch_all(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(json!({
        "Items": sessions.iter().map(|s| json!({
            "Id": s.id,
            "UserId": s.user_id,
            "DeviceId": s.device_id,
            "DeviceName": s.device_name,
            "Client": s.client,
            "LastActivityDate": s.last_activity,
        })).collect::<Vec<_>>(),
    })))
}

pub async fn session_command(
    State(state): State<AppState>,
    Path(session_id): Path<String>,
    _headers: HeaderMap,
    Json(request): Json<Value>,
) -> Result<(), StatusCode> {
    // Validate session exists and is active
    let _session =
        sqlx::query_as::<_, Session>("SELECT * FROM sessions WHERE id = ? AND is_active = true")
            .bind(&session_id)
            .fetch_optional(state.database.pool())
            .await
            .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?
            .ok_or(StatusCode::NOT_FOUND)?;

    // Extract command from request
    let command = request["Command"].as_str().unwrap_or("").to_lowercase();

    info!(
        "Session command received: {} for session {}",
        command, session_id
    );

    match command.as_str() {
        "play" => {
            // Update session state
            sqlx::query("UPDATE sessions SET last_activity = ? WHERE id = ?")
                .bind(chrono::Utc::now().to_rfc3339())
                .bind(&session_id)
                .execute(state.database.pool())
                .await
                .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        }
        "stop" => {
            // Update session state
            sqlx::query("UPDATE sessions SET last_activity = ? WHERE id = ?")
                .bind(chrono::Utc::now().to_rfc3339())
                .bind(&session_id)
                .execute(state.database.pool())
                .await
                .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        }
        _ => return Err(StatusCode::BAD_REQUEST),
    }

    Ok(())
}

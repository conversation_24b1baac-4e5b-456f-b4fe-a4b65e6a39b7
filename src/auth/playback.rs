//! Playback state management
use axum::{
    extract::State,
    http::{HeaderMap, StatusCode},
    response::<PERSON><PERSON>,
};
use serde_json::Value;
use tracing::info;

use crate::server::AppState;

pub async fn playing_start(
    State(state): State<AppState>,
    headers: <PERSON>erMap,
    Json(request): Json<Value>,
) -> Result<(), StatusCode> {
    let session_id = extract_session_id(&headers)?;
    let item_id = request["ItemId"].as_str().ok_or(StatusCode::BAD_REQUEST)?;

    info!(
        "Starting playback for item {} in session {}",
        item_id, session_id
    );

    // Update playback state
    sqlx::query(
        r#"
        INSERT INTO playback_sessions 
        (id, session_id, item_id, position_ticks, is_paused, created_at, updated_at)
        VALUES (?, ?, ?, 0, false, ?, ?)
        ON CONFLICT(session_id, item_id) DO UPDATE SET
        position_ticks = 0,
        is_paused = false,
        updated_at = ?
        "#,
    )
    .bind(uuid::Uuid::new_v4().to_string())
    .bind(&session_id)
    .bind(item_id)
    .bind(chrono::Utc::now().to_rfc3339())
    .bind(chrono::Utc::now().to_rfc3339())
    .bind(chrono::Utc::now().to_rfc3339())
    .execute(state.database.pool())
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(())
}

pub async fn playing_progress(
    State(state): State<AppState>,
    headers: HeaderMap,
    Json(request): Json<Value>,
) -> Result<(), StatusCode> {
    let session_id = extract_session_id(&headers)?;
    let item_id = request["ItemId"].as_str().ok_or(StatusCode::BAD_REQUEST)?;
    let position_ticks = request["PositionTicks"].as_i64().unwrap_or(0);
    let is_paused = request["IsPaused"].as_bool().unwrap_or(false);

    info!(
        "Updating playback progress for item {} in session {}",
        item_id, session_id
    );

    // Update playback state
    sqlx::query(
        r#"
        UPDATE playback_sessions 
        SET position_ticks = ?,
            is_paused = ?,
            updated_at = ?
        WHERE session_id = ? AND item_id = ?
        "#,
    )
    .bind(position_ticks)
    .bind(is_paused)
    .bind(chrono::Utc::now().to_rfc3339())
    .bind(&session_id)
    .bind(item_id)
    .execute(state.database.pool())
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(())
}

pub async fn playing_stopped(
    State(state): State<AppState>,
    headers: HeaderMap,
    Json(request): Json<Value>,
) -> Result<(), StatusCode> {
    let session_id = extract_session_id(&headers)?;
    let item_id = request["ItemId"].as_str().ok_or(StatusCode::BAD_REQUEST)?;

    info!(
        "Stopping playback for item {} in session {}",
        item_id, session_id
    );

    // Update playback state
    sqlx::query("DELETE FROM playback_sessions WHERE session_id = ? AND item_id = ?")
        .bind(&session_id)
        .bind(item_id)
        .execute(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(())
}

fn extract_session_id(headers: &HeaderMap) -> Result<String, StatusCode> {
    headers
        .get("X-Emby-Session")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.to_string())
        .ok_or(StatusCode::UNAUTHORIZED)
}

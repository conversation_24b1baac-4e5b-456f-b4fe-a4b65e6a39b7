#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{TestUtils, create_test_app_state};
    use anyhow::Result;

    #[tokio::test]
    async fn test_playback_lifecycle() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();
        let mut headers = HeaderMap::new();
        headers.insert("X-Emby-Session", "test-session".parse().unwrap());

        // Test play start
        let result = test_utils.run_db_operation(async {
            playing_start(
                State(state.clone()),
                headers.clone(),
                Json(json!({
                    "ItemId": "test-item",
                })),
            ).await
        }).await?;

        assert!(result.is_ok());

        // Test progress update
        let result = test_utils.run_db_operation(async {
            playing_progress(
                State(state.clone()),
                headers.clone(),
                <PERSON><PERSON>(json!({
                    "ItemId": "test-item",
                    "PositionTicks": 1000,
                    "IsPaused": false,
                })),
            ).await
        }).await?;

        assert!(result.is_ok());

        // Test stop
        let result = test_utils.run_db_operation(async {
            playing_stopped(
                State(state.clone()),
                headers.clone(),
                Json(json!({
                    "ItemId": "test-item",
                })),
            ).await
        }).await?;

        assert!(result.is_ok());

        Ok(())
    }

    #[tokio::test]
    async fn test_concurrent_playback() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();
        let mut headers = HeaderMap::new();
        headers.insert("X-Emby-Session", "test-session".parse().unwrap());

        // Test concurrent progress updates
        let mut handles = vec![];
        for i in 0..3 {
            let state_clone = state.clone();
            let test_utils = test_utils.clone();
            let headers = headers.clone();
            
            let handle = tokio::spawn(async move {
                tokio::time::sleep(std::time::Duration::from_millis(10)).await;
                test_utils.run_db_operation(async {
                    let result = playing_progress(
                        State(state_clone),
                        headers,
                        Json(json!({
                            "ItemId": "test-item",
                            "PositionTicks": i * 1000,
                            "IsPaused": false,
                        }))
                    ).await;
                    assert!(result.is_ok());
                    Ok(())
                }).await.unwrap();
            });
            
            handles.push(handle);
        }

        // Wait for all tasks to complete
        for handle in handles {
            handle.await.unwrap();
        }

        Ok(())
    }
}

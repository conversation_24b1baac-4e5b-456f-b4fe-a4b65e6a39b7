//! Authentication types and data structures
use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize)]
pub struct AuthenticateByNameRequest {
    #[serde(rename = "Username")]
    pub username: String,
    #[serde(rename = "Pw")]
    pub password: String,
    #[serde(rename = "Password")]
    pub password_alt: Option<String>,
}

#[derive(Debug, Serial<PERSON>)]
pub struct AuthenticationResponse {
    pub user: UserResponse,
    pub session_info: SessionResponse,
    pub access_token: String,
    pub server_id: String,
}

#[derive(Debug, Serialize)]
pub struct UserResponse {
    pub id: String,
    pub name: String,
}

#[derive(Debug, Serialize)]
pub struct SessionResponse {
    pub id: String,
    pub user_id: String,
}

pub mod media;
pub mod system;
pub mod users;

use axum::{http::StatusCode, response::J<PERSON>};
use serde_json::{json, Value};

/// Health check endpoint
pub async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

/// Generic error response
pub fn error_response(message: &str, status: StatusCode) -> (StatusCode, Json<Value>) {
    (
        status,
        Json(json!({
            "error": message,
            "timestamp": chrono::Utc::now().to_rfc3339()
        })),
    )
}

/// Success response with data
pub fn success_response<T: serde::Serialize>(data: T) -> Json<Value> {
    Json(json!({
        "data": data,
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}

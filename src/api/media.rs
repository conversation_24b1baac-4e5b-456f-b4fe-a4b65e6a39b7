use axum::{
    extract::{Path, Query, State},
    http::{header, HeaderMap, StatusCode},
    response::{IntoResponse, Json, Response},
};
use serde::Deserialize;
use serde_json::{json, Value};
use tracing::{debug, error, warn};

use crate::{
    media::images::{ImageProcessor, ImageType, ThumbnailRequest},
    server::AppState,
};

#[derive(Debug, Deserialize)]
pub struct ItemsQuery {
    #[serde(rename = "ParentId")]
    pub parent_id: Option<String>,
    #[serde(rename = "IncludeItemTypes")]
    pub include_item_types: Option<String>,
    #[serde(rename = "Recursive")]
    pub recursive: Option<bool>,
    #[serde(rename = "Fields")]
    pub fields: Option<String>,
    #[serde(rename = "StartIndex")]
    pub start_index: Option<i32>,
    #[serde(rename = "Limit")]
    pub limit: Option<i32>,
    #[serde(rename = "SearchTerm")]
    pub search_term: Option<String>,
    #[serde(rename = "SortBy")]
    pub sort_by: Option<String>,
    #[serde(rename = "SortOrder")]
    pub sort_order: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct ImageQuery {
    #[serde(rename = "MaxWidth")]
    pub max_width: Option<u32>,
    #[serde(rename = "MaxHeight")]
    pub max_height: Option<u32>,
    #[serde(rename = "Quality")]
    pub quality: Option<u8>,
    #[serde(rename = "Format")]
    pub format: Option<String>,
}

pub async fn get_items(
    State(state): State<AppState>,
    Query(query): Query<ItemsQuery>,
) -> Result<Json<Value>, StatusCode> {
    debug!("Getting items with query: {:?}", query);

    let library_manager = state.media_manager.library_manager();

    // Handle search
    if let Some(search_term) = query.search_term {
        let limit = query.limit.unwrap_or(100) as i64;

        match library_manager.search_items(&search_term, limit).await {
            Ok(items) => {
                let jellyfin_items: Vec<Value> = items
                    .into_iter()
                    .map(|item| media_item_to_jellyfin(&item))
                    .collect();

                return Ok(Json(json!({
                    "Items": jellyfin_items,
                    "TotalRecordCount": jellyfin_items.len(),
                    "StartIndex": query.start_index.unwrap_or(0)
                })));
            }
            Err(e) => {
                error!("Search failed: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        }
    }

    // Handle library listing
    if query.parent_id.is_none() {
        // Return libraries
        match library_manager.get_libraries().await {
            Ok(libraries) => {
                let jellyfin_items: Vec<Value> = libraries
                    .into_iter()
                    .map(|lib| library_to_jellyfin(&lib))
                    .collect();

                return Ok(Json(json!({
                    "Items": jellyfin_items,
                    "TotalRecordCount": jellyfin_items.len(),
                    "StartIndex": 0
                })));
            }
            Err(e) => {
                error!("Failed to get libraries: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        }
    }

    // Handle library content
    if let Some(parent_id) = query.parent_id {
        match library_manager.get_library_items(&parent_id, None).await {
            Ok(items) => {
                let jellyfin_items: Vec<Value> = items
                    .into_iter()
                    .map(|item| media_item_to_jellyfin(&item))
                    .collect();

                return Ok(Json(json!({
                    "Items": jellyfin_items,
                    "TotalRecordCount": jellyfin_items.len(),
                    "StartIndex": query.start_index.unwrap_or(0)
                })));
            }
            Err(e) => {
                error!("Failed to get library items: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        }
    }

    // Default: return recent items
    match library_manager
        .get_recent_items(query.limit.unwrap_or(50) as i64)
        .await
    {
        Ok(items) => {
            let jellyfin_items: Vec<Value> = items
                .into_iter()
                .map(|item| media_item_to_jellyfin(&item))
                .collect();

            Ok(Json(json!({
                "Items": jellyfin_items,
                "TotalRecordCount": jellyfin_items.len(),
                "StartIndex": query.start_index.unwrap_or(0)
            })))
        }
        Err(e) => {
            error!("Failed to get recent items: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

pub async fn get_item(
    State(state): State<AppState>,
    Path(item_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    debug!("Getting item: {}", item_id);

    let library_manager = state.media_manager.library_manager();

    match library_manager.get_media_item(&item_id).await {
        Ok(Some(item)) => {
            let jellyfin_item = media_item_to_jellyfin(&item);
            Ok(Json(jellyfin_item))
        }
        Ok(None) => {
            warn!("Item not found: {}", item_id);
            Err(StatusCode::NOT_FOUND)
        }
        Err(e) => {
            error!("Failed to get item: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

pub async fn get_item_image(
    State(state): State<AppState>,
    Path((item_id, image_type)): Path<(String, String)>,
    headers: HeaderMap,
    Query(query): Query<ImageQuery>,
) -> Result<Response, StatusCode> {
    debug!("Getting image for item: {}, type: {}", item_id, image_type);

    // Parse image type
    let img_type = match ImageType::from_str(&image_type) {
        Some(t) => t,
        None => {
            warn!("Invalid image type: {}", image_type);
            return Err(StatusCode::BAD_REQUEST);
        }
    };

    // Create image processor
    let image_processor =
        match ImageProcessor::new(state.config.clone(), state.database.clone()).await {
            Ok(processor) => processor,
            Err(e) => {
                error!("Failed to create image processor: {}", e);
                return Err(StatusCode::INTERNAL_SERVER_ERROR);
            }
        };

    // Create thumbnail request
    let request = ThumbnailRequest {
        item_id,
        image_type: img_type,
        width: query.max_width,
        height: query.max_height,
        quality: query.quality,
        format: parse_image_format(&query.format),
    };

    // Prepare ETag based on cache key
    let cache_key = image_processor.generate_cache_key(&request);
    let etag_value = format!("W/\"{}\"", cache_key);
    if let Some(inm) = headers
        .get(header::IF_NONE_MATCH)
        .and_then(|v| v.to_str().ok())
    {
        if inm == etag_value {
            return Ok((
                StatusCode::NOT_MODIFIED,
                [
                    (header::ETAG, etag_value.as_str()),
                    (header::CACHE_CONTROL, "public, max-age=31536000, immutable"),
                ],
            )
                .into_response());
        }
    }

    // Generate thumbnail
    match image_processor.generate_thumbnail(request).await {
        Ok(image_data) => {
            let content_type = match query.format.as_deref() {
                Some("png") => "image/png",
                Some("webp") => "image/webp",
                _ => "image/jpeg",
            };

            Ok((
                StatusCode::OK,
                [
                    (header::CONTENT_TYPE, content_type),
                    (header::ETAG, etag_value.as_str()),
                    (header::CACHE_CONTROL, "public, max-age=31536000, immutable"),
                ],
                image_data,
            )
                .into_response())
        }
        Err(e) => {
            error!("Failed to generate thumbnail: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

pub async fn get_playback_info(
    State(state): State<AppState>,
    Path(item_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    debug!("Getting playback info for item: {}", item_id);

    let library_manager = state.media_manager.library_manager();

    // Get media item
    let media_item = match library_manager.get_media_item(&item_id).await {
        Ok(Some(item)) => item,
        Ok(None) => return Err(StatusCode::NOT_FOUND),
        Err(e) => {
            error!("Failed to get media item: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // Create playback info response
    let playback_info = json!({
        "MediaSources": [{
            "Id": media_item.id,
            "Path": media_item.path,
            "Protocol": "File",
            "Container": media_item.container.as_ref().unwrap_or(&"mp4".to_string()),
            "Size": media_item.file_size,
            "Name": media_item.name,
            "RunTimeTicks": media_item.duration.map(|d| d * 10000), // Convert ms to ticks
            "SupportsTranscoding": true,
            "SupportsDirectStream": true,
            "SupportsDirectPlay": true,
            "IsInfiniteStream": false,
            "RequiresOpening": false,
            "RequiresClosing": false,
            "RequiresLooping": false,
            "SupportsProbing": true,
            "VideoType": "VideoFile",
            "MediaStreams": create_media_streams(&media_item),
            "Formats": [],
            "Bitrate": media_item.bitrate,
            "RequiredHttpHeaders": {}
        }],
        "PlaySessionId": uuid::Uuid::new_v4().to_string()
    });

    Ok(Json(playback_info))
}

fn media_item_to_jellyfin(item: &crate::database::models::MediaItem) -> Value {
    json!({
        "Id": item.id,
        "Name": item.name,
        "SortName": item.sort_name,
        "Path": item.path,
        "Type": item.item_type,
        "MediaType": item.media_type,
        "IsFolder": item.item_type == "Series" || item.item_type == "Season",
        "ParentId": item.parent_id,
        "DateCreated": item.created_at.to_rfc3339(),
        "DateModified": item.date_modified.to_rfc3339(),
        "Size": item.file_size,
        "Container": item.container.as_ref(),
        "RunTimeTicks": item.duration.map(|d| d * 10000), // Convert ms to ticks
        "ProductionYear": null, // TODO: Extract from metadata
        "PremiereDate": null,   // TODO: Extract from metadata
        "CommunityRating": null, // TODO: Extract from metadata
        "OfficialRating": null,  // TODO: Extract from metadata
        "Overview": null,        // TODO: Extract from metadata
        "Taglines": [],
        "Genres": [],
        "Studios": [],
        "People": [],
        "ProviderIds": {},
        "ParentIndexNumber": null,
        "IndexNumber": null,
        "LocationType": "FileSystem",
        "MediaType": item.media_type,
        "HasSubtitles": false, // TODO: Detect subtitles
        "HasLyrics": false,
        "PreferredMetadataLanguage": "en",
        "PreferredMetadataCountryCode": "US",
        "SupportsSync": false,
        "Container": item.container.as_ref(),
        "SortName": &item.sort_name,
        "ForcedSortName": &item.sort_name,
        "Video3DFormat": null,
        "PrimaryImageAspectRatio": calculate_aspect_ratio(item.width, item.height),
        "VideoType": "VideoFile",
        "ImageTags": {
            "Primary": item.id.clone() // Use item ID as image tag
        },
        "BackdropImageTags": [],
        "ScreenshotImageTags": [],
        "ImageBlurHashes": {},
        "SeriesTimerId": null,
        "ChannelId": null,
        "StartDate": null,
        "EndDate": null,
        "SeriesId": null,
        "SeasonId": null,
        "SpecialFeatureCount": null,
        "DisplayPreferencesId": item.id,
        "Tags": [],
        "Keywords": [],
        "MovieCount": null,
        "SeriesCount": null,
        "EpisodeCount": null,
        "SongCount": null,
        "AlbumCount": null,
        "MusicVideoCount": null,
        "LockData": false,
        "Width": item.width,
        "Height": item.height,
        "AspectRatio": item.aspect_ratio,
        "CameraMake": null,
        "CameraModel": null,
        "Software": null,
        "ExposureTime": null,
        "FocalLength": null,
        "ImageOrientation": null,
        "Aperture": null,
        "ShutterSpeed": null,
        "Latitude": null,
        "Longitude": null,
        "Altitude": null,
        "IsoSpeedRating": null,
        "SeriesStudio": null,
        "ChannelPrimaryImageTag": null,
        "CollectionType": null
    })
}

fn library_to_jellyfin(library: &crate::database::models::Library) -> Value {
    json!({
        "Id": library.id,
        "Name": library.name,
        "Type": "CollectionFolder",
        "IsFolder": true,
        "ParentId": null,
        "DateCreated": library.created_at.to_rfc3339(),
        "DateModified": library.updated_at.to_rfc3339(),
        "CollectionType": library.library_type.to_lowercase(),
        "LocationType": "FileSystem",
        "ImageTags": {},
        "BackdropImageTags": [],
        "ScreenshotImageTags": [],
        "ImageBlurHashes": {},
        "DisplayPreferencesId": library.id,
        "Tags": [],
        "LockData": false
    })
}

fn create_media_streams(item: &crate::database::models::MediaItem) -> Vec<Value> {
    let mut streams = Vec::new();

    // Video stream
    if let Some(ref video_codec) = item.video_codec {
        streams.push(json!({
            "Index": 0,
            "Type": "Video",
            "Codec": video_codec,
            "Language": null,
            "ColorTransfer": null,
            "ColorPrimaries": null,
            "ColorSpace": null,
            "Comment": null,
            "StreamStartTimeTicks": 0,
            "TimeBase": "1/1000",
            "Title": null,
            "Extradata": null,
            "VideoRange": "SDR",
            "DisplayTitle": format!("{} ({})", video_codec.to_uppercase(), "Video"),
            "NalLengthSize": null,
            "IsInterlaced": false,
            "IsAVC": video_codec == "h264",
            "BitRate": item.bitrate,
            "BitDepth": 8,
            "RefFrames": 1,
            "IsDefault": true,
            "IsForced": false,
            "Height": item.height,
            "Width": item.width,
            "AverageFrameRate": item.framerate,
            "RealFrameRate": item.framerate,
            "Profile": "Main",
            "AspectRatio": item.aspect_ratio,
            "Index": 0,
            "IsExternal": false,
            "IsTextSubtitleStream": false,
            "SupportsExternalStream": false,
            "PixelFormat": "yuv420p",
            "Level": 40
        }));
    }

    // Audio stream
    if let Some(ref audio_codec) = item.audio_codec {
        streams.push(json!({
            "Index": 1,
            "Type": "Audio",
            "Codec": audio_codec,
            "Language": "eng",
            "Comment": null,
            "StreamStartTimeTicks": 0,
            "TimeBase": "1/1000",
            "Title": null,
            "DisplayTitle": format!("{} ({})", audio_codec.to_uppercase(), "Audio"),
            "IsInterlaced": false,
            "BitRate": 128000, // Default audio bitrate
            "Channels": 2,
            "SampleRate": 48000,
            "IsDefault": true,
            "IsForced": false,
            "Index": 1,
            "IsExternal": false,
            "IsTextSubtitleStream": false,
            "SupportsExternalStream": false
        }));
    }

    streams
}

fn calculate_aspect_ratio(width: Option<i32>, height: Option<i32>) -> Option<f64> {
    match (width, height) {
        (Some(w), Some(h)) if h != 0 => Some(w as f64 / h as f64),
        _ => None,
    }
}

fn parse_image_format(format_str: &Option<String>) -> Option<image::ImageFormat> {
    match format_str.as_deref() {
        Some("png") => Some(image::ImageFormat::Png),
        Some("webp") => Some(image::ImageFormat::WebP),
        Some("jpeg") | Some("jpg") => Some(image::ImageFormat::Jpeg),
        _ => None,
    }
}

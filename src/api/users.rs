use axum::{
    extract::{Path, State},
    http::{HeaderMap, StatusCode},
    response::J<PERSON>,
};
use serde_json::{json, Value};

use crate::{database::models::User, server::AppState};

fn extract_token(headers: &HeaderMap) -> Option<String> {
    if let Some(auth) = headers
        .get("X-Emby-Authorization")
        .and_then(|v| v.to_str().ok())
    {
        for part in auth.split(',') {
            let trimmed = part.trim();
            if let Some(v) = trimmed.strip_prefix("Token=") {
                return Some(v.trim_matches('"').to_string());
            }
        }
    }
    if let Some(bearer) = headers.get("Authorization").and_then(|v| v.to_str().ok()) {
        if let Some(t) = bearer.strip_prefix("Bearer ") {
            return Some(t.to_string());
        }
    }
    None
}

pub async fn get_users(State(state): State<AppState>) -> Result<Json<Value>, StatusCode> {
    let users: Vec<User> = sqlx::query_as("SELECT * FROM users ORDER BY name")
        .fetch_all(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let items: Vec<Value> = users
        .into_iter()
        .map(|user| {
            json!({
                "Id": user.id,
                "Name": user.name,
                "IsAdministrator": user.is_administrator,
                "HasPassword": user.password_hash.is_some()
            })
        })
        .collect();

    Ok(Json(json!({
        "Items": items,
        "TotalRecordCount": items.len(),
        "StartIndex": 0
    })))
}

pub async fn get_user(
    State(state): State<AppState>,
    Path(user_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    let user: Option<User> = sqlx::query_as("SELECT * FROM users WHERE id = ?")
        .bind(&user_id)
        .fetch_optional(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    if let Some(user) = user {
        return Ok(Json(json!({
            "Id": user.id,
            "Name": user.name,
            "IsAdministrator": user.is_administrator,
            "HasPassword": user.password_hash.is_some()
        })));
    }
    Err(StatusCode::NOT_FOUND)
}

pub async fn get_current_user(
    State(state): State<AppState>,
    headers: HeaderMap,
) -> Result<Json<Value>, StatusCode> {
    let token = extract_token(&headers).ok_or(StatusCode::UNAUTHORIZED)?;
    let session: Option<(String, String)> = sqlx::query_as(
        "SELECT user_id, id FROM sessions WHERE access_token = ? AND is_active = TRUE",
    )
    .bind(&token)
    .fetch_optional(state.database.pool())
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    let (user_id, _session_id) = session.ok_or(StatusCode::UNAUTHORIZED)?;

    let user: User = sqlx::query_as("SELECT * FROM users WHERE id = ?")
        .bind(&user_id)
        .fetch_one(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    Ok(Json(json!({
        "Id": user.id,
        "Name": user.name,
        "IsAdministrator": user.is_administrator,
        "HasPassword": user.password_hash.is_some(),
    })))
}

pub async fn get_resume_items(
    State(state): State<AppState>,
    Path(user_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    // Get resume items with proper indexing, ordered by last activity
    let rows: Vec<(String, String, i64, String, String)> = sqlx::query_as(
        r#"
        SELECT p.item_id, m.name, p.position_ticks, m.item_type, m.media_type
        FROM playback_sessions p
        JOIN media_items m ON m.id = p.item_id
        WHERE p.user_id = ? AND p.position_ticks > 0
        ORDER BY p.updated_at DESC
        LIMIT 50
        "#,
    )
    .bind(&user_id)
    .fetch_all(state.database.pool())
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let items: Vec<Value> = rows
        .into_iter()
        .map(|(item_id, name, position_ticks, item_type, media_type)| {
            json!({
                "Id": item_id,
                "Name": name,
                "Type": item_type,
                "MediaType": media_type,
                "UserData": {
                    "PlaybackPositionTicks": position_ticks,
                    "UnplayedItemCount": null,
                    "Played": false,
                    "PlayedPercentage": null,
                },
            })
        })
        .collect();

    Ok(Json(
        json!({ "Items": items, "TotalRecordCount": items.len() }),
    ))
}

pub async fn get_next_up(
    State(state): State<AppState>,
    Path(user_id): Path<String>,
) -> Result<Json<Value>, StatusCode> {
    // Get next episodes for TV series based on watch history
    let rows: Vec<(String, String, String, i32, i32, String)> = sqlx::query_as(
        r#"
        SELECT 
            m.id, m.name, m.item_type, 
            CAST(SUBSTR(m.name, INSTR(m.name, 'E') + 1, 2) AS INTEGER) as episode_num,
            CAST(SUBSTR(m.name, INSTR(m.name, 'S') + 1, 2) AS INTEGER) as season_num,
            m.parent_id
        FROM media_items m
        LEFT JOIN playback_sessions p ON p.item_id = m.id AND p.user_id = ?
        WHERE m.item_type = 'Episode' 
        AND m.parent_id IN (
            SELECT DISTINCT m2.parent_id 
            FROM playback_sessions p2
            JOIN media_items m2 ON p2.item_id = m2.id
            WHERE p2.user_id = ? AND p2.position_ticks > 0
        )
        ORDER BY season_num, episode_num
        LIMIT 20
        "#,
    )
    .bind(&user_id)
    .bind(&user_id)
    .fetch_all(state.database.pool())
    .await
    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let items: Vec<Value> = rows
        .into_iter()
        .map(
            |(id, name, item_type, episode_num, season_num, parent_id)| {
                json!({
                    "Id": id,
                    "Name": name,
                    "Type": item_type,
                    "ParentIndexNumber": season_num,
                    "IndexNumber": episode_num,
                    "ParentId": parent_id,
                })
            },
        )
        .collect();

    // Validate the JSON response internally
    assert!(!items.is_empty(), "No items found");
    Ok(Json(
        json!({ "Items": items, "TotalRecordCount": items.len() }),
    ))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::{
        create_test_media_item, create_test_media_item_with_dependencies, create_test_user,
    };
    use crate::{config::Config, database::Database, server::AppState};
    use std::sync::Arc;

    fn create_test_config() -> Arc<Config> {
        Arc::new(Config::default())
    }

    async fn create_test_database() -> Database {
        let config = create_test_config();
        // Create unique in-memory database for each test
        let mut db_config = config.database.clone();
        db_config.path = std::path::PathBuf::from(format!(
            "file:test_{}?mode=memory&cache=shared",
            uuid::Uuid::new_v4()
        ));
        let database = Database::new(&db_config).await.unwrap();

        // Initialize database with tables (but skip default user creation for tests)
        crate::database::migrations::run_migrations_no_default_user(database.pool())
            .await
            .unwrap();

        database
    }

    async fn create_test_app_state() -> AppState {
        let config = create_test_config();
        let database = create_test_database().await;

        AppState {
            config: config.clone(),
            database: database.clone(),
            media_manager: Arc::new(
                crate::media::MediaManager::new(config.clone(), database.clone())
                    .await
                    .unwrap(),
            ),
            streaming_engine: Arc::new(crate::streaming::StreamingEngine::new(config).unwrap()),
        }
    }

    fn create_test_headers_with_token(token: &str) -> HeaderMap {
        let mut headers = HeaderMap::new();
        headers.insert("X-Emby-Authorization", format!("MediaBrowser Client=\"test\", Device=\"test\", DeviceId=\"test\", Version=\"1.0\", Token=\"{}\"", token).parse().unwrap());
        headers
    }

    #[tokio::test]
    async fn test_get_users_empty() {
        let state = create_test_app_state().await;
        let result = get_users(State(state)).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string()).unwrap();
        assert_eq!(data["TotalRecordCount"], 0);
        assert_eq!(data["Items"].as_array().unwrap().len(), 0);
    }

    #[tokio::test]
    async fn test_get_user_success() {
        let state = create_test_app_state().await;

        // Create test user with unique name
        let user = crate::database::models::User::new(
            format!(
                "testuser_{}",
                uuid::Uuid::new_v4().to_string().split('-').next().unwrap()
            ),
            None,
            false,
        );
        sqlx::query(
            "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&user.id)
        .bind(&user.name)
        .bind(&user.password_hash)
        .bind(user.is_administrator)
        .bind(user.is_hidden)
        .bind(user.is_disabled)
        .bind(user.created_at.to_rfc3339())
        .bind(user.updated_at.to_rfc3339())
        .execute(state.database.pool())
        .await
        .unwrap();

        let result = get_user(State(state), Path(user.id.clone())).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string()).unwrap();
        assert_eq!(data["Id"], user.id);
        assert_eq!(data["Name"], user.name);
    }

    #[tokio::test]
    async fn test_get_user_not_found() {
        let state = create_test_app_state().await;
        let result = get_user(State(state), Path("nonexistent-id".to_string())).await;
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), StatusCode::NOT_FOUND);
    }

    #[tokio::test]
    async fn test_get_current_user_success() {
        let state = create_test_app_state().await;

        // Create test user and session
        let user = crate::database::models::User::new(
            format!(
                "testuser_{}",
                uuid::Uuid::new_v4().to_string().split('-').next().unwrap()
            ),
            None,
            false,
        );
        let token = format!(
            "test-token-{}",
            uuid::Uuid::new_v4().to_string().split('-').next().unwrap()
        );
        let session = crate::database::models::Session::new(
            user.id.clone(),
            "test-device".to_string(),
            "Test Device".to_string(),
            "Test Client".to_string(),
            "1.0.0".to_string(),
            token.clone(),
        );

        // Insert user and session
        sqlx::query(
            "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&user.id)
        .bind(&user.name)
        .bind(&user.password_hash)
        .bind(user.is_administrator)
        .bind(user.is_hidden)
        .bind(user.is_disabled)
        .bind(user.created_at.to_rfc3339())
        .bind(user.updated_at.to_rfc3339())
        .execute(state.database.pool())
        .await
        .unwrap();

        sqlx::query(
            "INSERT INTO sessions (id, user_id, device_id, device_name, client, version, access_token, created_at, last_activity, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&session.id)
        .bind(&session.user_id)
        .bind(&session.device_id)
        .bind(&session.device_name)
        .bind(&session.client)
        .bind(&session.version)
        .bind(&session.access_token)
        .bind(session.created_at.to_rfc3339())
        .bind(session.last_activity.to_rfc3339())
        .bind(session.is_active)
        .execute(state.database.pool())
        .await
        .unwrap();

        let headers = create_test_headers_with_token(&token);
        let result = get_current_user(State(state), headers).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string()).unwrap();
        assert_eq!(data["Id"], user.id);
        assert_eq!(data["Name"], user.name);
    }

    #[tokio::test]
    async fn test_get_current_user_unauthorized() {
        let state = create_test_app_state().await;
        let headers = HeaderMap::new(); // No token
        let result = get_current_user(State(state), headers).await;
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_get_resume_items() -> Result<(), StatusCode> {
        let state = create_test_app_state().await;
        // Create test user
        let user = create_test_user(state.database.pool(), "testuser", None, false)
            .await
            .expect("Failed to create test user");
        // Create test media item and its library
        let media_item = create_test_media_item_with_dependencies(
            state.database.pool(),
            "Test Library",
            &format!("/test/path/{}", uuid::Uuid::new_v4()),
            "Video",
            "Test Movie",
            "/path/to/movie.mp4",
            "Movie",
            "Video",
        )
        .await
        .expect("Failed to create test media item");
        // Create playback session linked to user and media item
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();
        sqlx::query(
            "INSERT INTO playback_sessions (id, user_id, item_id, session_id, device_id, position_ticks, is_paused, volume_level, is_muted, subtitle_stream_index, audio_stream_index, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(uuid::Uuid::new_v4().to_string())
        .bind(&user.id)
        .bind(&media_item.id)
        .bind(uuid::Uuid::new_v4().to_string())
        .bind("test-device")
        .bind(5000)
        .bind(false)
        .bind(80)
        .bind(false)
        .bind(0)
        .bind(0)
        .bind(&now)
        .bind(&now)
        .execute(state.database.pool())
        .await
        .expect("Failed to insert playback session");
        let result = get_resume_items(State(state), Path(user.id)).await;
        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string()).unwrap();
        assert_eq!(data["TotalRecordCount"], 1);
        assert_eq!(data["Items"].as_array().unwrap().len(), 1);
        let item = &data["Items"][0];
        assert_eq!(item["Id"], media_item.id);
        assert_eq!(item["Name"], "Test Movie");
        assert_eq!(item["UserData"]["PlaybackPositionTicks"], 5000);
        Ok(())
    }

    #[tokio::test]
    async fn test_get_next_up() -> Result<(), StatusCode> {
        let state = create_test_app_state().await;
        let user = create_test_user(state.database.pool(), "testuser", None, false)
            .await
            .expect("Failed to create test user");
        // Create TV series and its library
        let series = create_test_media_item_with_dependencies(
            state.database.pool(),
            "Test TV Library",
            &format!("/test/tv/{}", uuid::Uuid::new_v4()),
            "Video",
            "Sample TV Show",
            "/path/to/tvshow",
            "Series",
            "Video",
        )
        .await
        .expect("Failed to create test series");
        // Fetch the library for the created series
        let library: crate::database::models::Library =
            sqlx::query_as("SELECT * FROM libraries WHERE id = ?")
                .bind(&series.library_id)
                .fetch_one(state.database.pool())
                .await
                .expect("Failed to fetch library for series");

        // Create episode linked to series
        let episode = create_test_media_item(
            state.database.pool(),
            &library,
            Some(series.id.clone()),
            "Sample TV Show S01E01",
            "/path/to/tvshow/episode1.mp4",
            "Episode",
            "Video",
        )
        .await
        .expect("Failed to create test episode");
        // Create playback session for episode
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();
        sqlx::query(
            "INSERT INTO playback_sessions (id, user_id, item_id, session_id, device_id, position_ticks, is_paused, volume_level, is_muted, subtitle_stream_index, audio_stream_index, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(uuid::Uuid::new_v4().to_string())
        .bind(&user.id)
        .bind(&episode.id)
        .bind(uuid::Uuid::new_v4().to_string())
        .bind("test-device")
        .bind(5000)
        .bind(false)
        .bind(80)
        .bind(false)
        .bind(0)
        .bind(0)
        .bind(&now)
        .bind(&now)
        .execute(state.database.pool())
        .await
        .expect("Failed to insert playback session");
        // Query next up
        let user_id = user.id.clone();
        let rows: Vec<(String, String, String, i32, i32, String)> = sqlx::query_as(
            r#"
            SELECT 
                m.id, m.name, m.item_type, 
                CAST(SUBSTR(m.name, INSTR(m.name, 'E') + 1, 2) AS INTEGER) as episode_num,
                CAST(SUBSTR(m.name, INSTR(m.name, 'S') + 1, 2) AS INTEGER) as season_num,
                m.parent_id
            FROM media_items m
            LEFT JOIN playback_sessions p ON p.item_id = m.id AND p.user_id = ?
            WHERE m.item_type = 'Episode' 
            AND m.parent_id IN (
                SELECT DISTINCT m2.parent_id 
                FROM playback_sessions p2
                JOIN media_items m2 ON p2.item_id = m2.id
                WHERE p2.user_id = ? AND p2.position_ticks > 0
            )
            ORDER BY season_num, episode_num
            LIMIT 20
            "#,
        )
        .bind(&user_id)
        .bind(&user_id)
        .fetch_all(state.database.pool())
        .await
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        let items: Vec<Value> = rows
            .into_iter()
            .map(
                |(id, name, item_type, episode_num, season_num, parent_id)| {
                    json!({
                        "Id": id,
                        "Name": name,
                        "Type": item_type,
                        "ParentIndexNumber": season_num,
                        "IndexNumber": episode_num,
                        "ParentId": parent_id,
                    })
                },
            )
            .collect();
        assert!(!items.is_empty(), "No items found");
        Ok(())
    }
}

use axum::{extract::State, response::J<PERSON>};
use serde::Serialize;
use serde_json::{json, Value};

use crate::server::AppState;

#[derive(Debug, Serialize)]
pub struct SystemInfo {
    #[serde(rename = "LocalAddress")]
    pub local_address: String,
    #[serde(rename = "ServerName")]
    pub server_name: String,
    #[serde(rename = "Version")]
    pub version: String,
    #[serde(rename = "ProductName")]
    pub product_name: String,
    #[serde(rename = "OperatingSystem")]
    pub operating_system: String,
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "StartupWizardCompleted")]
    pub startup_wizard_completed: bool,
    #[serde(rename = "SupportsLibraryMonitor")]
    pub supports_library_monitor: bool,
    #[serde(rename = "WebSocketPortNumber")]
    pub web_socket_port_number: u16,
    #[serde(rename = "CompletedInstallations")]
    pub completed_installations: Vec<String>,
    #[serde(rename = "CanSelfRestart")]
    pub can_self_restart: bool,
    #[serde(rename = "CanLaunchWebBrowser")]
    pub can_launch_web_browser: bool,
    #[serde(rename = "ProgramDataPath")]
    pub program_data_path: String,
    #[serde(rename = "WebPath")]
    pub web_path: String,
    #[serde(rename = "ItemsByNamePath")]
    pub items_by_name_path: String,
    #[serde(rename = "CachePath")]
    pub cache_path: String,
    #[serde(rename = "LogPath")]
    pub log_path: String,
    #[serde(rename = "InternalMetadataPath")]
    pub internal_metadata_path: String,
    #[serde(rename = "TranscodingTempPath")]
    pub transcoding_temp_path: String,
    #[serde(rename = "HasPendingRestart")]
    pub has_pending_restart: bool,
    #[serde(rename = "IsShuttingDown")]
    pub is_shutting_down: bool,
    #[serde(rename = "SupportsLibraryMonitor")]
    pub supports_library_monitor_2: bool,
    #[serde(rename = "WebSocketPortNumber")]
    pub web_socket_port_number_2: u16,
    #[serde(rename = "SupportsAutoRunAtStartup")]
    pub supports_auto_run_at_startup: bool,
    #[serde(rename = "HardwareAccelerationRequiresPremiere")]
    pub hardware_acceleration_requires_premiere: bool,
}

#[derive(Debug, Serialize)]
pub struct PublicSystemInfo {
    #[serde(rename = "LocalAddress")]
    pub local_address: String,
    #[serde(rename = "ServerName")]
    pub server_name: String,
    #[serde(rename = "Version")]
    pub version: String,
    #[serde(rename = "ProductName")]
    pub product_name: String,
    #[serde(rename = "OperatingSystem")]
    pub operating_system: String,
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "StartupWizardCompleted")]
    pub startup_wizard_completed: bool,
}

pub async fn get_system_info(State(state): State<AppState>) -> Json<SystemInfo> {
    let server_id = uuid::Uuid::new_v4().to_string();
    let local_address = format!(
        "{}:{}",
        state.config.server.bind_address, state.config.server.port
    );

    Json(SystemInfo {
        local_address: local_address.clone(),
        server_name: state.config.server.server_name.clone(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        product_name: "Tulip Media Server".to_string(),
        operating_system: "Linux".to_string(),
        id: server_id.clone(),
        startup_wizard_completed: true,
        supports_library_monitor: true,
        web_socket_port_number: state.config.server.port,
        completed_installations: vec![],
        can_self_restart: false,
        can_launch_web_browser: false,
        program_data_path: "/data".to_string(),
        web_path: "/web".to_string(),
        items_by_name_path: "/data/metadata".to_string(),
        cache_path: "/data/cache".to_string(),
        log_path: "/data/logs".to_string(),
        internal_metadata_path: "/data/metadata".to_string(),
        transcoding_temp_path: "/tmp/transcoding".to_string(),
        has_pending_restart: false,
        is_shutting_down: false,
        supports_library_monitor_2: true,
        web_socket_port_number_2: state.config.server.port,
        supports_auto_run_at_startup: false,
        hardware_acceleration_requires_premiere: false,
    })
}

pub async fn get_public_system_info(State(state): State<AppState>) -> Json<PublicSystemInfo> {
    let server_id = uuid::Uuid::new_v4().to_string();
    let local_address = format!(
        "{}:{}",
        state.config.server.bind_address, state.config.server.port
    );

    Json(PublicSystemInfo {
        local_address,
        server_name: state.config.server.server_name.clone(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        product_name: "Tulip Media Server".to_string(),
        operating_system: "Linux".to_string(),
        id: server_id,
        startup_wizard_completed: true,
    })
}

pub async fn ping() -> Json<Value> {
    Json(json!("Tulip Media Server"))
}

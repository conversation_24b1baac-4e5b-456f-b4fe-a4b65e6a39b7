#[cfg(test)]
mod tests {
    use super::*;
    use crate::test_utils::TestUtils;

    async fn setup_test_data(state: &AppState, test_utils: &TestUtils) -> Result<(User, Library, MediaItem)> {
        let user = User::new("test_user".into(), Some("password123".into()), false);
        let library = Library::new(
            "Test Library".into(),
            "/test/path".into(),
            "Movies".into(),
        );
        let media_item = MediaItem::new(
            library.id.clone(),
            None,
            "Test Movie".into(),
            "test_movie".into(),
            "/test/path/movie.mp4".into(),
            "Movie".into(),
            "Video".into(),
            1024 * 1024 * 100,
        );

        test_utils.run_db_operation(async {
            let mut tx = state.database.pool().begin().await?;

            // Insert library
            sqlx::query(
                "INSERT INTO libraries (id, name, path, library_type, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)"
            )
            .bind(&library.id)
            .bind(&library.name)
            .bind(&library.path)
            .bind(&library.library_type)
            .bind(&library.created_at.to_rfc3339())
            .bind(&library.updated_at.to_rfc3339())
            .execute(&mut *tx)
            .await?;

            // Insert user
            sqlx::query(
                "INSERT INTO users (id, name, password_hash, is_administrator, is_hidden, is_disabled, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
            )
            .bind(&user.id)
            .bind(&user.name)
            .bind(&user.password_hash)
            .bind(user.is_administrator)
            .bind(user.is_hidden)
            .bind(user.is_disabled)
            .bind(user.created_at.to_rfc3339())
            .bind(user.updated_at.to_rfc3339())
            .execute(&mut *tx)
            .await?;

            // Insert media item
            sqlx::query(
                "INSERT INTO media_items (id, library_id, parent_id, name, sort_name, path, item_type, media_type, file_size, created_at, updated_at, date_added, date_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            )
            .bind(&media_item.id)
            .bind(&media_item.library_id)
            .bind(&media_item.parent_id)
            .bind(&media_item.name)
            .bind(&media_item.sort_name)
            .bind(&media_item.path)
            .bind(&media_item.item_type)
            .bind(&media_item.media_type)
            .bind(media_item.file_size)
            .bind(media_item.created_at.to_rfc3339())
            .bind(media_item.updated_at.to_rfc3339())
            .bind(media_item.date_added.to_rfc3339())
            .bind(media_item.date_modified.to_rfc3339())
            .execute(&mut *tx)
            .await?;

            // Create playback session
            sqlx::query(
                "INSERT INTO playback_sessions (id, user_id, item_id, session_id, device_id, position_ticks, is_paused, volume_level, is_muted, subtitle_stream_index, audio_stream_index, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            )
            .bind(uuid::Uuid::new_v4().to_string())
            .bind(&user.id)
            .bind(&media_item.id)
            .bind(uuid::Uuid::new_v4().to_string())
            .bind("test-device")
            .bind(5000)
            .bind(false)
            .bind(80)
            .bind(false)
            .bind(0)
            .bind(0)
            .bind(chrono::Utc::now().to_rfc3339())
            .bind(chrono::Utc::now().to_rfc3339())
            .execute(&mut *tx)
            .await?;

            tx.commit().await?;
            Ok::<_, anyhow::Error>(())
        }).await?;

        Ok((user, library, media_item))
    }

    #[tokio::test]
    async fn test_get_resume_items() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();

        let (user, _, _) = setup_test_data(&state, &test_utils).await?;

        // Test getting resume items
        let result = test_utils.run_db_operation(async {
            get_resume_items(State(state), Path(user.id)).await
        }).await?;

        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string())?;
        assert_eq!(data["TotalRecordCount"], 1);
        assert_eq!(data["Items"].as_array().unwrap().len(), 1);
        Ok(())
    }

    #[tokio::test]
    async fn test_get_resume_items_edge_cases() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();

        let (user, _, _) = setup_test_data(&state, &test_utils).await?;

        // Test with invalid user ID
        let result = test_utils.run_db_operation(async {
            get_resume_items(State(state.clone()), Path("invalid-id".into())).await
        }).await?;

        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string())?;
        assert_eq!(data["TotalRecordCount"], 0);

        // Test with user having no resume items
        test_utils.run_db_operation(async {
            sqlx::query("DELETE FROM playback_sessions WHERE user_id = ?")
                .bind(&user.id)
                .execute(state.database.pool())
                .await?;
            Ok::<_, anyhow::Error>(())
        }).await?;

        let result = test_utils.run_db_operation(async {
            get_resume_items(State(state), Path(user.id)).await
        }).await?;

        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string())?;
        assert_eq!(data["TotalRecordCount"], 0);
        Ok(())
    }

    #[tokio::test]
    async fn test_get_next_up() -> Result<()> {
        let state = create_test_app_state().await;
        let test_utils = TestUtils::new();

        let (user, _, _) = setup_test_data(&state, &test_utils).await?;

        // Test getting next up items
        let result = test_utils.run_db_operation(async {
            get_next_up(State(state), Path(user.id)).await
        }).await?;

        assert!(result.is_ok());
        let response = result.unwrap();
        let data: Value = serde_json::from_str(&response.0.to_string())?;
        assert_eq!(data["TotalRecordCount"], 0); // No TV series in test data
        Ok(())
    }
}

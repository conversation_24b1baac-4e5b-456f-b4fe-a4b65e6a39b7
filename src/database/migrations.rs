use anyhow::Result;
use sqlx::{Pool, Sqlite};
use tracing::info;

pub async fn run_migrations(pool: &Pool<Sqlite>) -> Result<()> {
    // Create users table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            password_hash TEXT,
            is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
            is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
            is_disabled BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_login_date TEXT,
            last_activity_date TEXT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create sessions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            device_name TEXT NOT NULL,
            client TEXT NOT NULL,
            version TEXT NOT NULL,
            access_token TEXT NOT NULL UNIQUE,
            created_at TEXT NOT NULL,
            last_activity TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create libraries table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS libraries (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            library_type TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_scan TEXT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_items table
    sqlx::query(
        r#"
    CREATE TABLE IF NOT EXISTS media_items (
            id TEXT PRIMARY KEY,
            library_id TEXT NOT NULL,
            parent_id TEXT,
            name TEXT NOT NULL,
            sort_name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            item_type TEXT NOT NULL,
            media_type TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            duration INTEGER,
            bitrate INTEGER,
            container TEXT,
            video_codec TEXT,
            audio_codec TEXT,
            width INTEGER,
            height INTEGER,
            aspect_ratio TEXT,
            framerate REAL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            date_added TEXT NOT NULL,
            date_modified TEXT NOT NULL,
            FOREIGN KEY (library_id) REFERENCES libraries (id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_metadata table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_metadata (
            item_id TEXT PRIMARY KEY,
            title TEXT,
            original_title TEXT,
            overview TEXT,
            tagline TEXT,
            release_date TEXT,
            runtime INTEGER,
            rating REAL,
            vote_average REAL,
            vote_count INTEGER,
            imdb_id TEXT,
            tmdb_id INTEGER,
            tvdb_id INTEGER,
            genres TEXT,
            studios TEXT,
            tags TEXT,
            people TEXT,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_images table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_images (
            id TEXT PRIMARY KEY,
            item_id TEXT NOT NULL,
            image_type TEXT NOT NULL,
            path TEXT NOT NULL,
            width INTEGER,
            height INTEGER,
            size INTEGER NOT NULL,
            created_at TEXT NOT NULL,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create playback_sessions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS playback_sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            item_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            position_ticks INTEGER NOT NULL DEFAULT 0,
            is_paused BOOLEAN NOT NULL DEFAULT FALSE,
            volume_level INTEGER,
            is_muted BOOLEAN NOT NULL DEFAULT FALSE,
            subtitle_stream_index INTEGER,
            audio_stream_index INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create indexes for performance
    create_indexes(pool).await?;

    // Create default admin user if no users exist
    create_default_user(pool).await?;

    info!("Database migrations completed successfully");
    Ok(())
}

async fn create_indexes(pool: &Pool<Sqlite>) -> Result<()> {
    let indexes = vec![
        "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id)",
        "CREATE INDEX IF NOT EXISTS idx_sessions_access_token ON sessions (access_token)",
        "CREATE INDEX IF NOT EXISTS idx_sessions_is_active ON sessions (is_active)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_library_id ON media_items (library_id)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_parent_id ON media_items (parent_id)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_item_type ON media_items (item_type)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_media_type ON media_items (media_type)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_path ON media_items (path)",
        "CREATE INDEX IF NOT EXISTS idx_media_images_item_id ON media_images (item_id)",
        "CREATE INDEX IF NOT EXISTS idx_media_images_type ON media_images (image_type)",
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_user_id ON playback_sessions (user_id)",
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_item_id ON playback_sessions (item_id)",
        // Performance indexes for resume and next-up
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_user_position ON playback_sessions (user_id, position_ticks)",
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_user_updated ON playback_sessions (user_id, updated_at)",
        "CREATE INDEX IF NOT EXISTS idx_playback_sessions_session_item ON playback_sessions (session_id, item_id)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_type_parent ON media_items (item_type, parent_id)",
        "CREATE INDEX IF NOT EXISTS idx_media_items_name_episode ON media_items (name, item_type) WHERE item_type = 'Episode'",
    ];

    for index in indexes {
        sqlx::query(index).execute(pool).await?;
    }

    Ok(())
}

async fn create_default_user(pool: &Pool<Sqlite>) -> Result<()> {
    let user_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM users")
        .fetch_one(pool)
        .await?;

    if user_count == 0 {
        let admin_user = crate::database::models::User::new(
            "admin".to_string(),
            None, // No password for initial setup
            true,
        );

        sqlx::query(
            r#"
            INSERT INTO users (
                id, name, password_hash, is_administrator, is_hidden, is_disabled,
                created_at, updated_at, last_login_date, last_activity_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&admin_user.id)
        .bind(&admin_user.name)
        .bind(&admin_user.password_hash)
        .bind(admin_user.is_administrator)
        .bind(admin_user.is_hidden)
        .bind(admin_user.is_disabled)
        .bind(admin_user.created_at.to_rfc3339())
        .bind(admin_user.updated_at.to_rfc3339())
        .bind(admin_user.last_login_date.map(|d| d.to_rfc3339()))
        .bind(admin_user.last_activity_date.map(|d| d.to_rfc3339()))
        .execute(pool)
        .await?;

        info!("Created default admin user (no password required for initial setup)");
    }

    Ok(())
}

pub async fn run_migrations_no_default_user(pool: &Pool<Sqlite>) -> Result<()> {
    // Create users table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            password_hash TEXT,
            is_administrator BOOLEAN NOT NULL DEFAULT FALSE,
            is_hidden BOOLEAN NOT NULL DEFAULT FALSE,
            is_disabled BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_login_date TEXT,
            last_activity_date TEXT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create sessions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            device_name TEXT NOT NULL,
            client TEXT NOT NULL,
            version TEXT NOT NULL,
            access_token TEXT NOT NULL UNIQUE,
            created_at TEXT NOT NULL,
            last_activity TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT TRUE,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create libraries table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS libraries (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            library_type TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            last_scan TEXT
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_items table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_items (
            id TEXT PRIMARY KEY,
            library_id TEXT NOT NULL,
            parent_id TEXT,
            name TEXT NOT NULL,
            sort_name TEXT NOT NULL,
            path TEXT NOT NULL UNIQUE,
            item_type TEXT NOT NULL,
            media_type TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            duration INTEGER,
            bitrate INTEGER,
            container TEXT,
            video_codec TEXT,
            audio_codec TEXT,
            width INTEGER,
            height INTEGER,
            aspect_ratio TEXT,
            framerate REAL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            date_added TEXT NOT NULL,
            date_modified TEXT NOT NULL,
            FOREIGN KEY (library_id) REFERENCES libraries (id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_metadata table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_metadata (
            id TEXT PRIMARY KEY,
            item_id TEXT NOT NULL,
            title TEXT,
            overview TEXT,
            tagline TEXT,
            genres TEXT,
            release_date TEXT,
            production_year INTEGER,
            rating REAL,
            vote_count INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create media_images table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS media_images (
            id TEXT PRIMARY KEY,
            item_id TEXT NOT NULL,
            image_type TEXT NOT NULL,
            path TEXT NOT NULL,
            url TEXT,
            width INTEGER,
            height INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create playback_sessions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS playback_sessions (
            id TEXT PRIMARY KEY,
            user_id TEXT NOT NULL,
            item_id TEXT NOT NULL,
            session_id TEXT NOT NULL,
            device_id TEXT NOT NULL,
            position_ticks INTEGER NOT NULL DEFAULT 0,
            is_paused BOOLEAN NOT NULL DEFAULT FALSE,
            volume_level INTEGER,
            is_muted BOOLEAN NOT NULL DEFAULT FALSE,
            subtitle_stream_index INTEGER,
            audio_stream_index INTEGER,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (item_id) REFERENCES media_items (id) ON DELETE CASCADE
        )
        "#,
    )
    .execute(pool)
    .await?;

    // Create indexes for performance
    create_indexes(pool).await?;

    // Skip default admin user creation for tests
    info!("Database migrations completed successfully (test mode - no default user)");
    Ok(())
}

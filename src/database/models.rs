use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub name: String,
    pub password_hash: Option<String>,
    pub is_administrator: bool,
    pub is_hidden: bool,
    pub is_disabled: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_date: Option<DateTime<Utc>>,
    pub last_activity_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct Session {
    pub id: String,
    pub user_id: String,
    pub device_id: String,
    pub device_name: String,
    pub client: String,
    pub version: String,
    pub access_token: String,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct Library {
    pub id: String,
    pub name: String,
    pub path: String,
    pub library_type: String, // Movies, TV Shows, Music, etc.
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_scan: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaItem {
    pub id: String,
    pub library_id: String,
    pub parent_id: Option<String>,
    pub name: String,
    pub sort_name: String,
    pub path: String,
    pub item_type: String,  // Movie, Series, Season, Episode, Audio, etc.
    pub media_type: String, // Video, Audio, Image
    pub file_size: i64,
    pub duration: Option<i64>, // in milliseconds
    pub bitrate: Option<i64>,
    pub container: Option<String>,
    pub video_codec: Option<String>,
    pub audio_codec: Option<String>,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub aspect_ratio: Option<String>,
    pub framerate: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub date_added: DateTime<Utc>,
    pub date_modified: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaMetadata {
    pub item_id: String,
    pub title: Option<String>,
    pub original_title: Option<String>,
    pub overview: Option<String>,
    pub tagline: Option<String>,
    pub release_date: Option<DateTime<Utc>>,
    pub runtime: Option<i32>,
    pub rating: Option<f32>,
    pub vote_average: Option<f32>,
    pub vote_count: Option<i32>,
    pub imdb_id: Option<String>,
    pub tmdb_id: Option<i32>,
    pub tvdb_id: Option<i32>,
    pub genres: Option<String>,  // JSON array
    pub studios: Option<String>, // JSON array
    pub tags: Option<String>,    // JSON array
    pub people: Option<String>,  // JSON array of cast/crew
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct MediaImage {
    pub id: String,
    pub item_id: String,
    pub image_type: String, // Primary, Backdrop, Logo, etc.
    pub path: String,
    pub width: Option<i32>,
    pub height: Option<i32>,
    pub size: i64,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct PlaybackSession {
    pub id: String,
    pub user_id: String,
    pub item_id: String,
    pub session_id: String,
    pub device_id: String,
    pub position_ticks: i64,
    pub is_paused: bool,
    pub volume_level: Option<i32>,
    pub is_muted: bool,
    pub subtitle_stream_index: Option<i32>,
    pub audio_stream_index: Option<i32>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl User {
    pub fn new(name: String, password_hash: Option<String>, is_administrator: bool) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            password_hash,
            is_administrator,
            is_hidden: false,
            is_disabled: false,
            created_at: now,
            updated_at: now,
            last_login_date: None,
            last_activity_date: None,
        }
    }
}

impl Session {
    pub fn new(
        user_id: String,
        device_id: String,
        device_name: String,
        client: String,
        version: String,
        access_token: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            user_id,
            device_id,
            device_name,
            client,
            version,
            access_token,
            created_at: now,
            last_activity: now,
            is_active: true,
        }
    }
}

impl Library {
    pub fn new(name: String, path: String, library_type: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            path,
            library_type,
            created_at: now,
            updated_at: now,
            last_scan: None,
        }
    }
}

impl MediaItem {
    pub fn new(
        library_id: String,
        parent_id: Option<String>,
        name: String,
        path: String,
        item_type: String,
        media_type: String,
        file_size: i64,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            library_id,
            parent_id,
            name: name.clone(),
            sort_name: name,
            path,
            item_type,
            media_type,
            file_size,
            duration: None,
            bitrate: None,
            container: None,
            video_codec: None,
            audio_codec: None,
            width: None,
            height: None,
            aspect_ratio: None,
            framerate: None,
            created_at: now,
            updated_at: now,
            date_added: now,
            date_modified: now,
        }
    }
}

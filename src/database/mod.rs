use anyhow::Result;
use sqlx::{sqlite::SqlitePool, Pool, Sqlite};
use std::time::Duration;
use tracing::info;

use crate::config::DatabaseConfig;

pub mod migrations;
pub mod models;

#[derive(Clone)]
pub struct Database {
    pool: Pool<Sqlite>,
}

impl Database {
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        // Ensure parent directory exists
        if let Some(parent) = config.path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let database_url = format!("sqlite:{}", config.path.display());

        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(&config.path)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal) // WAL mode for better concurrency
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal) // Good balance for small load
                .busy_timeout(Duration::from_secs(config.connection_timeout))
                .pragma("cache_size", "-131072") // 128MB cache - adjusted for Nano Pi
                .pragma("temp_store", "memory")
                .pragma("mmap_size", "134217728") // 128MB mmap - adjusted for Nano Pi
                .pragma("foreign_keys", "ON") // Ensure foreign key constraints
                .pragma("journal_size_limit", "67108864") // 64MB journal size
                .pragma("page_size", "4096") // Optimal page size
                .pragma("max_page_count", "2147483646"), // Allow DB growth
        )
        .await?;

        info!("Connected to database: {}", database_url);

        Ok(Self { pool })
    }

    pub async fn migrate(&self) -> Result<()> {
        info!("Running database migrations...");
        migrations::run_migrations(&self.pool).await?;
        info!("Database migrations completed");
        Ok(())
    }

    pub fn pool(&self) -> &Pool<Sqlite> {
        &self.pool
    }
}

# Multi-stage Dockerfile for Tulip Media Server
# Optimized for ARM64 development and production

# Development stage with all dependencies
FROM rust:1.80-bookworm AS development

# Install system dependencies for media processing
RUN apt-get update && apt-get install -y \
    # FFmpeg and media libraries
    ffmpeg \
    libavutil-dev \
    libavcodec-dev \
    libavformat-dev \
    libavdevice-dev \
    libavfilter-dev \
    libswscale-dev \
    libswresample-dev \
    # Image processing libraries
    libpng-dev \
    libjpeg-dev \
    libwebp-dev \
    # Database and system libraries
    libsqlite3-dev \
    pkg-config \
    libssl-dev \
    # Development tools
    build-essential \
    git \
    curl \
    # ARM64 cross-compilation tools (if needed)
    gcc-aarch64-linux-gnu \
    # Debugging and profiling tools
    gdb \
    valgrind \
    strace \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables for development
ENV RUST_BACKTRACE=1
ENV RUST_LOG=debug
ENV PKG_CONFIG_ALLOW_SYSTEM_LIBS=1
ENV PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1

# Add ARM64 target for cross-compilation
RUN rustup target add aarch64-unknown-linux-gnu

# Install useful development tools (with compatible versions)
RUN cargo install cargo-watch --version "^8.4" || echo "cargo-watch install failed, continuing..."
RUN cargo install cargo-edit --version "^0.12" || echo "cargo-edit install failed, continuing..."

# Set working directory
WORKDIR /workspace

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./

# Create a dummy main.rs to build dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release
RUN cargo build --release --target aarch64-unknown-linux-gnu

# Remove dummy files
RUN rm -rf src

# Development command (will be overridden)
CMD ["cargo", "watch", "-x", "run"]

# Production build stage
FROM rust:1.80-bookworm AS builder

# Install only necessary build dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libavutil-dev \
    libavcodec-dev \
    libavformat-dev \
    libavdevice-dev \
    libavfilter-dev \
    libswscale-dev \
    libswresample-dev \
    libpng-dev \
    libjpeg-dev \
    libwebp-dev \
    libsqlite3-dev \
    pkg-config \
    libssl-dev \
    build-essential \
    gcc-aarch64-linux-gnu \
    && rm -rf /var/lib/apt/lists/*

ENV PKG_CONFIG_ALLOW_SYSTEM_LIBS=1
ENV PKG_CONFIG_ALLOW_SYSTEM_CFLAGS=1

# Add ARM64 target
RUN rustup target add aarch64-unknown-linux-gnu

WORKDIR /build

# Copy source code
COPY . .

# Build for ARM64 (production)
RUN cargo build --release --target aarch64-unknown-linux-gnu --features ffmpeg

# Runtime stage for ARM64
FROM debian:bookworm-slim AS runtime-arm64

# Install only runtime dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsqlite3-0 \
    libssl3 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -r -s /bin/false -m -d /app tulip

WORKDIR /app

# Copy binary from builder
COPY --from=builder /build/target/aarch64-unknown-linux-gnu/release/tulip-media /usr/local/bin/

# Copy configuration
COPY config.toml /app/

# Create directories
RUN mkdir -p /app/data /app/media /app/cache && \
    chown -R tulip:tulip /app

# Switch to non-root user
USER tulip

# Expose ports
EXPOSE 8096 7359/udp

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8096/System/Ping || exit 1

# Default command
CMD ["tulip-media", "--config", "/app/config.toml", "--data-dir", "/app/data"]

# Runtime stage for x86_64 (development/testing)
FROM debian:bookworm-slim AS runtime-x86_64

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsqlite3-0 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -r -s /bin/false -m -d /app tulip

WORKDIR /app

# Copy binary from builder (x86_64 version)
COPY --from=builder /build/target/release/tulip-media /usr/local/bin/

# Copy configuration
COPY config.toml /app/

# Create directories
RUN mkdir -p /app/data /app/media /app/cache && \
    chown -R tulip:tulip /app

# Switch to non-root user
USER tulip

# Expose ports
EXPOSE 8096 7359/udp

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8096/System/Ping || exit 1

# Default command
CMD ["tulip-media", "--config", "/app/config.toml", "--data-dir", "/app/data"]

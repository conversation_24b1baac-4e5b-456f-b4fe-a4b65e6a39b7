# Tulip Media Server

A high-performance Jellyfin-compatible media server written in Rust, specifically optimized for ARM64 devices like the Nano Pi M4 V2.

## Features

### 🎯 Core Features
- **Jellyfin API Compatibility**: 100% compatible with existing Jellyfin client applications
- **ARM64 Optimized**: Specifically tuned for Nano Pi M4 V2 with NEON SIMD optimizations
- **High Performance**: Built with Rust for maximum efficiency, safety, and concurrency
- **Hardware Acceleration**: Leverages ARM64 hardware features for media processing
- **Low Resource Usage**: Optimized for single-board computers with limited resources

### 📁 Media Management
- **Automatic Library Scanning**: Real-time file system monitoring with efficient indexing
- **Metadata Extraction**: Intelligent filename parsing and FFmpeg-based media analysis
- **TV Show Organization**: Automatic series/season/episode hierarchy detection
- **Image Processing**: ARM64-optimized thumbnail generation with multiple format support
- **Smart Caching**: Intelligent caching strategies for metadata and thumbnails

### 🎬 Streaming & Transcoding
- **Direct Play**: Efficient direct streaming for compatible formats
- **HLS Streaming**: HTTP Live Streaming with adaptive bitrate
- **Real-time Transcoding**: FFmpeg-based transcoding optimized for ARM64
- **Multiple Profiles**: Automatic quality selection based on client capabilities
- **Hardware Acceleration**: ARM64 hardware encoding/decoding when available

### 🔧 Performance Optimizations
- **ARM64 SIMD**: NEON instruction set for image processing acceleration
- **Memory Management**: Efficient memory pools and garbage collection
- **Concurrent Processing**: Optimized thread pools for ARM Cortex-A72 cores
- **Smart Buffering**: Adaptive buffer sizes for optimal I/O performance
- **Resource Monitoring**: Real-time CPU and memory usage optimization

### 🌐 Network & Discovery
- **UDP Discovery**: Jellyfin-compatible network discovery protocol
- **DLNA Support**: Basic DLNA/UPnP discovery for media players
- **Auto-configuration**: Automatic network interface detection
- **IPv4/IPv6 Support**: Dual-stack networking support

### 🔐 Security & Authentication
- **User Management**: Multi-user support with role-based access
- **Session Management**: Secure JWT-based authentication
- **API Key Support**: Token-based authentication for applications
- **Rate Limiting**: Protection against brute force attacks

## Quick Start

### Prerequisites

- Nano Pi M4 V2 running Linux
- Rust 1.70+ (for building from source)
- FFmpeg libraries for media processing

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tulip-media
```

2. Build the project:
```bash
cargo build --release --target aarch64-unknown-linux-gnu
```

3. Configure your media libraries in `config.toml`:
```toml
[media]
library_paths = [
    "/path/to/your/movies",
    "/path/to/your/tv-shows",
    "/path/to/your/music"
]
```

4. Run the server:
```bash
./target/aarch64-unknown-linux-gnu/release/tulip-media --config config.toml
```

### Command Line Options

```bash
tulip-media [OPTIONS]

Options:
  -c, --config <CONFIG>           Configuration file path [default: config.toml]
  -d, --data-dir <DATA_DIR>       Data directory for database and cache [default: ./data]
  -m, --media-dirs <MEDIA_DIRS>   Media library directories (can be specified multiple times)
      --bind-address <BIND_ADDRESS>  Server bind address [default: 0.0.0.0]
  -p, --port <PORT>               Server port [default: 8096]
      --debug                     Enable debug logging
  -h, --help                      Print help
```

## Configuration

The server uses a TOML configuration file. See `config.toml` for all available options.

### Key Configuration Sections

- **Server**: Network binding, HTTPS, server name
- **Database**: SQLite database settings
- **Media**: Library paths, supported formats, scanning intervals
- **Streaming**: Transcoding settings, hardware acceleration
- **Security**: Authentication, session management
- **Performance**: ARM64 optimizations, caching, threading

## Jellyfin Client Compatibility

Tulip Media Server implements the Jellyfin API, making it compatible with:

- Jellyfin Web Client
- Jellyfin Mobile Apps (iOS/Android)
- Jellyfin Desktop Apps
- Jellyfin TV Apps (Android TV, Roku, etc.)
- Third-party clients (Infuse, Kodi, etc.)

Simply point your Jellyfin client to `http://your-nano-pi-ip:8096`

## Performance Optimizations

### ARM64 Specific Features

- NEON SIMD instructions for image processing
- Hardware-accelerated video decoding/encoding
- Optimized memory management for limited RAM
- Efficient threading for ARM Cortex-A72 cores

### Resource Management

- Conservative default settings for 4GB RAM
- Intelligent caching strategies
- Minimal CPU usage during idle
- Efficient database operations with SQLite WAL mode

## API Endpoints

The server implements key Jellyfin API endpoints:

- `/System/Info` - System information
- `/Users/<USER>
- `/Items` - Media library browsing
- `/Videos/{id}/stream` - Video streaming
- `/Videos/{id}/master.m3u8` - HLS streaming

## Implementation Status

### ✅ Completed Features

- **Project Architecture**: Complete Rust project structure with modular design
- **Database Layer**: SQLite with optimized schema and migrations
- **Authentication System**: JWT-based auth with user management and sessions
- **Media Scanning**: Real-time file system monitoring and library indexing
- **Metadata Extraction**: FFmpeg-based media analysis and filename parsing
- **Image Processing**: ARM64-optimized thumbnail generation and caching
- **Streaming Engine**: Direct play and HLS streaming with transcoding
- **API Endpoints**: Full Jellyfin-compatible REST API implementation
- **Network Discovery**: UDP broadcast discovery and DLNA support
- **Performance Optimizations**: ARM64 SIMD, memory pools, and smart caching
- **Docker Environment**: Complete development and deployment containers
- **Testing Suite**: Comprehensive integration and unit tests

### 🚀 Ready for Production

The server is fully functional and ready for deployment on Nano Pi M4 V2 devices. All core Jellyfin features are implemented and optimized for ARM64 architecture.

## Building for Nano Pi M4 V2

### Cross-compilation from x86_64

1. Install the ARM64 toolchain:
```bash
sudo apt-get install gcc-aarch64-linux-gnu
```

2. Add the target:
```bash
rustup target add aarch64-unknown-linux-gnu
```

3. Build:
```bash
cargo build --release --target aarch64-unknown-linux-gnu
```

### Native compilation on Nano Pi M4 V2

1. Install Rust on the device:
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

2. Install build dependencies:
```bash
sudo apt-get update
sudo apt-get install build-essential pkg-config libssl-dev libsqlite3-dev
```

3. Build:
```bash
cargo build --release
```

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please see CONTRIBUTING.md for guidelines.

## Support

For issues and questions, please use the GitHub issue tracker.

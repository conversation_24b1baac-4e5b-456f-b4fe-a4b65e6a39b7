use anyhow::Result;
use std::path::PathBuf;

#[tokio::test]
async fn test_server_startup() -> Result<()> {
    // This test verifies that the server can start up successfully
    // In a real test environment, you would start the server and make HTTP requests

    // For now, just test that the configuration can be loaded
    let config = tulip_media::config::Config::default();
    assert_eq!(config.server.port, 8096);
    assert_eq!(config.server.server_name, "Tulip Media Server");

    Ok(())
}

#[tokio::test]
async fn test_media_scanning() -> Result<()> {
    // Test media scanning functionality
    // This would require setting up a test media directory

    // Create a temporary directory with test files
    let temp_dir = tempfile::tempdir()?;
    let test_file = temp_dir.path().join("test_movie.mp4");
    std::fs::write(&test_file, b"fake video content")?;

    // Test file detection
    assert!(tulip_media::utils::is_video_file(&test_file));

    Ok(())
}

#[tokio::test]
async fn test_database_operations() -> Result<()> {
    // Test database operations
    use tulip_media::config::DatabaseConfig;
    use tulip_media::database::{models::*, Database};

    let temp_dir = tempfile::tempdir()?;
    let db_path = temp_dir.path().join("test.db");

    let db_config = DatabaseConfig {
        path: db_path,
        max_connections: 5,
        connection_timeout: 30,
    };

    let database = Database::new(&db_config).await?;
    database.migrate().await?;

    // Test creating a library
    let library = Library::new(
        "Test Library".to_string(),
        "/test/path".to_string(),
        "Movies".to_string(),
    );

    sqlx::query(
        r#"
        INSERT INTO libraries (id, name, path, library_type, created_at, updated_at, last_scan)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        "#,
    )
    .bind(&library.id)
    .bind(&library.name)
    .bind(&library.path)
    .bind(&library.library_type)
    .bind(library.created_at.to_rfc3339())
    .bind(library.updated_at.to_rfc3339())
    .bind(library.last_scan.map(|d| d.to_rfc3339()))
    .execute(database.pool())
    .await?;

    // Verify the library was created
    let retrieved_library = sqlx::query_as::<_, Library>("SELECT * FROM libraries WHERE id = ?")
        .bind(&library.id)
        .fetch_one(database.pool())
        .await?;

    assert_eq!(retrieved_library.name, "Test Library");
    assert_eq!(retrieved_library.library_type, "Movies");

    Ok(())
}

#[tokio::test]
async fn test_image_processing() -> Result<()> {
    // Test image processing functionality
    use std::sync::Arc;
    use tulip_media::config::Config;
    use tulip_media::database::Database;
    use tulip_media::media::images::ImageProcessor;

    let temp_dir = tempfile::tempdir()?;
    let db_path = temp_dir.path().join("test.db");

    let mut config = Config::default();
    config.database.path = db_path;
    let config = Arc::new(config);

    let database = Database::new(&config.database).await?;
    database.migrate().await?;

    let _image_processor = ImageProcessor::new(config, database).await?;

    // Create a simple test image
    let test_image = image::RgbImage::new(100, 100);
    let test_image_path = temp_dir.path().join("test.jpg");
    test_image.save(&test_image_path)?;

    // This would test thumbnail generation in a real scenario
    // For now, just verify the processor was created successfully
    assert!(test_image_path.exists());

    Ok(())
}

#[tokio::test]
async fn test_transcoding_profiles() -> Result<()> {
    // Test transcoding profile generation
    use tulip_media::streaming::transcoding::TranscodingEngine;

    let profiles = TranscodingEngine::get_default_profiles();
    assert!(!profiles.is_empty());

    // Verify ARM64 optimized profile exists
    let arm64_profile = profiles.iter().find(|p| p.name == "ARM64_Optimized");
    assert!(arm64_profile.is_some());

    let profile = arm64_profile.unwrap();
    assert_eq!(profile.video_codec, "libx264");
    assert_eq!(profile.audio_codec, "aac");
    assert_eq!(profile.max_bitrate, 4_000_000);

    Ok(())
}

#[tokio::test]
async fn test_jellyfin_api_compatibility() -> Result<()> {
    // Test Jellyfin API response format
    use tulip_media::database::models::*;

    let media_item = MediaItem::new(
        "lib123".to_string(),
        None,
        "Test Movie".to_string(),
        "/path/to/movie.mp4".to_string(),
        "Movie".to_string(),
        "Video".to_string(),
        1024 * 1024 * 1024, // 1GB
    );

    // Test that the item has required Jellyfin fields
    assert!(!media_item.id.is_empty());
    assert_eq!(media_item.name, "Test Movie");
    assert_eq!(media_item.item_type, "Movie");
    assert_eq!(media_item.media_type, "Video");

    Ok(())
}

#[tokio::test]
async fn test_performance_optimizations() -> Result<()> {
    // Test ARM64 performance optimizations
    use tulip_media::utils::performance::PerformanceOptimizer;

    let optimizer = PerformanceOptimizer::new(256); // 256MB cache

    // Test thread count optimization for ARM64
    let thread_count = optimizer.get_optimal_thread_count();
    assert!(thread_count <= 4); // Conservative for ARM64

    // Test buffer size optimization
    let buffer_size = optimizer.get_optimal_buffer_size();
    if cfg!(target_arch = "aarch64") {
        assert_eq!(buffer_size, 64 * 1024); // 64KB for ARM64
    } else {
        assert_eq!(buffer_size, 32 * 1024); // 32KB for other architectures
    }

    Ok(())
}

#[tokio::test]
async fn test_discovery_service() -> Result<()> {
    // Test network discovery functionality
    use std::sync::Arc;
    use tulip_media::config::Config;
    use tulip_media::discovery::DiscoveryService;

    let config = Arc::new(Config::default());
    let _discovery = DiscoveryService::new(config)?;

    // In a real test, you would start the discovery service and test UDP responses
    // For now, just verify it can be created

    Ok(())
}

#[tokio::test]
async fn test_metadata_extraction() -> Result<()> {
    // Test metadata extraction from filenames
    use std::path::Path;
    use tulip_media::media::metadata::MetadataExtractor;

    let extractor = MetadataExtractor::new();

    // Test filename parsing
    let test_path = Path::new("/movies/The Matrix (1999).mp4");
    let metadata = extractor.extract_metadata_from_filename(test_path).await;

    assert_eq!(metadata.title, Some("The Matrix".to_string()));

    // Test TV show detection
    let tv_path = Path::new("/tv/Breaking Bad/Season 01/Breaking Bad - S01E01 - Pilot.mkv");
    let tv_info = extractor.extract_tv_show_info(tv_path).await;

    if let Some(info) = tv_info {
        assert_eq!(info.show_name, "Breaking Bad");
        assert_eq!(info.season_number, 1);
        assert_eq!(info.episode_number, 1);
    }

    Ok(())
}

#[tokio::test]
async fn test_memory_management() -> Result<()> {
    // Test memory pool functionality
    use tulip_media::utils::performance::MemoryPool;

    let pool: MemoryPool<u8> = MemoryPool::new(10);

    // Get a buffer from the pool
    let buffer = pool.get(1024);
    assert_eq!(buffer.len(), 1024);

    // Return the buffer to the pool
    pool.return_buffer(buffer);

    // Get another buffer (should reuse the returned one)
    let buffer2 = pool.get(512);
    assert_eq!(buffer2.len(), 512);

    Ok(())
}

// Helper function to create test configuration (currently unused but kept for future tests)
#[allow(dead_code)]
fn _create_test_config() -> tulip_media::config::Config {
    let mut config = tulip_media::config::Config::default();
    config.server.port = 0; // Use random port for testing
    config.media.library_paths = vec![PathBuf::from("/tmp/test-media")];
    config
}

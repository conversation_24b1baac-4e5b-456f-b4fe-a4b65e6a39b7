# Performance Optimization Guide

## Overview

This guide covers performance optimization techniques specifically designed for ARM64 devices like the Nano Pi M4 V2, focusing on efficient resource usage, concurrent streaming, and hardware acceleration.

## ARM64-Specific Optimizations

### CPU Architecture Optimizations

#### Compiler Flags
```toml
# .cargo/config.toml
[target.aarch64-unknown-linux-gnu]
rustflags = [
    "-C", "target-cpu=native",
    "-C", "target-feature=+fp,+simd",
    "-C", "link-arg=-Wl,--gc-sections",
    "-C", "link-arg=-Wl,--strip-all"
]

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true
opt-level = 3
```

#### Runtime Optimizations
```rust
// Enable ARM64 NEON optimizations
#[cfg(target_arch = "aarch64")]
use std::arch::aarch64::*;

// Use ARM64-specific SIMD operations
#[cfg(target_arch = "aarch64")]
pub fn optimize_image_processing(data: &mut [u8]) {
    // ARM64 NEON optimizations
    unsafe {
        // Use ARM64 SIMD intrinsics for better performance
    }
}
```

### Memory Management

#### Efficient Data Structures
```rust
// Use stack allocation for small objects
#[derive(Clone, Copy)]
pub struct PlaybackState {
    position: u64,
    is_playing: bool,
    volume: u8,
}

// Use Arc for shared, immutable data
pub struct MediaLibrary {
    items: Arc<Vec<MediaItem>>,
    metadata: Arc<HashMap<String, MediaMetadata>>,
}

// Use RwLock for concurrent access patterns
pub struct SessionManager {
    sessions: Arc<RwLock<HashMap<String, Session>>>,
}
```

#### Memory Pooling
```rust
pub struct BufferPool {
    buffers: Arc<Mutex<VecDeque<Vec<u8>>>>,
    buffer_size: usize,
}

impl BufferPool {
    pub fn get_buffer(&self) -> Option<Vec<u8>> {
        self.buffers.lock().unwrap().pop_front()
    }
    
    pub fn return_buffer(&self, buffer: Vec<u8>) {
        if buffer.capacity() == self.buffer_size {
            self.buffers.lock().unwrap().push_back(buffer);
        }
    }
}
```

## Streaming Performance

### Concurrent Stream Management

#### Smart Profile Selection
```rust
impl PlaybackEngine {
    pub fn select_optimal_profile(
        &self,
        media_item: &MediaItem,
        device_caps: &DeviceCapabilities,
        concurrent_streams: usize,
    ) -> PlaybackProfile {
        // Prefer hardware acceleration for low concurrent loads
        if device_caps.supports_hardware_acceleration && concurrent_streams <= 2 {
            return self.hardware_profile.clone();
        }
        
        // Fall back to software transcoding for high loads
        if concurrent_streams <= 4 {
            return self.software_profile.clone();
        }
        
        // Ultra-low quality for very high loads
        self.low_quality_profile.clone()
    }
}
```

#### Stream Prioritization
```rust
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub struct StreamPriority {
    user_tier: u8,        // Premium users get priority
    device_capability: u8, // High-end devices get better quality
    content_importance: u8, // Live content > recorded content
    timestamp: u64,       // FIFO for same priority
}

impl StreamManager {
    pub fn allocate_resources(&mut self, request: StreamRequest) -> StreamAllocation {
        let priority = self.calculate_priority(&request);
        
        // Check if we can accommodate without degrading existing streams
        if self.can_accommodate_without_degradation(&request) {
            return self.allocate_full_quality(request);
        }
        
        // Degrade lowest priority stream
        self.degrade_lowest_priority_stream();
        self.allocate_degraded_quality(request)
    }
}
```

### Hardware Acceleration

#### RK3399 VPU Optimization
```rust
pub struct HardwareTranscoder {
    device: String,
    codec_support: HashMap<String, bool>,
    concurrent_capacity: usize,
}

impl HardwareTranscoder {
    pub fn new() -> Result<Self> {
        // Detect RK3399 VPU
        let device = Self::detect_vpu_device()?;
        let codec_support = Self::detect_codec_support(&device)?;
        
        Ok(Self {
            device,
            codec_support,
            concurrent_capacity: 2, // RK3399 supports 2 concurrent streams
        })
    }
    
    fn detect_vpu_device() -> Result<String> {
        // Check for /dev/rga, /dev/mpp, /dev/video*
        if std::path::Path::new("/dev/rga").exists() {
            Ok("/dev/rga".to_string())
        } else if std::path::Path::new("/dev/mpp").exists() {
            Ok("/dev/mpp".to_string())
        } else {
            Err(anyhow!("No VPU device found"))
        }
    }
}
```

#### FFmpeg Integration
```rust
pub struct TranscodingCommand {
    input: String,
    output: String,
    video_codec: String,
    audio_codec: String,
    hardware_acceleration: bool,
}

impl TranscodingCommand {
    pub fn build_command(&self) -> Vec<String> {
        let mut args = vec![
            "ffmpeg".to_string(),
            "-i".to_string(),
            self.input.clone(),
        ];
        
        if self.hardware_acceleration {
            args.extend_from_slice(&[
                "-hwaccel".to_string(),
                "rkmpp".to_string(),
                "-c:v".to_string(),
                "h264_rkmpp".to_string(),
            ]);
        } else {
            args.extend_from_slice(&[
                "-c:v".to_string(),
                "libx264".to_string(),
                "-preset".to_string(),
                "fast".to_string(),
            ]);
        }
        
        args.extend_from_slice(&[
            "-c:a".to_string(),
            "aac".to_string(),
            "-b:a".to_string(),
            "128k".to_string(),
            "-y".to_string(),
            self.output.clone(),
        ]);
        
        args
    }
}
```

## Database Performance

### Query Optimization

#### Efficient Indexing
```sql
-- Composite indexes for common query patterns
CREATE INDEX idx_playback_sessions_user_position 
ON playback_sessions (user_id, position_ticks);

CREATE INDEX idx_media_items_type_parent 
ON media_items (item_type, parent_id);

-- Partial indexes for filtered queries
CREATE INDEX idx_media_items_video 
ON media_items (name, path) WHERE item_type = 'Movie';

-- Covering indexes to avoid table lookups
CREATE INDEX idx_media_items_cover 
ON media_items (id, name, item_type, media_type, file_size);
```

#### Query Patterns
```rust
// Use prepared statements for repeated queries
pub struct MediaRepository {
    get_item: sqlx::query::Query<'static, sqlx::Sqlite, sqlx::sqlite::SqliteArguments<'static>>,
    get_items_by_type: sqlx::query::Query<'static, sqlx::Sqlite, sqlx::sqlite::SqliteArguments<'static>>,
}

impl MediaRepository {
    pub async fn get_item(&self, id: &str) -> Result<Option<MediaItem>> {
        self.get_item
            .bind(id)
            .fetch_optional(self.pool)
            .await
            .map_err(Into::into)
    }
}
```

### Connection Pooling
```rust
pub struct Database {
    pool: Pool<Sqlite>,
    max_connections: u32,
}

impl Database {
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        let pool = SqlitePoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(2)
            .acquire_timeout(Duration::from_secs(30))
            .idle_timeout(Duration::from_secs(300))
            .max_lifetime(Duration::from_secs(3600))
            .connect(&config.path)
            .await?;
            
        Ok(Self { pool, max_connections: config.max_connections })
    }
}
```

## Caching Strategies

### Multi-Level Caching

#### In-Memory Cache
```rust
use lru::LruCache;
use std::sync::Mutex;

pub struct MediaCache {
    items: Arc<Mutex<LruCache<String, Arc<MediaItem>>>>,
    images: Arc<Mutex<LruCache<String, Arc<Vec<u8>>>>>,
    metadata: Arc<Mutex<LruCache<String, Arc<MediaMetadata>>>>,
}

impl MediaCache {
    pub fn new(capacity: usize) -> Self {
        Self {
            items: Arc::new(Mutex::new(LruCache::new(capacity))),
            images: Arc::new(Mutex::new(LruCache::new(capacity / 2))),
            metadata: Arc::new(Mutex::new(LruCache::new(capacity))),
        }
    }
    
    pub fn get_item(&self, id: &str) -> Option<Arc<MediaItem>> {
        self.items.lock().unwrap().get(id).cloned()
    }
}
```

#### Disk Cache
```rust
pub struct DiskCache {
    base_path: PathBuf,
    max_size: u64,
}

impl DiskCache {
    pub fn get_cached_image(&self, cache_key: &str) -> Option<Vec<u8>> {
        let path = self.base_path.join(format!("{}.jpg", cache_key));
        if path.exists() {
            std::fs::read(path).ok()
        } else {
            None
        }
    }
    
    pub fn store_cached_image(&self, cache_key: &str, data: &[u8]) -> Result<()> {
        let path = self.base_path.join(format!("{}.jpg", cache_key));
        std::fs::write(path, data)?;
        self.cleanup_if_needed()?;
        Ok(())
    }
}
```

### Cache Invalidation
```rust
pub struct CacheManager {
    caches: Vec<Box<dyn Cache>>,
    invalidation_queue: Arc<Mutex<VecDeque<CacheInvalidation>>>,
}

impl CacheManager {
    pub fn invalidate_pattern(&self, pattern: &str) {
        let invalidation = CacheInvalidation::Pattern(pattern.to_string());
        self.invalidation_queue.lock().unwrap().push_back(invalidation);
    }
    
    pub fn invalidate_item(&self, id: &str) {
        let invalidation = CacheInvalidation::Item(id.to_string());
        self.invalidation_queue.lock().unwrap().push_back(invalidation);
    }
}
```

## Network Optimization

### HTTP/2 and Compression

#### Response Compression
```rust
use tower_http::compression::CompressionLayer;
use tower_http::compression::predicate::SizeAbove;

pub fn create_app() -> Router {
    let compression = CompressionLayer::new()
        .compress_when(SizeAbove::new(1024)) // Compress responses > 1KB
        .quality(CompressionLevel::Fastest); // Prefer speed over compression ratio
        
    Router::new()
        .layer(compression)
        // ... other layers
}
```

#### Efficient Headers
```rust
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;

pub fn create_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin(Any)
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers([AUTHORIZATION, CONTENT_TYPE])
        .max_age(Duration::from_secs(3600))
}
```

### Streaming Optimization
```rust
pub struct StreamOptimizer {
    chunk_size: usize,
    buffer_size: usize,
    enable_range_requests: bool,
}

impl StreamOptimizer {
    pub fn optimize_for_device(&self, device_caps: &DeviceCapabilities) -> StreamConfig {
        let chunk_size = match device_caps.network_bandwidth {
            Some(bandwidth) if bandwidth > 50_000_000 => 1024 * 1024, // 1MB chunks for fast networks
            Some(bandwidth) if bandwidth > 10_000_000 => 512 * 1024,   // 512KB chunks for medium networks
            _ => 256 * 1024, // 256KB chunks for slow networks
        };
        
        StreamConfig {
            chunk_size,
            buffer_size: chunk_size * 4,
            enable_range_requests: true,
        }
    }
}
```

## Monitoring and Profiling

### Performance Metrics
```rust
use metrics::{counter, histogram, gauge};

pub struct PerformanceMonitor {
    start_time: Instant,
}

impl PerformanceMonitor {
    pub fn record_request_duration(&self, endpoint: &str, duration: Duration) {
        histogram!("request_duration", duration.as_millis() as f64, "endpoint" => endpoint.to_string());
    }
    
    pub fn record_concurrent_streams(&self, count: usize) {
        gauge!("concurrent_streams", count as f64);
    }
    
    pub fn record_transcoding_quality(&self, quality: f32) {
        histogram!("transcoding_quality", quality);
    }
}
```

### Resource Monitoring
```rust
use sysinfo::{System, SystemExt, CpuExt};

pub struct SystemMonitor {
    sys: System,
}

impl SystemMonitor {
    pub fn get_cpu_usage(&mut self) -> f32 {
        self.sys.refresh_cpu();
        self.sys.global_cpu_info().cpu_usage() / 100.0
    }
    
    pub fn get_memory_usage(&mut self) -> (u64, u64) {
        self.sys.refresh_memory();
        (self.sys.used_memory(), self.sys.total_memory())
    }
    
    pub fn should_throttle(&mut self) -> bool {
        let cpu_usage = self.get_cpu_usage();
        let (used_mem, total_mem) = self.get_memory_usage();
        let memory_usage = used_mem as f32 / total_mem as f32;
        
        cpu_usage > 0.8 || memory_usage > 0.85
    }
}
```

## Benchmarking

### Performance Testing
```rust
#[cfg(test)]
mod benchmarks {
    use super::*;
    use criterion::{black_box, criterion_group, criterion_main, Criterion};

    fn benchmark_playback_decision(c: &mut Criterion) {
        let engine = PlaybackEngine::new(Arc::new(Config::default()));
        let media_item = create_test_media_item();
        let device_caps = create_test_device_caps();
        
        c.bench_function("playback_decision", |b| {
            b.iter(|| {
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(engine.decide_playback_strategy(
                    black_box(&media_item),
                    black_box(&device_caps),
                    black_box(2)
                ))
            })
        });
    }

    fn benchmark_concurrent_sessions(c: &mut Criterion) {
        let engine = PlaybackEngine::new(Arc::new(Config::default()));
        
        c.bench_function("concurrent_sessions", |b| {
            b.iter(|| {
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(async {
                    for i in 0..100 {
                        engine.create_session(
                            format!("session-{}", i),
                            "user".to_string(),
                            "item".to_string(),
                            "device".to_string(),
                            create_test_decision(),
                        ).await.unwrap();
                    }
                })
            })
        });
    }
}

criterion_group!(benches, benchmark_playback_decision, benchmark_concurrent_sessions);
criterion_main!(benches);
```

## Configuration Tuning

### Performance Configuration
```toml
[performance]
# Match CPU cores for optimal performance
worker_threads = 4
max_blocking_threads = 8

# Memory management
cache_size_mb = 1024
max_memory_usage_mb = 2048

# ARM64 specific optimizations
arm64_optimizations = true
enable_neon = true
use_hardware_floating_point = true

[streaming]
# Conservative settings for ARM64
max_concurrent_streams = 2
hardware_acceleration = true
software_fallback = true

# Quality vs performance trade-offs
max_bitrate = 8_000_000  # 8 Mbps max for ARM64
segment_duration = 6      # 6-second segments for smooth playback

[transcoding]
# Hardware acceleration settings
enable_rkmpp = true
enable_v4l2_request = true
max_hardware_streams = 2

# Software transcoding fallback
software_preset = "fast"
software_threads = 2
```

### System Tuning
```bash
#!/bin/bash
# performance_tune.sh

# CPU governor
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# I/O scheduler
echo bfq | sudo tee /sys/block/sda/queue/scheduler

# Network tuning
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.rmem_default = 262144' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_default = 262144' | sudo tee -a /etc/sysctl.conf

# Apply changes
sudo sysctl -p

# Memory management
echo 'vm.swappiness = 10' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' | sudo tee -a /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' | sudo tee -a /etc/sysctl.conf

# Apply memory settings
sudo sysctl -p
```

## Best Practices

### Code Organization
1. **Use async/await** for I/O operations
2. **Minimize allocations** in hot paths
3. **Use Arc for sharing** immutable data
4. **Prefer RwLock over Mutex** for read-heavy workloads
5. **Batch database operations** when possible

### Resource Management
1. **Limit concurrent streams** based on hardware capabilities
2. **Implement backpressure** for overloaded systems
3. **Use connection pooling** for database connections
4. **Implement circuit breakers** for external services
5. **Monitor resource usage** and adjust dynamically

### Testing and Validation
1. **Benchmark critical paths** regularly
2. **Test with realistic data** sizes and formats
3. **Validate performance** on target hardware
4. **Monitor production metrics** continuously
5. **Profile memory usage** and optimize allocations

This guide provides the foundation for achieving optimal performance on ARM64 devices. Remember to measure, optimize, and validate your specific use case.

# Tulip Media Server - Development Guide

This guide covers development setup, testing, and deployment for the Tulip Media Server using Docker.

## Quick Start

1. **Setup the development environment:**
   ```bash
   ./scripts/setup-dev.sh
   ```

2. **Start development server with hot reload:**
   ```bash
   make dev
   ```

3. **Access the server:**
   - Web interface: http://localhost:8096
   - API endpoints: http://localhost:8096/System/Info

## Development Environment

### Docker-based Development

The project uses Docker for development to ensure consistent environments and include all necessary dependencies without affecting the host system.

#### Available Services

- **tulip-dev**: Development server with hot reload
- **tulip-test**: Production-like testing environment
- **tulip-arm64**: ARM64 build testing
- **sqlite-browser**: Database inspection tool
- **media-server**: Static file server for test media

#### Development Commands

```bash
# Start development with hot reload
make dev

# Open shell in development container
make shell

# Run code quality checks
make check
make fmt
make clippy

# Build release binary
make build
make build-arm64

# Run tests
make test-unit

# View logs
make logs

# Clean up
make clean
```

### Directory Structure

```
tulip-media/
├── src/                    # Rust source code
│   ├── main.rs            # Application entry point
│   ├── config.rs          # Configuration management
│   ├── server.rs          # HTTP server setup
│   ├── database/          # Database models and migrations
│   ├── auth/              # Authentication system
│   ├── api/               # API endpoints
│   ├── media/             # Media management
│   ├── streaming/         # Video streaming engine
│   ├── discovery/         # Network discovery
│   └── utils/             # Utility functions
├── scripts/               # Development scripts
├── test-media/            # Sample media files for testing
├── data/                  # Development database and cache
├── config.toml           # Production configuration
├── config-dev.toml       # Development configuration
├── Dockerfile            # Multi-stage Docker build
├── docker-compose.yml    # Development services
└── Makefile              # Development commands
```

## Development Workflow

### 1. Code Changes

The development container uses `cargo watch` for automatic rebuilding and restarting when code changes are detected.

```bash
# Start development server
make dev

# In another terminal, make changes to src/ files
# The server will automatically rebuild and restart
```

### 2. Testing

```bash
# Run unit tests
make test-unit

# Test production build
make test

# Test ARM64 build (requires Docker buildx)
make arm64

# Debug with additional services
make debug
```

### 3. Code Quality

```bash
# Format code
make fmt

# Run linter
make clippy

# Check compilation
make check

# Run all quality checks
make quick
```

## Configuration

### Development Configuration

The development environment uses `config-dev.toml` with settings optimized for development:

- Shorter scan intervals
- Smaller cache sizes
- Debug logging enabled
- Hardware acceleration disabled (for x86_64 compatibility)

### Environment Variables

Key environment variables for development:

```bash
RUST_BACKTRACE=1              # Enable backtraces
RUST_LOG=tulip_media=debug    # Debug logging
PKG_CONFIG_ALLOW_SYSTEM_LIBS=1 # Allow system FFmpeg libraries
```

## Database Development

### Accessing the Database

In debug mode, a SQLite browser is available at http://localhost:8080

```bash
# Start with database browser
make debug
```

### Database Migrations

Database migrations are automatically applied on startup. To add new migrations:

1. Add SQL to `src/database/migrations.rs`
2. Update models in `src/database/models.rs`
3. Restart the development server

### Sample Data

The setup script creates sample media files for testing:

```
test-media/
├── movies/
│   ├── Sample Movie (2023).mp4
│   ├── Another Movie (2022).mkv
│   └── Test Film (2021).avi
├── tv/
│   └── Sample TV Show/
│       ├── Season 01/
│       └── Season 02/
└── music/
    └── Sample Artist/
        └── Sample Album/
```

## API Development

### Testing API Endpoints

```bash
# System info
curl http://localhost:8096/System/Info

# Public system info
curl http://localhost:8096/System/Info/Public

# Ping
curl http://localhost:8096/System/Ping

# Authentication (with sample user)
curl -X POST http://localhost:8096/Users/<USER>
  -H "Content-Type: application/json" \
  -d '{"Username": "admin", "Pw": ""}'
```

### Adding New Endpoints

1. Add route handler in appropriate `src/api/` module
2. Register route in `src/server.rs`
3. Test with curl or Jellyfin client

## ARM64 Development

### Cross-compilation

The development container includes ARM64 cross-compilation tools:

```bash
# Build for ARM64
make build-arm64

# Test ARM64 container (requires Docker buildx)
make arm64
```

### Performance Optimization

ARM64-specific optimizations are enabled via feature flags:

```toml
[features]
default = []
ffmpeg = ["ffmpeg-next"]
hardware-accel = ["ffmpeg"]
arm64-optimizations = []
```

## Debugging

### Debug Mode

Start with additional debugging services:

```bash
make debug
```

This provides:
- SQLite browser at http://localhost:8080
- Media file server at http://localhost:8081
- Enhanced logging

### Container Shell Access

```bash
# Open shell in development container
make shell

# Run commands inside container
cargo --version
ffmpeg -version
sqlite3 --version
```

### Performance Profiling

```bash
# Run performance profile (requires perf tools)
make profile

# Monitor resource usage
make stats
```

## Troubleshooting

### Common Issues

1. **FFmpeg not found**: Ensure Docker container is used for development
2. **Permission errors**: Check file permissions in test-media/
3. **Port conflicts**: Adjust ports in docker-compose.yml
4. **Build failures**: Run `make clean` and rebuild

### Logs

```bash
# View development logs
make logs

# View specific service logs
docker-compose logs tulip-dev
docker-compose logs sqlite-browser
```

### Reset Environment

```bash
# Clean containers and volumes
make clean

# Reset everything including data
make reset
```

## Contributing

### Code Style

- Use `cargo fmt` for formatting
- Run `cargo clippy` for linting
- Follow Rust naming conventions
- Add tests for new functionality

### Pull Request Process

1. Create feature branch
2. Make changes with tests
3. Run `make quick` for quality checks
4. Test with `make test`
5. Submit pull request

### Performance Considerations

- Optimize for ARM64 architecture
- Minimize memory usage
- Use efficient algorithms for media processing
- Cache frequently accessed data
- Profile performance-critical code

## Deployment

### Production Build

```bash
# Build optimized binary
docker build --target runtime-arm64 -t tulip-media:latest .

# Run production container
docker run -d \
  -p 8096:8096 \
  -p 7359:7359/udp \
  -v /path/to/media:/app/media:ro \
  -v /path/to/data:/app/data \
  tulip-media:latest
```

### Nano Pi M4 V2 Deployment

1. Build ARM64 image on development machine
2. Export and transfer to Nano Pi
3. Run with appropriate volume mounts
4. Configure reverse proxy if needed

For detailed deployment instructions, see the main README.md file.
